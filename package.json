{"name": "com.kamerounsport", "version": "0.0.1", "private": true, "scripts": {"android": "ENVFILE=./configuration/.env.dev && npx react-native run-android", "ios": "npx react-native run-ios", "lint": "eslint .", "alog": "npx react-native log-android", "start": "ENVFILE=./configuration/.env.dev && npx react-native start --reset-cache", "watch": "watchman watch src --foreground --logfile=/dev/stdout", "test": "jest", "gc": "cd android && ./gradlew clean && cd ..", "android:bundle": "npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle", "ios:inmemory": "ENVFILE=./configuration/.env.inmemory react-native run-ios", "ios:dev": "ENVFILE=./configuration/.env.dev npx react-native run-ios", "ios:staging": "ENVFILE=./configuration/.env.staging react-native run-ios", "ios:prod": "ENVFILE=./configuration/.env.prod react-native run-ios", "android:inmemory": "ENVFILE=./configuration/.env.inmemory react-native run-android", "android:dev": "ENVFILE=./configuration/.env.dev && npx react-native run-android", "android:staging": "ENVFILE=./configuration/.env.staging react-native run-android", "android:prod": "ENVFILE=./configuration/.env.prod react-native run-android", "android:apk:prod": "yarn android:bundle && cd android && ENVFILE=./configuration/.env.prod ./gradlew assembleRelease", "android:apk:staging": "yarn android:bundle && cd android && ENVFILE=./configuration/.env.staging ./gradlew assembleRelease", "android:apk:dev": "yarn android:bundle && cd android && ENVFILE=./.env ./gradlew assembleRelease", "android:apk:bundle:dev": "yarn android:bundle && cd android && ENVFILE=./configuration/.env.dev ./gradlew bundle", "build:ios:assets": "npx react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios"}, "dependencies": {"@arneson/react-native-rich-text-editor": "^1.2.2", "@notifee/react-native": "^7.8.0", "@react-native-community/async-storage": "^1.12.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "8.4.4", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/slider": "^4.4.3", "@react-native-firebase/app": "^18.8.0", "@react-native-firebase/database": "^18.8.0", "@react-native-firebase/messaging": "^18.8.0", "@react-native-firebase/storage": "^18.8.0", "@react-native-picker/picker": "^2.4.10", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@reduxjs/toolkit": "^1.9.5", "@tanstack/react-query": "^5.28.6", "@tootsweet/react-native-gps-state": "https://github.com/huy-lv/react-native-gps-state.git#master", "axios": "^1.6.7", "date-fns": "^2.30.0", "deprecated-react-native-prop-types": "^4.2.1", "expo-checkbox": "^3.0.0", "jetifier": "^2.0.0", "lottie-react-native": "^6.4.0", "pure-md5": "^0.1.14", "react": "18.2.0", "react-native": "0.72.4", "react-native-background-actions": "^3.0.0", "react-native-camera": "^4.2.1", "react-native-collapsible": "^1.6.1", "react-native-config": "^1.5.0", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "^4.3.3", "react-native-datepicker": "git+https://github.com/Luckygirlllll/react-native-datepicker", "react-native-device-info": "^10.8.0", "react-native-document-picker": "^9.3.1", "react-native-event-listeners": "^1.0.7", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-get-location": "^4.0.1", "react-native-gifted-chat": "2.4.0", "react-native-htmlview": "^0.16.0", "react-native-image-crop-picker": "^0.41.6", "react-native-image-picker": "^7.2.3", "react-native-image-resizer": "^1.4.5", "react-native-image-zoom-viewer": "^3.0.1", "react-native-interactable": "^2.0.1", "react-native-iphone-x-helper": "^1.3.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "https://github.com/Sunbreak/react-native-linear-gradient.git#fabric", "react-native-localize": "^3.0.2", "react-native-maps": "1.20.1", "react-native-onboarding-swiper": "^1.3.0", "react-native-permissions": "^4.1.5", "react-native-phone-input": "^1.3.6", "react-native-restart": "^0.0.27", "react-native-root-siblings": "^4.1.1", "react-native-safe-area-context": "5.0.0", "react-native-screens": "^3.20.0", "react-native-smooth-pincode-input": "^1.0.9", "react-native-splash-screen": "^3.3.0", "react-native-sqlite-2": "^3.6.2", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^13.10.0", "react-native-vector-icons": "^9.2.0", "react-native-webview": "^12.2.0", "react-redux": "^8.0.7", "redux": "4.0.0", "redux-logger": "^3.0.6", "redux-observable": "1.0.0", "rn-fetch-blob": "^0.10.15", "rxjs": "6.2.2", "rxjs-compat": "^6.2.2", "schedule": "0.4.0", "unsplash-js": "^6.0.0", "uuid": "^3.3.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tanstack/eslint-plugin-query": "^5.28.6", "@tsconfig/react-native": "^3.0.0", "@types/jest": "^29.2.1", "@types/lodash.clonedeep": "^4.5.7", "@types/react": "^18.0.24", "@types/react-native-vector-icons": "^6.4.13", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}