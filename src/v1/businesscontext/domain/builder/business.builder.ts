import { BusinessOpeningHoursType } from '../../adapters/primaries/edition/business/businessEdition.types';
import { Business } from '../entities/business';
import { BusinessAddress } from '../entities/businessAddress';
import { BusinessContact } from '../entities/businessContact';
import { BusinessOpeningHours } from '../entities/businessOpeningHour';
import { BusinessSubscription } from '../entities/businessSubscription';

export class BusinessBuilder {
    protected _id: string
    protected _businessId: string

    protected _name: string
    protected _type: string
    protected _activity: string
    protected _membership: 'freemium' | 'premium'
    protected _active: boolean

    protected _address: string
    protected _zipcode: string
    protected _city: string
    protected _country: string
    protected _longitude: number
    protected _latitude: number

    protected _siren: string
    protected _isVerified: boolean

    protected _coverImage: string
    protected _profileImage: string

    protected _openingHours: BusinessOpeningHours[] = []

    protected _description: string
    protected _website: string
    protected _email: string
    protected _phoneNumber: string

    protected _plan: string
    protected _status: string
    protected _startDate: string
    protected _endDate: string
    protected _isSubscriptionActive: boolean
    protected _trialEndDate: Date

    withId(value: string): BusinessBuilder {
        this._id = value
        return this
    }

    withName(value: string): BusinessBuilder {
        this._name = value
        return this
    }

    withType(value: string): BusinessBuilder {
        this._type = value
        return this
    }

    withActivity(value: string): BusinessBuilder {
        this._activity = value
        return this
    }

    withMembership(value: 'freemium' | 'premium'): BusinessBuilder {
        this._membership = value
        return this
    }

    withAddress(value: string): BusinessBuilder {
        this._address = value
        return this
    }

    withZipCode(value: string): BusinessBuilder {
        this._zipcode = value
        return this
    }

    withCity(value: string): BusinessBuilder {
        this._city = value
        return this
    }
    withActive(value: boolean): BusinessBuilder {
        this._active = value
        return this
    }

    withCountry(value: string): BusinessBuilder {
        this._country = value
        return this
    }

    withLongitude(value: number): BusinessBuilder {
        this._longitude = value
        return this
    }

    withLatitude(value: number): BusinessBuilder {
        this._latitude = value
        return this
    }

    withSiren(value: string): BusinessBuilder {
        this._siren = value
        return this
    }

    withIsVerified(value: boolean): BusinessBuilder {
        this._isVerified = value
        return this
    }

    withCoverImage(value: string): BusinessBuilder {
        this._coverImage = value
        return this
    }

    withProfileImage(value: string): BusinessBuilder {
        this._profileImage = value
        return this
    }

    withOpeningHours(value: BusinessOpeningHoursType[]): BusinessBuilder {
        value.map(item => {
            this._openingHours.push(
                new BusinessOpeningHours(
                    item.id,
                    item.openingDays.startTime,
                    item.openingDays.endTime,
                    item.openingDays.key,
                    item.isPaused
                )
            )
        })
        return this
    }

    withDescription(value: string): BusinessBuilder {
        this._description = value
        return this
    }

    withWebsite(value: string): BusinessBuilder {
        this._website = value
        return this
    }

    withEmail(value: string): BusinessBuilder {
        this._email = value
        return this
    }

    withPhoneNumber(value: string): BusinessBuilder {
        this._phoneNumber = value
        return this
    }

    withPlan(value: string): BusinessBuilder {
        this._plan = value
        return this
    }

    withStatus(value: string): BusinessBuilder {
        this._status = value
        return this
    }

    withStartDate(value: string): BusinessBuilder {
        this._startDate = value
        return this
    }

    withEndDate(value: string): BusinessBuilder {
        this._endDate = value
        return this
    }

    withIsSubscriptionActive(value: boolean): BusinessBuilder {
        this._isSubscriptionActive = value
        return this
    }

    withTrialEndDate(value: Date): BusinessBuilder {
        this._trialEndDate = value
        return this
    }

    fromBusiness(business: Business): BusinessBuilder {
        this._id = business.id
        this._name = business.name
        this._type = business.type
        this._activity = business.activity
        this._membership = business.membership
        this._siren = business.siren
        this._isVerified = business.isVerified
        this._coverImage = business.coverImage
        this._profileImage = business.profileImage
        this._openingHours = business.openingHours
        this._active = business.active

        this._description = business.contacts.description
        this._website = business.contacts.website
        this._email = business.contacts.email
        this._phoneNumber = business.contacts.phoneNumber

        this._address = business.address.address
        this._zipcode = business.address.zipCode
        this._latitude = business.address.latitude
        this._longitude = business.address.longitude
        this._city = business.address.city
        this._country = business.address.country

        this._plan = business.subscription.plan;
        this._status = business.subscription.status;
        this._startDate = business.subscription.startDate;
        this._endDate = business.subscription.endDate;
        this._isSubscriptionActive = business.subscription.isSubscriptionActive;
        this._trialEndDate = business.subscription.trialEndDate;

        return this
    }

    build(): Business {
        const contact = new BusinessContact(
            this._description,
            this._website,
            this._email,
            this._phoneNumber
        )
        const address = new BusinessAddress(
            this._address,
            this._zipcode,
            this._latitude,
            this._longitude,
            this._city,
            this._country
        )
        const subscription = new BusinessSubscription(
            this._plan,
            this._startDate,
            this._endDate,
            this._status,
            this._isSubscriptionActive,
            this._trialEndDate
        )
        return new Business(
            this._id,
            this._name,
            this._type,
            this._activity,
            this._active,
            this._membership,
            this._siren,
            this._isVerified,
            this._coverImage,
            this._profileImage,
            this._openingHours,
            contact,
            address,
            subscription
        )
    }
}
