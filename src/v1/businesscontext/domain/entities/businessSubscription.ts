export class BusinessSubscription {
    constructor(
        protected _plan: string,
        protected _startDate: string,
        protected _endDate: string,
        protected _status: string,
        protected _isSubscriptionActive: boolean,
        protected _trialEndDate: Date
    ) {}

    get plan(): string {
        return this._plan;
    }

    get startDate(): string {
        return this._startDate;
    }

    get endDate(): string {
        return this._endDate;
    }

    get status(): string {
        return this._status;
    }

    get isSubscriptionActive(): boolean {
        return this._isSubscriptionActive;
    }

    get trialEndDate(): Date {
        return this._trialEndDate;
    }

    get duration(): number {
        const start = new Date(this._startDate);
        const end = new Date(this._endDate);
        const durationInMs = end.getTime() - start.getTime();
        return Math.ceil(durationInMs / (1000 * 60 * 60 * 24)); // Convert to days
    }

    get summary(): string {
        return `${this._plan.charAt(0).toUpperCase() + this._plan.slice(1)} plan, ${this._status.toLowerCase()}, from ${this._startDate} to ${this._endDate}`;
    }
}

export class SubscriptionBuilder {
    private _plan: string;
    private _startDate: string;
    private _endDate: string;
    private _status: string;
    private _isSubscriptionActive: boolean;
    private _trialEndDate: Date;

    withPlan(plan: string): SubscriptionBuilder {
        this._plan = plan;
        return this;
    }

    withStartDate(startDate: string): SubscriptionBuilder {
        this._startDate = startDate;
        return this;
    }

    withEndDate(endDate: string): SubscriptionBuilder {
        this._endDate = endDate;
        return this;
    }

    withStatus(status: string): SubscriptionBuilder {
        this._status = status;
        return this;
    }

    withSubscriptionActive(isSubscriptionActive: boolean): SubscriptionBuilder {
        this._isSubscriptionActive = isSubscriptionActive;
        return this;
    }

    withTrialEndDate(trialEndDate: Date): SubscriptionBuilder {
        this._trialEndDate = trialEndDate;
        return this;
    }

    build(): BusinessSubscription {
        return new BusinessSubscription(
            this._plan,
            this._startDate,
            this._endDate,
            this._status,
            this._isSubscriptionActive,
            this._trialEndDate
        );
    }
}