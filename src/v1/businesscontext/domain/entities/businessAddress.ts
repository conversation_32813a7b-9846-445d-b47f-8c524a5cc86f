import { Coordinates } from '../../../common/domain/entities/Coordinates';
import { Countries } from '../../../configuration/setting/app.settings';

export class BusinessAddress {
    constructor(
        protected _address: string,
        protected _zipCode: string,
        protected _latitude: number,
        protected _longitude: number,
        protected _city: string,
        protected _country: string
    ) {
    }

    get address(): string {
        return this._address;
    }

    get zipCode(): string {
        return this._zipCode;
    }

    get latitude(): number {
        return this._latitude;
    }

    get longitude(): number {
        return this._longitude;
    }

    get city(): string {
        return this._city;
    }

    get country(): string {
        return this._country;
    }

    get fullAddress(): string {
        return `${this._address}, ${this._zipCode}, ${this._city}, ${this.getLabelFromValue(this._country)}`
    }

    get coordinates(): Coordinates {
        return { longitude: this._longitude, latitude: this._latitude }
    }

    getLabelFromValue(value: string): string {
            for (const country of Countries())
                if (country.code === value)
                    return country.name
    }
}
