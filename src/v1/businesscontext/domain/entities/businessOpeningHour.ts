export class BusinessOpeningHours {
    constructor(
        protected _id: string,
        protected _startTime: string,
        protected _endTime: string,
        protected _openingDays: 'fixed' | 'all_days' |
    'mondays_to_fridays' | 'saturdays_and_sundays' |
    'all_mondays' | 'all_tuesdays' |
    'all_wednesdays' | 'all_thursdays' |
    'all_fridays' | 'all_saturdays' |
    'all_sundays',
        protected _isPaused: boolean
    ) {}

    get id(): string {
        return this._id
    }

    get startTime(): string {
        return this._startTime
    }

    get endTime(): string {
        return this._endTime
    }

    get openingDays() {
        return this._openingDays
    }
    get isPaused(): boolean {
        return this._isPaused
    }
}
