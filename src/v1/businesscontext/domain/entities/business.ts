import {BusinessAddress} from './businessAddress';
import {BusinessContact} from './businessContact';
import {BusinessOpeningHours} from './businessOpeningHour';
import {BusinessSubscription} from './businessSubscription';

export class Business {
  static FREEMIUM: 'freemium' | 'premium' = 'freemium';

  constructor(
    protected _id: string,
    protected _name: string,
    protected _type: string,
    protected _activity: string,
    protected _active: boolean,
    protected _membership: 'freemium' | 'premium',
    protected _siren: string,
    protected _isVerified: boolean,
    protected _coverImage: string,
    protected _profileImage: string,
    protected _openingHours: BusinessOpeningHours[],
    protected _contacts: BusinessContact,
    protected _address: BusinessAddress,
    protected _subscription: BusinessSubscription,
  ) {}

  get id() {
    return this._id;
  }

  get name() {
    return this._name;
  }

  get type() {
    return this._type;
  }

  get activity() {
    return this._activity;
  }
  get active() {
    return this._active;
  }
  get membership(): 'freemium' | 'premium' {
    return this._membership;
  }

  get address(): BusinessAddress {
    return this._address;
  }

  get siren() {
    return this._siren;
  }

  get isVerified() {
    return this._isVerified;
  }

  get coverImage(): string {
    return this._coverImage;
  }

  get profileImage(): string {
    return this._profileImage;
  }

  public set coverImage(v: string) {
    this._coverImage = v;
  }

  public set profileImage(v: string) {
    this._profileImage = v;
  }

  get openingHours(): BusinessOpeningHours[] {
    return this._openingHours;
  }

  get contacts(): BusinessContact {
    return this._contacts;
  }

  get subscription(): BusinessSubscription {
    return this._subscription;
  }
}
