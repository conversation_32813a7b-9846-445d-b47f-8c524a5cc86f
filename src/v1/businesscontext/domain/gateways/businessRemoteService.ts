import { Observable } from 'rxjs';
import { Business } from '../entities/business';

export interface BusinessRemoteService {

    getBusinessBySiren(siren: string): Observable<Business>

    registerBusiness(business: Business, professionalID: string, frontCardPicture: string, backCardPicture: string): Observable<string>

    editRemoteBusiness(business: Business): Observable<void>

    loadRemoteBusiness(businessId: string): Observable<Business>

    checkOwnership(): Observable<string>
}
