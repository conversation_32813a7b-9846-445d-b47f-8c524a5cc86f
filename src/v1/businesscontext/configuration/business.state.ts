import { Business } from '../domain/entities/business';
import { BusinessEditionState } from './edition/businessEdition.state';
import { LoadLocalBusinessState } from './localLoading/loadLocalBusinessState';
import { BusinessRegistrationState } from './registration/business.registration.state';
import { LoadRemoteBusinessState } from './remoteLoading/loadLocalBusinessState';

export interface BusinessInfoState {
    business: Business
}
export interface BusinessState {
    registration: BusinessRegistrationState
    loadLocalBusiness: LoadLocalBusinessState
    loadRemoteBusiness: LoadRemoteBusinessState
    businessEdition: BusinessEditionState
    business: BusinessInfoState
    ownership: CheckOwnershipState
}

export interface CheckOwnershipState {
    loading: boolean
    error: string
    success: boolean
}
