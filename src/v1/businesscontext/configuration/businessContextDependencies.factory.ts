import Config from 'react-native-config';
import { InMemoryBusinessRepository } from '../adapters/secondaries/inmemory/inMemoryBusiness.repository';
import { InMemoryBusinessService } from '../adapters/secondaries/inmemory/inMemoryBusinessService';
import { APIBusinessService } from '../adapters/secondaries/real/APIBusinessService';
import { SqliteBusinessRepository } from '../adapters/secondaries/real/SqliteBusinessRepository';
import { BusinessRepository } from '../domain/gateways/business.repository';
import { BusinessRemoteService } from '../domain/gateways/businessRemoteService';

export class BusinessContextDependenciesFactory {
    static businessRemoteService(): BusinessRemoteService {
        switch (Config.ENV) {
            case 'prod':
            case 'staging':
            case 'dev':
                return new APIBusinessService()
            default:
                return new InMemoryBusinessService()
        }
    }

    static businessRepository(): BusinessRepository {
        switch (Config.ENV) {
            case 'prod':
            case 'staging':
            case 'dev':
                return new SqliteBusinessRepository()
            default:
                return new InMemoryBusinessRepository()
        }
    }
}
