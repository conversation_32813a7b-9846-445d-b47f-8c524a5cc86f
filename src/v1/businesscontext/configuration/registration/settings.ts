import { I18n } from '../../../configuration/i18n/i18n';

export const BusinessTypes = () => [
    { label: I18n.getTranslation().common.settings.types.sport_club, value: 'sport_club' },
    { label: I18n.getTranslation().common.settings.types.sports_association, value: 'sports_association' },
    { label: I18n.getTranslation().common.settings.types.sports_facilitiy, value: 'sports_facilitiy' },
    { label: I18n.getTranslation().common.settings.types.national_federation, value: 'national_federation' },
    { label: I18n.getTranslation().common.settings.types.sports_league, value: 'sports_league' },
    { label: I18n.getTranslation().common.settings.types.company, value: 'company' },
    { label: I18n.getTranslation().common.settings.types.sport_doctor, value: 'sport_doctor' },
    { label: I18n.getTranslation().common.settings.types.physiotherapist, value: 'physiotherapist' },
    { label: I18n.getTranslation().common.settings.types.dietitian, value: 'dietitian' },
    { label: I18n.getTranslation().common.settings.types.shop, value: 'shop' },
    { label: I18n.getTranslation().common.settings.types.coach, value: 'coach' },
    { label: I18n.getTranslation().common.settings.types.promotor, value: 'promotor' },
    { label: I18n.getTranslation().common.settings.types.masseur, value: 'masseur' },
    { label: I18n.getTranslation().common.settings.types.paramedical, value: 'paramedical' },
    { label: I18n.getTranslation().common.settings.types.training_center, value: 'training_center' },
    { label: I18n.getTranslation().common.settings.types.recreation_centre, value: 'recreation_centre' },


]
