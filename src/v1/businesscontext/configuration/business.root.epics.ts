import { combineEpics } from 'redux-observable';
import { checkOwnershipEpic } from '../usecases/checkOwnership/checkOwnership.epic';
import { businessEditionEpic } from '../usecases/edition/businessEdition.epic';
import { loadLocalBusinessEpic } from '../usecases/localLoading/loadLocalBusiness.epic';
import {
    businessRegistrationEpics,
    loadBusinessBySirenEpic
} from '../usecases/registration/businessRegistration.epics';
import { loadRemoteBusinessEpic } from '../usecases/remoteLoading/loadRemoteBusiness.epic';

export const businessRootEpics = combineEpics(
    businessRegistrationEpics,
    loadBusinessBySirenEpic,
    businessEditionEpic,
    loadLocalBusinessEpic,
    loadRemoteBusinessEpic,
    checkOwnershipEpic
)
