import { combineReducers } from 'redux';
import { businessReducer } from '../usecases/business.reducer';
import { checkOwnershipReducer } from '../usecases/checkOwnership/checkOwnership.reducer';
import { businessEditionReducer } from '../usecases/edition/businessEdition.reducer';
import { loadLocalBusinessReducer } from '../usecases/localLoading/loadLocalBusiness.reducer';
import { businessRegistrationReducer } from '../usecases/registration/businessRegistration.reducers';
import { loadRemoteBusinessReducer } from '../usecases/remoteLoading/loadRemoteBusiness.reducer';
import { BusinessState } from './business.state';

export const businessRootReducer = combineReducers<BusinessState, any>({
    businessEdition   : businessEditionReducer,
    registration      : businessRegistrationReducer,
    loadLocalBusiness : loadLocalBusinessReducer,
    loadRemoteBusiness: loadRemoteBusinessReducer,
    business          : businessReducer,
    ownership         : checkOwnershipReducer
})
