import { Observable, of } from 'rxjs';
import { BusinessBuilder } from '../../../domain/builder/business.builder';
import { Business } from '../../../domain/entities/business';
import { BusinessRepository } from '../../../domain/gateways/business.repository';

export class InMemoryBusinessRepository implements BusinessRepository {

    getLocalBusiness(): Observable<Business> {
        return of(new BusinessBuilder()
            .withId('a35bfbf6-8091-4536-b9b7-078e1c7b3b45')
            .withName('Store name')
            .withType('Entreprise')
            .withAddress('21 RUE DE LA TERRASSE')
            .withZipCode('75017')
            .withLatitude(48.903617)
            .withLongitude(2.304809)
            .withCity('paris')
            .withSiren('123456789')
            .withCoverImage(null)
            .withProfileImage(null)
            .withOpeningHours([{
                id         : '123456789',
                openingDays: {
                    key      : 'all_saturdays',
                    label    : 'all_saturdays',
                    startTime: '08:10',
                    endTime  : '18:00'
                },
                isPaused   : true
            }, {
                id         : '123456788',
                openingDays: {
                    key      : 'all_saturdays',
                    label    : 'all_saturdays',
                    startTime: '08:00',
                    endTime  : '13:00'
                },
                isPaused   : true
            }])
            .withDescription('lorem empsum Commerce de détail de boissons en magasin spécialisé ')
            .withWebsite('www.website.com')
            .withEmail('<EMAIL>')
            .withPhoneNumber('+***********')
            .build())
    }

    saveBusiness(business: Business): Observable<void> {
        return of();
    }

    editBusiness(business: Business): Observable<void> {
        return of(null)
    }
}
