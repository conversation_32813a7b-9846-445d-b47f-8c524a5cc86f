import { Observable, of } from 'rxjs';
import { BusinessBuilder } from '../../../domain/builder/business.builder';
import { Business } from '../../../domain/entities/business';
import { BusinessRemoteService } from '../../../domain/gateways/businessRemoteService';

export class InMemoryBusinessService implements BusinessRemoteService {
    getBusinessBySiren(siren: string): Observable<Business> {
        return of(new BusinessBuilder()
            .withName('Store name')
            .withType('Entreprise')
            .withAddress('21 Avenue Claude Debussy')
            .withZipCode('92110')
            .withCity('Clichy')
            .withSiren(siren)
            .build())
    }

    registerBusiness(business: Business, professionalID: string, frontCardPicture: string, backCardPicture: string): Observable<string> {
        return of('a35bfbf6-8091-4536-b9b7-078e1c7b3b45')
    }

    editRemoteBusiness(business: Business) {
        return of(void 0)
    }

    loadRemoteBusiness(businessId: string): Observable<Business> {
        return of(new BusinessBuilder()
            .withId('businessId')
            .withName('Store name')
            .withType('Entreprise')
            .withAddress('21 Avenue Claude Debussy')
            .withZipCode('92110')
            .withCity('Clichy')
            .withActive(true)
            .withCountry('FR')
            .build())
    }
    checkOwnership(): Observable<string> {
        return undefined;
    }
}
