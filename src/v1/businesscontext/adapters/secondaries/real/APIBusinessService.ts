import Config from 'react-native-config';
import { map } from 'rxjs/internal/operators';
import { catchError } from 'rxjs/operators';
import { Observable } from 'rxjs/Rx';
import { SecuredObservableAjaxHttpClient } from '../../../../common/adapters/secondaries/real/securedObservableAjax.httpClient';
import { ApplicationContextDependenciesFactory } from '../../../../common/configuration/applicationContextDependencies.factory';
import { BusinessBuilder } from '../../../domain/builder/business.builder';
import { Business } from '../../../domain/entities/business';
import { BusinessOpeningHours } from '../../../domain/entities/businessOpeningHour';
import { BusinessRemoteService } from '../../../domain/gateways/businessRemoteService';
import { BusinessMapper } from './mappers/business.mapper';
import { ApiSirenInfoDTO, BusinessApiDTO } from './mappers/dto/business.db.dto';

interface TypeOpeningHours {
    id: number,
    daytype: string
    startTime: string
    endTime: string
    enabled: number
}

export class APIBusinessService implements BusinessRemoteService {

    getBusinessBySiren(siren: string): Observable<Business> {
        const url = `${Config.API_URL}/v1/company/siren?siren=${siren}`
        return new SecuredObservableAjaxHttpClient(ApplicationContextDependenciesFactory.sessionRepository()).get(url)
            .pipe(
                map((response: ApiSirenInfoDTO) => new BusinessBuilder()
                    .withName(response.data.name)
                    .withType('Entreprise')
                    .withAddress(response.data.address)
                    .withZipCode(response.data.zipCode)
                    .withCity(response.data.city)
                    .withSiren(siren)
                    .build()
                ), catchError(err => Observable.throwError(err.status.toString())))
    }

    registerBusiness(business: Business, professionalID: string, frontCardPicture: string, backCardPicture: string): Observable<string> {
        const frontCardPictureName = frontCardPicture.substring(frontCardPicture.lastIndexOf('/') + 1)
        const backCardPictureName = backCardPicture.substring(backCardPicture.lastIndexOf('/') + 1)
        const professionalIDName =  professionalID.substring(professionalID.lastIndexOf('/') + 1)
        const body = new FormData();
        body.append('id-card-front', { uri: frontCardPicture, name: frontCardPictureName, type: 'image/jpeg' });
        body.append('id-card-back', { uri: backCardPicture, name: backCardPictureName, type: 'image/jpeg' });
        body.append('pro-id', { uri: professionalID, name: professionalIDName, type: 'image/jpeg' })
        body.append('legal_name', business.name)
        body.append('legal_street_address', business.address.address)
        body.append('legal_zipcode', business.address.zipCode)
        body.append('legal_city', business.address.city)
        body.append('legal_country', business.address.country)
        body.append('type', business.type)
        body.append('phoneNumber', business.contacts.phoneNumber);
        const url = `${Config.API_URL}/v1/company/subscription`

        return new SecuredObservableAjaxHttpClient(ApplicationContextDependenciesFactory.sessionRepository()).post(url, body)
            .pipe(
                map((response: { response: { data: string } }) => response.response.data
                    , catchError(err => Observable.throwError(err.status.toString())
                    )
                )
            )
    }

    editRemoteBusiness(business: Business): Observable<void> {
        const body = new FormData();
        const url = `${Config.API_URL}/v1/company/${business.id}/update-settings-and-openingHours`
        const header = {
            'Content-Type': 'multipart/form-data'
        }
        if (!this.isWebsite(business.profileImage)) {
            const profileImageName = business.profileImage.substring(business.profileImage.lastIndexOf('/') + 1)
            body.append('profileImage', { uri: business.profileImage, name: profileImageName, type: 'image/jpeg' });
        }

        if (!this.isWebsite(business.coverImage) && business.coverImage !== '' && business.coverImage !== null && business.coverImage !== undefined) {
            const coverImageName = business.coverImage.substring(business.coverImage.lastIndexOf('/') + 1)
            body.append('coverImage', { uri: business.coverImage, name: coverImageName, type: 'image/jpeg' });
        }
        if (business.contacts.phoneNumber)
            body.append('phoneNumber', business.contacts.phoneNumber);
        if (business.contacts.website)
            body.append('website', business.contacts.website);
        if (business.contacts.email)
            body.append('email', business.contacts.email);

        body.append('companyId', business.id);
        body.append('name', business.name);
        body.append('type', business.type);
        body.append('category', business.activity);
        body.append('address', business.address.address);
        body.append('city', business.address.city);
        body.append('country', business.address.country);
        body.append('zipCode', business.address.zipCode);
        body.append('longitude', business.address.longitude);
        body.append('latitude', business.address.latitude);
        body.append('description', business.contacts.description);
        body.append('openingHours', JSON.stringify(this.openingHoursCommand(business.openingHours)))

        return new SecuredObservableAjaxHttpClient(ApplicationContextDependenciesFactory.sessionRepository()).post(url, body, header)
            .pipe(
                map(() => void 0),
                catchError(err => Observable.throwError(err.message.toString())
                )
            )
    }

    loadRemoteBusiness(businessId: string): Observable<Business> {
        const url = `${Config.API_URL}/v1/company/settings/${businessId}`
        return new SecuredObservableAjaxHttpClient(ApplicationContextDependenciesFactory.sessionRepository()).get(url)
            .pipe(
                map((businessApiDTO: BusinessApiDTO) => BusinessMapper.mapApiToDomainBusiness(businessApiDTO)),
                catchError(err => Observable.throwError(err))
            )
    }

    checkOwnership(): Observable<string> {
        const url = `${Config.API_URL}/v1/company/ownership`
        return new SecuredObservableAjaxHttpClient(ApplicationContextDependenciesFactory.sessionRepository()).get(url)
            .pipe(
                map((response: { data: Array<{ companyId: string, profileId: string }> }) => {
                    if (response.data.length > 0)
                        return response.data[0].companyId
                    Observable.throwError('NO_ASSOCIATION')
                }),
                catchError(err => Observable.throwError(err))
            )
    }

    private openingHoursCommand(openingHours: BusinessOpeningHours[]): TypeOpeningHours[] {
        const openingHoursFormat = []
        openingHours.map(item => {
            openingHoursFormat.push({
                id       : item.id,
                daytype  : item.openingDays,
                startTime: this.removeSeconds(item.startTime),
                endTime  : this.removeSeconds(item.endTime),
                enabled  : item.isPaused ? '0' : '1'
            })
        })
        return openingHoursFormat
    }

    private removeSeconds(time: string): string {
        return time.substr(0, 5)
    }

    private isWebsite(website: string): boolean {
        const regex = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
        return regex.test(website)
    }
}
