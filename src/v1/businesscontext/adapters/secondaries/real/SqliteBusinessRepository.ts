import {Observable} from 'rxjs';
import {fromPromise} from 'rxjs/internal/observable/fromPromise';
import {ApplicationContext} from '../../../../common/configuration/application.context';
import {Business} from '../../../domain/entities/business';
import {BusinessRepository} from '../../../domain/gateways/business.repository';
import {BusinessMapper} from './mappers/business.mapper';

const db = ApplicationContext.getInstance().db();

export class SqliteBusinessRepository implements BusinessRepository {
  getLocalBusiness(): Observable<Business> {
    const getBusinessDetailPromise = new Promise<Business>(
      (resolve, reject) => {
        db.transaction(transactionSelect => {
          transactionSelect.executeSql(
            'SELECT * from business',
            [],
            (transaction, result) => {
              if (result.rows.length > 0)
                resolve(
                  BusinessMapper.mapDBToDomainBusiness(result.rows.item(0)),
                );
              else reject(0);
            },
            err => reject(err),
          );
        });
      },
    );
    return fromPromise(getBusinessDetailPromise);
  }

  saveBusiness(business: Business): Observable<void> {
    const saveBusinessPromise = new Promise<void>((resolve, reject) => {
      db.transaction(
        transactionInsertion => {
          transactionInsertion.executeSql(
            'INSERT INTO business values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [
              business.id,
              business.name,
              business.type,
              business.membership,
              business.address.address,
              business.address.zipCode,
              business.address.longitude,
              business.address.latitude,
              business.address.city,
              business.address.country,
              business.coverImage,
              business.profileImage,
              business.contacts.description,
              business.contacts.website,
              business.contacts.email,
              business.contacts.phoneNumber,
            ],
            () => resolve(void 0),
            err => reject(err),
          );
        },
        err => reject(err),
      );
    });

    return fromPromise(saveBusinessPromise);
  }

  editBusiness(business: Business): Observable<void> {
    const newBusinessPromise = new Promise<void>((resolve, reject) => {
      db.transaction(transaction => {
        transaction.executeSql(
          'UPDATE business SET name = ? , type = ?, membership = ?, address = ? , zipCode = ?, ' +
            'longitude = ?, latitude = ? , city = ?, country = ?, coverImage = ?, profileImage = ? , ' +
            'description = ? , website = ?, email = ?, phoneNumber = ? WHERE id = ?',
          [
            business.name,
            business.type,
            business.membership,
            business.address.address,
            business.address.zipCode,
            business.address.longitude,
            business.address.latitude,
            business.address.city,
            business.address.country,
            business.coverImage,
            business.profileImage,
            business.contacts.description,
            business.contacts.website,
            business.contacts.email,
            business.contacts.phoneNumber,
            business.id,
          ],
          (transactionUpdate, result) => {
            if (result.rowsAffected === 1) return resolve(void 0);
            else return reject('error');
          },
          err => reject(err),
        );
      });
    });

    return fromPromise(newBusinessPromise);
  }
}
