export interface BusinessDatabaseDTO {
    id: string
    siren: string
    name: string
    type: string
    membership: 'freemium' | 'premium'
    address: string
    zipCode: string
    longitude: string
    latitude: string
    city: string
    country: string
    coverImage: string
    profileImage: string
    description: string
    website: string
    email: string
    phoneNumber: string
}

export interface OpeningHoursDatabaseDTO {
    id: number
    startTime: string
    endTime: string
    isPaused: boolean
    openingDays: 'fixed' | 'all_days' |
        'mondays_to_fridays' | 'saturdays_and_sundays' |
        'all_mondays' | 'all_tuesdays' |
        'all_wednesdays' | 'all_thursdays' |
        'all_fridays' | 'all_saturdays' |
        'all_sundays'
}

export interface ApiSirenInfoDTO {
    status: number
    message: string,
    data: {
        address: string
        city: string
        name: string
        siren: number
        zipCode: string
    }

}

export interface BusinessApiDTO {
    status: number
    message: string
    data: {
        companyId: string
        name: string
        type: string
        category: string
        membership: 'freemium' | 'premium'
        active: string
        coverImage: string
        profileImage: string
        description: string
        website: string
        email: string
        phoneNumber: string
        address: string
        city: string
        country: string
        zipCode: string
        longitude: string
        latitude: string
        openHours: OpeningHoursApiDTO[]
    }
}

interface OpeningHoursApiDTO {
    companyId: string
    enabled: string
    endTime: string
    id: string
    startTime: string
    type: 'all_days' |
        'mondays_to_fridays' | 'saturdays_and_sundays' |
        'all_mondays' | 'all_tuesdays' |
        'all_wednesdays' | 'all_thursdays' |
        'all_fridays' | 'all_saturdays' |
        'all_sundays'
}
