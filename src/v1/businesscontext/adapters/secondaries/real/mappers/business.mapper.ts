import Config from 'react-native-config';
import { BusinessBuilder } from '../../../../domain/builder/business.builder';
import { Business } from '../../../../domain/entities/business';
import { BusinessOpeningHoursType } from '../../../primaries/edition/business/businessEdition.types';
import { BusinessApiDTO, BusinessDatabaseDTO } from './dto/business.db.dto';

export class BusinessMapper {
    static mapDBToDomainBusiness(businessDBDTO: BusinessDatabaseDTO): Business {
        return new BusinessBuilder()
            .withId(businessDBDTO.id)
            .withName(businessDBDTO.name)
            .withType(businessDBDTO.type)
            .withMembership(businessDBDTO.membership)
            .withAddress(businessDBDTO.address)
            .withZipCode(businessDBDTO.zipCode)
            .withCity(businessDBDTO.city)
            .withCountry(businessDBDTO.country)
            .withLongitude(Number(businessDBDTO.longitude))
            .withLatitude(Number(businessDBDTO.latitude))
            .withSiren(businessDBDTO.siren)
            .withCoverImage(businessDBDTO.coverImage)
            .withProfileImage(businessDBDTO.profileImage)
            .withDescription(businessDBDTO.description)
            .withWebsite(businessDBDTO.website)
            .withEmail(businessDBDTO.email)
            .withPhoneNumber(businessDBDTO.phoneNumber)
            .build()
    }

    static mapApiToDomainBusiness(businessApiDTO: BusinessApiDTO): Business {
        const openingHours: BusinessOpeningHoursType [] = []
        if (businessApiDTO.data.openHours.length > 0)
            businessApiDTO.data.openHours.map(item =>
                openingHours.push({
                    id         : item.id,
                    openingDays: {
                        key      : item.type,
                        label    : item.type,
                        startTime: item.startTime,
                        endTime  : item.endTime
                    },
                    isPaused   : !item.enabled
                })
            )
        return new BusinessBuilder()
            .withId(businessApiDTO.data.companyId)
            .withName(businessApiDTO.data.name)
            .withType(businessApiDTO.data.type)
            .withActivity(businessApiDTO.data.category)
            .withActive(businessApiDTO.data.active === 'true')
            .withMembership(businessApiDTO.data.membership)
            .withAddress(businessApiDTO.data.address)
            .withZipCode(businessApiDTO.data.zipCode)
            .withCity(businessApiDTO.data.city)
            .withCountry(businessApiDTO.data.country)
            .withLongitude(Number(businessApiDTO.data.longitude))
            .withLatitude(Number(businessApiDTO.data.latitude))
            .withCoverImage(this.getIcon(businessApiDTO.data.coverImage))
            .withProfileImage(this.getIcon(businessApiDTO.data.profileImage))
            .withDescription(businessApiDTO.data.description)
            .withWebsite(businessApiDTO.data.website)
            .withEmail(businessApiDTO.data.email)
            .withPhoneNumber(businessApiDTO.data.phoneNumber)
            .withOpeningHours(openingHours)
            .build()
    }

    private static getIcon(pictureUrl: string): string {
        if (pictureUrl)
            return Config.API_URL + '/' + pictureUrl
        return pictureUrl
    }

}
