import React, { PureComponent } from 'react';
import { StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native';
import { WebViewBrowser } from '../../../../../common/adapters/primaries/components/modal/webViewBrowser.modal';
import { I18n } from '../../../../../configuration/i18n/i18n';
import { Theme } from '../../../../../configuration/theme/app.theme';
interface Props {
    cgu: string
    privacy: string
}

interface State {
    visibleModal: boolean
    urlModal: string
}

export class TermsAndCondition extends PureComponent<Props, State> {
    constructor(props) {
        super(props)
        this.state = {
            visibleModal: false,
            urlModal: ''
        }
    }
    render() {
        return (
            <View style={styles.agreeText}>
                <Text style={styles.text}>{I18n.getTranslation().business.text_to_agree}</Text>
                <TouchableWithoutFeedback onPress={() =>
                    this.setState({ visibleModal: true, urlModal: this.props.cgu })}>
                        <Text style={[styles.text, { color: Theme.magenta }]}>{I18n.getTranslation().business.cgu}</Text>
                </TouchableWithoutFeedback>
                <Text style={styles.text}>{I18n.getTranslation().business.and}</Text>
                <TouchableWithoutFeedback onPress={() =>
                    this.setState({ visibleModal: true, urlModal: this.props.privacy })}>
                        <Text style={[styles.text, { color: Theme.magenta }]}>{I18n.getTranslation().business.privacy}</Text>
                </TouchableWithoutFeedback>
                <Text style={styles.text}>{I18n.getTranslation().business.text_to_agree_part_2}</Text>
                <WebViewBrowser onCloseModal={() => this.setState({ visibleModal: false })}
                                visible={this.state.visibleModal}
                                urlBrowser={this.state.urlModal}/>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    bottom    : {
        flexDirection : 'row',
        alignItems    : 'center',
        justifyContent: 'center',
        marginVertical: 10
    },
    text      : {
        color     : 'black',
        fontSize  : 16,
        fontFamily: 'U8 Bold',
        textAlign : 'center'
    },
    agreeText : {
        width            : '90%',
        flexDirection    : 'row',
        flexWrap         : 'wrap',
        zIndex           : 1,
        alignItems       : 'center',
        justifyContent   : 'center',
        paddingHorizontal: 10,
        paddingVertical  : 20
    }
})
