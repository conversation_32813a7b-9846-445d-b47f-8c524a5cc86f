import React, { PureComponent } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { I18n } from '../../../../../configuration/i18n/i18n';
import { Business } from '../../../../domain/entities/business';

interface Props {
    business: Business
}

export class BusinessInfoPresentational extends PureComponent<Props, any> {
    render() {
        return (
            <View style={styles.container}>
                <View style={styles.content}>
                    <Text style={styles.businessItem}>{this.props.business.name}</Text>
                    <Text style={styles.businessItem}>
                        {this.props.business.address.address}
                    </Text>
                    <Text style={styles.businessItem}>
                        {this.props.business.address.zipCode}, {this.props.business.address.city}
                    </Text>
                </View>
                <View style={styles.title}>
                    <Text style={styles.text}>{I18n.getTranslation().business.description_business_info}</Text>
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container   : {
        paddingVertical   : 10,
        flex          : 1,
        width         : '100%',
        alignItems    : 'center',
        justifyContent: 'center'
    },
    title       : {
        width    : '90%',
        alignSelf: 'center',
        padding  : 5
    },
    text        : {
        color    : 'black',
        fontSize : 16,
        fontFamily: 'U8 Regular',
        textAlign: 'center'
    },
    content     : {
        borderWidth : 1,
        flex        : 1,
        padding     : 20,
        width       : '100%',
        borderColor : 'black',
        borderRadius: 5,
        opacity     : 0.8
    },
    businessItem: {
        fontSize: 18,
        fontFamily: 'U8 Regular',
        color   : 'black'
    }
})
