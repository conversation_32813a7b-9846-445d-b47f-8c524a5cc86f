import React, { PureComponent } from 'react'
import { StyleSheet, View } from 'react-native'
import { Theme } from '../../../../../configuration/theme/app.theme';

interface Props {
    children: any
    title?: string
    number?: string
    description?: string
}

export class HeaderForm extends PureComponent<Props, any> {
    render() {
        return (
            <View style={styles.containerGlobal}>
                <View style={styles.header}>
                    <View style={styles.headerItem}>
                        {this.props.children}
                    </View>
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    containerGlobal: {
        flexDirection : 'column',
        alignItems    : 'center',
        justifyContent: 'center',
        paddingTop    : 20
    },
    container      : {
        flex          : 1,
        flexDirection : 'row',
        justifyContent: 'center',
        alignItems    : 'center',
        width         : '100%'
    },
    header         : {
        flex          : 2,
        justifyContent: 'center',
        alignItems    : 'center'
    },
    headerItem     : {
        width    : 120,
        height   : 120,
        alignSelf: 'center'
    },
    numberView     : {
        borderRadius   : 13,
        height         : 26,
        width          : 26,
        marginRight    : 10,
        backgroundColor: Theme.magenta,
        alignSelf      : 'center'
    },
    number         : {
        color          : 'black',
        fontSize       : 12,
        fontFamily: 'U8 Regular',
        textAlign      : 'center',
        paddingVertical: 6
    },
    titleView      : {
        alignSelf: 'center'
    },
    title          : {
        fontSize  : 16,
        color     : 'white',
        fontFamily: 'U8 Bold'
    },
    description    : {
        flexDirection : 'row',
        alignItems    : 'center',
        justifyContent: 'center',
        marginVertical: 10
    },
    descriptionText: {
        width     : '80%',
        textAlign : 'center',
        color     : 'black',
        fontSize  : 14,
        fontFamily: 'U8 Regular',
        lineHeight: 18,
        flexWrap  : 'wrap'
    }
})
