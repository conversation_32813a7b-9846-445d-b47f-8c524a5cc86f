import React, {PureComponent} from 'react';
import {Keyboard, StyleSheet, View} from 'react-native';
import {InputWithLabel} from '../../../../../common/adapters/primaries/components/form/inputs/inputWithLabel';
import {ChoicePickerContainer} from '../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import {PicturePickerContainer} from '../../../../../common/adapters/primaries/components/form/pickers/picturePicker.container';
import {ChoiceType} from '../../../../../common/domain/entities/types/AppTypes';
import {RGBAColor} from '../../../../../common/domain/entities/types/rgba';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {Countries} from '../../../../../configuration/setting/app.settings';
import {BusinessTypes} from '../../../../configuration/registration/settings';
import {Business} from '../../../../domain/entities/business';
import {BusinessRegistrationValidator} from '../businessRegistration.validator';
import {BusinessFieldTypes} from '../static/businessFieldTypes';
import {CustomizedCameraContainer} from 'src/v1/common/adapters/primaries/components/form/pickers/customizedCamera.container';
import {PhoneInputField} from 'src/_components';
import { Coordinates } from 'src/v1/common/domain/entities/Coordinates';
import { AddressMap } from 'src/v1/common/adapters/primaries/components/form/fields/addressMap.field';
import { DefaultTextInput } from 'src/v1/common/adapters/primaries/components/form/inputs/defaultText.input';
import { SubmissionFormValidator } from 'src/v1/instantSubmissioncontext/adapters/primaries/form_common/validation/submission.validator';
import { util } from 'src/_helpers/util';
import { FormValidation } from 'src/v1/common/usecases/specifications/formValidation';

interface Props {
  raiseUpdates: (key: BusinessFieldTypes, value) => void;
  business: Business;
  isFormSubmitted: boolean;
}

interface State {
  name: string;
  type: string;
  idProfessional: string;
  frontCard: string;
  backCard: string;
  address: string;
  city: string;
  zipCode: string;
  country: string;
  addressPosition: Coordinates;
  positionTmp: {latitude: string; longitude: string};
  nameError: boolean;
  typeError: boolean;
  idProfessionalError: boolean;
  frontCardError: boolean;
  backCardError: boolean;
  addressError: boolean;
  cityError: boolean;
  zipCodeError: boolean;
  countryError: boolean;
  phoneNumber: string;
  phoneNumberError: boolean;
  latitudeError: boolean;
  longitudeError: boolean;
}

export class BusinessForm extends PureComponent<Props, State> {
  private previousAddress: {
    zipCode: string;
    city: string;
    country: string;
    address: string;
  };

  constructor(props) {
    super(props);
    this.state = {
      name: undefined,
      type: undefined,
      idProfessional: undefined,
      frontCard: undefined,
      backCard: undefined,
      address: '',
      city: '',
      zipCode: '',
      country: 'CM',
      addressPosition: {latitude: undefined, longitude: undefined},
      positionTmp: {latitude: undefined, longitude: undefined},
      nameError: null,
      typeError: null,
      idProfessionalError: null,
      frontCardError: null,
      backCardError: null,
      addressError: null,
      cityError: null,
      zipCodeError: null,
      countryError: null,
      phoneNumber: '+237',
      phoneNumberError: null,
      latitudeError: null,
      longitudeError: null,
    };
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.isFormSubmitted === true)
      if (
        this.props.isFormSubmitted !== prevProps.isFormSubmitted ||
        this.state !== prevState
      )
        this.setState({
          nameError: this.checkFieldError('name', this.state.name),
          typeError: this.checkFieldError('type', this.state.type),
          idProfessionalError: this.checkFieldError(
            'idProfessional',
            this.state.idProfessional,
          ),
          frontCardError: this.checkFieldError(
            'frontCard',
            this.state.frontCard,
          ),
          backCardError: this.checkFieldError('backCard', this.state.backCard),
          addressError: this.checkFieldError('address', this.state.address),
          cityError: this.checkFieldError('name', this.state.city),
          zipCodeError: this.checkFieldError('zipCode', this.state.zipCode),
          countryError: this.checkFieldError('country', this.state.country),
          phoneNumberError: this.checkFieldError(
            'phoneNumber',
            this.state.phoneNumber,
          ),
        });
  }

  render() {
    return (
      <View>
        <InputWithLabel
          placeholder={I18n.getTranslation().business.store_name}
          label={I18n.getTranslation().business.store_name}
          value={this.state.name}
          style={{marginTop: 12}}
          error={this.state.nameError}
          onChange={name =>
            this.setState({name}, () => this.props.raiseUpdates('name', name))
          }
        />
        <ChoicePickerContainer
          placeholder={I18n.getTranslation().business.type_placehoder}
          options={this.getCategoryList()}
          value={this.state.type}
          style={{marginTop: 12}}
          error={this.state.typeError}
          onChange={type =>
            this.setState({type: type.value}, () =>
              this.props.raiseUpdates('type', type.value),
            )
          }
        />

        <CustomizedCameraContainer
          style={{marginTop: 10}}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.id_professional_placeholder}
          imagePlaceholder={this.state.idProfessional}
          onPictureTaken={imageURI => {
            this.setState({idProfessional: imageURI}, () =>
              this.props.raiseUpdates('idProfessional', imageURI),
            );
          }}
        />

        <CustomizedCameraContainer
          style={{marginTop: 10}}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.card_front}
          imagePlaceholder={this.state.frontCard}
          onPictureTaken={imageURI => {
            this.setState({frontCard: imageURI}, () =>
              this.props.raiseUpdates('frontCard', imageURI),
            );
          }}
        />

        <CustomizedCameraContainer
          style={{marginTop: 10}}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.card_back}
          imagePlaceholder={this.state.backCard}
          onPictureTaken={imageURI => {
            this.setState({backCard: imageURI}, () =>
              this.props.raiseUpdates('backCard', imageURI),
            );
          }}
        />
        <InputWithLabel
          placeholder={I18n.getTranslation().business.address_placeholder}
          label={I18n.getTranslation().business.address}
          value={this.state.address}
          style={{marginTop: 12}}
          error={this.state.addressError}
          onChange={address =>
            this.setState({address}, () =>
              this.props.raiseUpdates('address', address),
            )
          }
          onBlur={() => this.getCoordinatesFromAddress()}
        />
        <View style={styles.addressContainer}>
          <InputWithLabel
            placeholder={I18n.getTranslation().business.city_placeholder}
            label={I18n.getTranslation().business.city}
            value={this.state.city}
            style={{width: '58%'}}
            error={this.state.cityError}
            onChange={city =>
              this.setState({city}, () => this.props.raiseUpdates('city', city))
            }
            onBlur={() => this.getCoordinatesFromAddress()}
          />
          <InputWithLabel
            placeholder={I18n.getTranslation().business.zipcode_placeholder}
            label={I18n.getTranslation().business.zipcode}
            value={this.state.zipCode}
            keyboardType={'numeric'}
            style={{width: '39%'}}
            error={this.state.zipCodeError}
            onChange={zipCode =>
              this.setState({zipCode}, () =>
                this.props.raiseUpdates('zipCode', zipCode),
              )
            }
            onBlur={() => this.getCoordinatesFromAddress()}
          />
        </View>
        <ChoicePickerContainer
          placeholder={I18n.getTranslation().business.country_placehoder}
          options={this.getCountryList()}
          value={this.state.country}
          style={{marginTop: 12}}
          error={this.state.countryError}
          onChange={country =>
            this.setState({country: country.value}, () =>
              this.props.raiseUpdates('country', country.value),
            )
          }
        />
        <AddressMap
          style={{marginTop: 10}}
          iconType={this.props.iconType}
          position={this.state.addressPosition}
          draggableMarker={true}
          raiseUpdates={(key, value) => this.props.raiseUpdates(key, value)}
        />
        <DefaultTextInput
          style={{marginTop: 10}}
          placeholder={I18n.getTranslation().instant_submission.latitude}
          keyboardType={'numeric'}
          onChange={value => {
            const location = {
              latitude: value,
              longitude: this.state.positionTmp.longitude,
            };
            this.setState({
              positionTmp: location,
              latitudeError:
                !SubmissionFormValidator.checkCoordinate(value),
            });
          }}
          error={this.state.latitudeError}
          value={this.state.positionTmp.latitude}
          onBlur={() => this.convertCoordinateToPosition()}
        />
        <DefaultTextInput
          style={{marginTop: 10}}
          placeholder={I18n.getTranslation().instant_submission.longitude}
          keyboardType={'numeric'}
          onChange={value => {
            /*const parts = value.split(".");
          const parts2 =
            parts[1].length > 7 ? parts[1].substring(0, 7) : parts[1];*/

            const location = {
              latitude: this.state.positionTmp.latitude,
              longitude: value,
            };
            /*const location = {
            latitude: this.state.positionTmp.latitude,
            longitude: `${parts[0]}.${parts2}`,
          };*/
            this.setState({
              positionTmp: location,
              longitudeError:
                !SubmissionFormValidator.checkCoordinate(value),
            });
          }}
          error={this.state.longitudeError}
          onBlur={() => this.convertCoordinateToPosition()}
          value={this.state.positionTmp.longitude}
        />
        <PhoneInputField
          style={{marginTop: 10}}
          placeholder={I18n.getTranslation().business.phone_number}
          value={this.state.phoneNumber}
          error={this.state.phoneNumberError ? false : null}
          onChange={(phoneNumber, isValid) =>
            this.onPhoneNumberChangeHandler(phoneNumber, isValid)
          }
        />
      </View>
    );
  }

  private convertCoordinateToPosition() {
    let location: Coordinates = {latitude: undefined, longitude: undefined};
    if (this.state.positionTmp.latitude && this.state.positionTmp.longitude)
      location = {
        latitude: parseFloat(
          parseFloat(this.state.positionTmp.latitude).toFixed(7),
        ),
        longitude: parseFloat(
          parseFloat(this.state.positionTmp.longitude).toFixed(7),
        ),
      };
    this.setState({addressPosition: location}, () => {
      this.props.raiseUpdates('coordinates', location);
    });
  }

  private async geocodeAddress(
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ) {
    const res = await util.geocodeAddress(zipCode, city, country, address);

    if (res?.longitude && res?.latitude) {
      this.setState({
        addressPosition: res,
        positionTmp: {
          latitude: res.latitude.toString(),
          longitude: res.longitude.toString()
        },
      })
      this.props.raiseUpdates('coordinates', res);
    }
  }

  private getCoordinatesFromAddress() {
    /*if (
      !this.state.addressPosition.longitude &&
      !this.state.addressPosition.latitude
    )*/
    console.log("Form is valid: ", this.isValidForm());
    if (this.isValidForm()) {
      /*
      this.previousAddress = {
        address: this.state.address,
        city: this.state.city,
        country: this.state.country,
        zipCode: this.state.zipCode,
      };
      */
      this.geocodeAddress(
        this.state.zipCode,
        this.state.city,
        this.state.country,
        this.state.address,
      );
    }
  }

  private checkIfThereIsError() {
    this.setState({
      addressError: !FormValidation.validation('address', this.state.address),
      cityError: !SubmissionFormValidator.checkCity(this.state.city),
      zipCodeError: !FormValidation.validation('zipCode', this.state.zipCode),
      latitudeError: !SubmissionFormValidator.checkCoordinate(
        this.state.positionTmp.latitude,
      ),
      longitudeError: !SubmissionFormValidator.checkCoordinate(
        this.state.positionTmp.longitude,
      ),
    });
  }

  private isValidForm() {
    return (
      this.state.address.length > 5 &&
      this.state.city.length > 3 &&
      this.state.country.length > 0 &&
      FormValidation.validation('zipCode', this.state.zipCode)
    );
  }

  private compareTheNewAndPreviousAddress(): boolean {
    return (
      this.state.address !== this.previousAddress.address ||
      this.state.zipCode !== this.previousAddress.zipCode ||
      this.state.city !== this.previousAddress.city ||
      this.state.country !== this.previousAddress.country
    );
  }

  private onPhoneNumberChangeHandler = (
    phoneNumber: string,
    isValid: boolean,
  ) => {
    if (isValid)
      this.setState({phoneNumber, phoneNumberError: false}, () => {
        Keyboard.dismiss();
        this.props.raiseUpdates('phoneNumber', phoneNumber);
      });
    else
      this.setState({phoneNumber: undefined, phoneNumberError: true}, () =>
        this.props.raiseUpdates('phoneNumber', undefined),
      );
  };

  private checkFieldError(field, value): boolean {
    return BusinessRegistrationValidator.isNotValid(field, value);
  }

  private getCountryList(): ChoiceType[] {
    const result = [];
    Countries().map(item => {
      result.push({
        label: item.name,
        value: item.code,
      });
    });
    return result;
  }

  private getCategoryList(): ChoiceType[] {
    const result = [];
    BusinessTypes().map(type => {
      result.push({
        label: type.label,
        value: type.value,
      });
    });
    return result;
  }
}

const styles = StyleSheet.create({
  addressContainer: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
