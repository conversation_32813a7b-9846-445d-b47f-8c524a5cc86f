import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../../../../../common/adapters/primaries/icons/baseIcon';

export class IdentityIcon extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 1024
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width} viewBox="0 0 1024 1024">
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G
                    stroke="none"
                    strokeWidth={1}
                    fill="none"
                    fillRule="evenodd"
                >
                    <Circle cx="90.5" cy="90.5" r="90.5" fill="#EEEEEE" />
                    <G fill="none" fillRule="evenodd">
                        <Circle fill="url(#prefix__a)"  cx={90.5} cy={90.5} r={55} />
                        <Circle fill="#EEEEEE" stroke="#EEEEEE"  cx={90.5} cy={90.5} r={35} />
                        <Circle
                            cx={90.5}
                            cy={90.5}
                            r={20}
                            fill="#000"
                            stroke="#000"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
