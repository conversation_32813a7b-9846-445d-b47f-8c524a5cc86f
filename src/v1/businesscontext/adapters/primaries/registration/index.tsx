import {connect, useDispatch, useSelector} from 'react-redux';
import {AppState} from '../../../../configuration/AppState';
import {Business} from '../../../domain/entities/business';
import {updateReduxStoreBusiness} from '../../../usecases/business.action';
import {businessSelector} from '../../../usecases/business.selector';
import {
  loadBusinessBySiren,
  registerBusiness,
} from '../../../usecases/registration/businessRegistration.actions';
import {
  errorRegistrationSelector,
  loadingRegistrationSelector,
  successRegistrationSelector,
} from '../../../usecases/registration/businessRegistration.selectors';
import {BusinessRegistrationContainer} from './businessRegistration.container';
import {faker, http, storage, t, util} from 'src/_helpers';
import {Spinner} from 'src/_components';
import {Platform, View} from 'react-native';
import {getAuth, updateAuthBusiness} from 'src/_reducers';
import {Constants} from 'src/_constants';
import fStorage from '@react-native-firebase/storage';
import {useEffect, useState} from 'react';
import {BusinessBuilder} from 'src/v1/businesscontext/domain/builder/business.builder';

const mapStateToProps = (state: AppState) => ({
  business: businessSelector(state),
  isLoading: loadingRegistrationSelector(state),
  successRegistration: successRegistrationSelector(state),
  loadBusinessBySirenError: errorRegistrationSelector(state),
});

const mapDispatchToProps = dispatch => ({
  loadBusinessBySiren: (siren: string) => dispatch(loadBusinessBySiren(siren)),
  registerBusiness: (
    business: Business,
    professionalID: string,
    frontCardPicture: string,
    backCardPicture: string,
  ) =>
    dispatch(
      registerBusiness(
        business,
        professionalID,
        frontCardPicture,
        backCardPicture,
      ),
    ),
  updateBusiness: (business: Business) =>
    dispatch(updateReduxStoreBusiness(business)),
});
/*export const RegistrationBusiness = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessRegistrationContainer);*/

export const RegistrationBusiness = (props: any) => {
  const dispatch = useDispatch();
  const auth = useSelector(getAuth);

  const [state, setState] = useState({
    loading: true,
    business: undefined,
  });

  const loadBusiness = async () => {
    const user = await storage.get(Constants.USER_INFO_KEY);
    const company = user?.company;

    setState(prevState => ({
      ...prevState,
      loading: false,
      business: company?.uuid
        ? new BusinessBuilder()
            .withId(company.uuid)
            .withName(company.name)
            .withType(company.type)
            .withActivity(company.activity)
            .withAddress(company.location.address)
            .withZipCode(company.location.zipCode)
            .withCity(company.location.city)
            .withCountry(company.location.country)
            .withActive(company.active)
            .withLongitude(Number(company.location.longitude))
            .withLatitude(Number(company.location.latitude))
            .withSiren(company.metas.siren)
            .withCoverImage(company.profile.coverImage)
            .withProfileImage(company.profile.profileImage)
            .withOpeningHours(company.activeDays ?? [])
            .withDescription(company.description)
            .withWebsite(company.contact.website)
            .withEmail(company.contact.email)
            .withPhoneNumber(company.contact.phoneNumber)
            .build()
        : undefined,
    }));
  };

  useEffect(() => {
    loadBusiness();
  }, [auth.business]);

  const createPro = async (
    business: Business,
    idProfessional: string,
    frontCardPicture: string,
    backCardPicture: string,
  ) => {
    try {
      setState(prevState => ({...prevState, business, loading: true}));
      let payload = {...business};
      if (idProfessional?.indexOf('file:/') > -1 || Platform.OS==="ios") {
        if(idProfessional){

        const reference = fStorage().ref(
          `companyPics/${idProfessional.split('/').pop()}`,
        );
        await reference.putFile(idProfessional);
        const url = await reference.getDownloadURL();
        payload = {...payload, _idProfessional: url};
      }
    }

      if (frontCardPicture?.indexOf('file:/') > -1 || Platform.OS==="ios") {
        if(frontCardPicture){

        const reference = fStorage().ref(
          `companyPics/${frontCardPicture.split('/').pop()}`,
        );
        await reference.putFile(frontCardPicture);
        const url = await reference.getDownloadURL();
        payload = {...payload, _frontCardPicture: url};
      }
    }

      if (backCardPicture?.indexOf('file:/') > -1 || Platform.OS==="ios") {
        if(backCardPicture){

        const reference = fStorage().ref(
          `companyPics/${backCardPicture.split('/').pop()}`,
        );
        await reference.putFile(backCardPicture);
        const url = await reference.getDownloadURL();
        payload = {...payload, _backCardPicture: url};
      }
    }
      console.log("PAYLOAD", payload)
      const res = await http.post('/companies/createcompany', payload);
      if (res.data?.uuid) {
        await storage.set(Constants.USER_INFO_KEY, res.data);
        loadBusiness();
        dispatch(updateAuthBusiness(business));
        props.navigation.navigate('business_registration_confirmation');
      } else {
        setState(prevState => ({...prevState, business, loading: false}));
      }
    } catch (error) {
      setState(prevState => ({...prevState, loading: false}));
      util.alert(
        t(
          'Une erreur est survenue lors de la création de votre compte PRO. Merci de reéssayer',
          'An error occured while saving your pro account. Please try again later.',
        ),
      );
    }
  };

  if (state.loading) {
    return (
      <View
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flex: 1,
        }}>
        <Spinner background={'rgba(254, 148, 62, 0.4);'} />
      </View>
    );
  }

  return (
    <BusinessRegistrationContainer
      navigation={props.navigation}
      loadBusinessBySiren={function (siren: string): Business {
        return faker.getApplicationBusiness();
      }}
      loadBusinessBySirenError={''}
      business={state.business}
      isLoading={false}
      successRegistration={false}
      registerBusiness={function (
        business: Business,
        idProfessional: string,
        frontCardPicture: string,
        backCardPicture: string,
      ): void {
        createPro(business, idProfessional, frontCardPicture, backCardPicture);
      }}
      updateBusiness={function (business: Business): void {
        console.log(arguments);
      }}
    />
  );
};
