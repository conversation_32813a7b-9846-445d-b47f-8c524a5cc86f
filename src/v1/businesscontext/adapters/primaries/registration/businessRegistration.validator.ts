import { FormValidation } from '../../../../common/usecases/specifications/formValidation';
import { BusinessRegistrationType } from './businessRegistrationType';

export class BusinessRegistrationValidator {
    static validate(business: BusinessRegistrationType): boolean {
        return (
            (this.isNotEmpty(business.name) && FormValidation.validation('name', business.name))
            && this.isNotEmpty(business.type)
            && this.isNotEmpty(business.backCard)
            && this.isNotEmpty(business.frontCard)
            && this.isNotEmpty(business.idProfessional)
            && (this.isNotEmpty(business.zipCode) && FormValidation.validation('zipCode', business.zipCode))
            && (this.isNotEmpty(business.address) && FormValidation.validation('address', business.address))
            && this.isNotEmpty(business.country)
            && (this.isNotEmpty(business.city) && FormValidation.validation('name', business.city))
            && this.isNotEmpty(business.phoneNumber)
        )
    }

    static isNotEmpty(field: string): boolean {
        return field !== null && field !== '' && field !== undefined
    }

    static isNotValid(field, value): boolean {
        if (field === 'name' || field === 'address' || field === 'zipCode')
            return !this.isNotEmpty(value) || !FormValidation.validation(field, value)
        else
            return !this.isNotEmpty(value)
    }
}
