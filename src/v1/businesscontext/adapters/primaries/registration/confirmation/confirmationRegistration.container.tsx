import React, {PureComponent} from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {DefaultButton} from '../../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import {HeaderTitle} from '../../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../configuration/theme/app.theme';
import {ConfirmationIcon} from './components/confirmation.icon';

interface Props {
  navigation: any;
}

export class ConfirmationRegistrationContainer extends PureComponent<
  Props,
  any
> {
  componentDidMount() {
    /* BackHandler.addEventListener(
      'hardwareBackPress',
      this.onBackButtonPressAndroid,
    );*/
    this.props.navigation.setOptions({
      headerShadowVisible: false,
      headerTitleAlign: 'center',
      title: I18n.getTranslation().business.title_confirm,
      headerTitleStyle: {
        fontSize: 14,
        fontFamily: 'U8 Bold',
        color: '#000000',
      },
      headerStyle: {
        backgroundColor: 'white',
      },
    });
  }

  //onBackButtonPressAndroid = () => true;

  render() {
    return (
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.titleText}>
            {I18n.getTranslation().business.header_confirmation}
          </Text>
          <View style={styles.headerItem}>
            <ConfirmationIcon width={600} />
          </View>
        </View>
        <View style={styles.content}>
          <View style={styles.description}>
            <Text style={styles.descriptionText}>
              {I18n.getTranslation().business.desc_confirm}
            </Text>
          </View>
          <View style={styles.description}>
            <Text style={styles.trialText}>
              {I18n.getTranslation().business.trial_period_disclaimer}
            </Text>
          </View>
          <DefaultButton
            style={{marginVertical: 20, width: '90%'}}
            label={I18n.getTranslation().business.manageProfile}
            onPress={() => this.props.navigation.navigate('business_edition')}
          />
          <View>
            <View style={styles.agreeText}>
              <Text style={styles.descriptionText}>
                {I18n.getTranslation().business.desc_bottom}
              </Text>
              <TouchableWithoutFeedback
                onPress={() =>
                  Linking.openURL('mailto:<EMAIL> ')
                }>
                <Text style={[styles.text, {color: Theme.magenta}]}>
                  {I18n.getTranslation().business.desc_mail}
                </Text>
              </TouchableWithoutFeedback>
            </View>
            <View style={styles.agreeText}>
              <Text style={styles.descriptionText}>
                {I18n.getTranslation().business.info_bottom}
              </Text>
              <TouchableWithoutFeedback
                onPress={() => Linking.openURL('www.sportaabe.com')}>
                <Text style={[styles.text, {color: Theme.magenta}]}>
                  {I18n.getTranslation().business.info_mail}
                </Text>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </View>
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
    paddingTop: 20,
  },
  header: {
    flex: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerItem: {
    width: 120,
    height: 120,
    alignSelf: 'center',
    paddingTop: 10,
    marginVertical: 20,
  },
  content: {
    flex: 5,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  description: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginVertical: 10,
    width: '90%',
  },
  descriptionText: {
    textAlign: 'center',
    color: 'black',
    fontSize: 14,
    fontFamily: 'U8 Regular',
    lineHeight: 18,
  },
  trialText: {
    textAlign: 'center',
    color: 'green',
    fontSize: 16,
    fontFamily: 'U8 Bold',
    lineHeight: 18,
  },
  titleText: {
    width: '80%',
    textAlign: 'center',
    color: 'black',
    fontSize: 14,
    lineHeight: 18,
    flexWrap: 'wrap',
    fontFamily: 'U8 Bold',
  },
  button: {
    backgroundColor: 'transparent',
    borderRadius: 5,
    width: '90%',
    alignSelf: 'center',
    paddingVertical: 10,
    marginVertical: 20,
    height: 60,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'black',
    borderWidth: 2,
    borderStyle: 'solid',
  },
  textButton: {
    color: 'black',
    textAlign: 'center',
    fontSize: 15,
    paddingVertical: 10,
    fontFamily: 'U8 Bold',
  },
  text: {
    color: 'black',
    fontSize: 14,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
  },
  agreeText: {
    width: '98%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
});
