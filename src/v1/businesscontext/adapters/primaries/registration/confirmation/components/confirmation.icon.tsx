import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../../../../../../common/adapters/primaries/icons/baseIcon';

export class ConfirmationIcon extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 1024
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width} viewBox="0 0 1024 1024">
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G
                    transform="translate(-97.000000, -85.000000)"
                    stroke="none"
                    strokeWidth={1}
                    fill="none"
                    fillRule="evenodd"
                >
                    <Circle
                        cx="90.5"
                        cy="90.5"
                        r="90.5"
                        transform="translate(97.000000, 85.000000)"
                        fill="#EEEEEE"
                    />
                    <G transform="translate(147.000000, 130.000000)">
            <Path
                d="M78.737 34.497c0-.264 0-.264 0 0-.264-.527-.264-.79-.527-.79l-.527-.527h-.263c-.263 0-.527-.263-.79-.263h-9.217V2.633C67.413 1.053 66.36 0 64.78 0H14.483c-1.58 0-2.633 1.053-2.633 2.633v30.02H2.633 2.37c-.263 0-.527 0-.79.264h-.263c-.264 0-.527.263-.527.526-.263.264-.263.527-.527.527v.263C0 34.76 0 35.023 0 35.287v41.08C0 77.947 1.053 79 2.633 79h73.734C77.947 79 79 77.947 79 76.367v-41.08c0-.264 0-.527-.263-.79zM68.203 37.92l-1.053.79v-.79h1.053zM17.117 5.267H61.62v37.13l-22.383 15.8-22.384-15.8V5.267h.264zM11.85 38.71l-1.053-.79h1.053v.79zm61.883 35.023H5.267v-33.18l7.9 5.53L37.92 63.727c.79.526 2.107.526 3.16 0l24.753-17.644 7.9-5.53v33.18z"
                fill="url(#prefix__a)"
            />
            <Path
                d="M26.86 17.907h25.28c1.58 0 2.633-1.054 2.633-2.634 0-1.58-1.053-2.633-2.633-2.633H26.86c-1.58 0-2.633 1.053-2.633 2.633s1.053 2.634 2.633 2.634zM26.86 31.073h25.28c1.58 0 2.633-1.053 2.633-2.633s-1.053-2.633-2.633-2.633H26.86c-1.58 0-2.633 1.053-2.633 2.633s1.053 2.633 2.633 2.633zM26.86 44.24h25.28c1.58 0 2.633-1.053 2.633-2.633s-1.053-2.634-2.633-2.634H26.86c-1.58 0-2.633 1.054-2.633 2.634 0 1.316 1.053 2.633 2.633 2.633z"
                fill="url(#prefix__a)"
            />
        </G>
                </G>
    </Svg>
        )
    }
}
