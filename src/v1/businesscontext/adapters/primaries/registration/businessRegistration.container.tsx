import React, {PureComponent} from 'react';
import {BackHandler, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {DefaultButton} from '../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../configuration/i18n/i18n';
import {BusinessBuilder} from '../../../domain/builder/business.builder';
import {Business} from '../../../domain/entities/business';
import {BusinessRegistrationValidator} from './businessRegistration.validator';
import {BusinessForm} from './component/business.form';
import {HeaderForm} from './component/header.form';
import {IdentityIcon} from './component/identity.icon';
import {TermsAndCondition} from './component/termsAndCondition.presentational';
import {util} from 'src/_helpers';
import { Coordinates } from 'src/v1/common/domain/entities/Coordinates';

interface Props {
  navigation: any;
  loadBusinessBySiren: (siren: string) => Business;
  loadBusinessBySirenError: string;
  business: Business;
  isLoading: boolean;
  successRegistration: boolean;
  registerBusiness: (
    business: Business,
    idProfessional: string,
    frontCardPicture: string,
    backCardPicture: string,
  ) => void;
  updateBusiness: (business: Business) => void;
}

interface State {
  name: string;
  type: string;
  idProfessional: string;
  frontCard: string;
  backCard: string;
  address: string;
  city: string;
  zipCode: string;
  country: string;
  coordinates: Coordinates;
  phoneNumber: string;
  isFormSubmitted: boolean;
}

export class BusinessRegistrationContainer extends PureComponent<Props, State> {
  private backHandler: any;

  constructor(props) {
    super(props);
    this.state = {
      name: undefined,
      type: undefined,
      idProfessional: undefined,
      frontCard: undefined,
      backCard: undefined,
      address: undefined,
      city: undefined,
      zipCode: undefined,
      country: "CM",
      coordinates: undefined,
      phoneNumber: undefined,
      isFormSubmitted: false,
    };
  }

  static navigationOptions = ({navigation}) => {
    return {
      headerTitle: (
        <HeaderTitle
          title={I18n.getTranslation().business.header_authentication}
        />
      ),
      headerRight: <View />,
      headerLeft: (
        <HeaderButtonIcon
          CTA={() => navigation.goBack()}
          iconName={'angle-left'}
          color={'black'}
        />
      ),
      headerStyle: {
        backgroundColor: 'white',
        borderBottomColor: 'white',
        elevation: 0,
        shadowOpacity: 0,
        height: 40,
      },
    };
  };

  componentDidMount() {
    this.backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      this.onBackButtonPressAndroid,
    );
    //this.props.navigation.setParams({ backBtn: this.goBack });
  }

  onBackButtonPressAndroid = () => {
    if (this.props.navigation.isFocused()) return true;
    else return false;
  };

  componentWillUnmount() {
    this.backHandler.remove();
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.successRegistration === true &&
      this.props.successRegistration !== prevProps.successRegistration
    )
      this.props.navigation.navigate('business_registration_confirmation');
  }

  render() {
    const loading = this.props.isLoading ? (
      <Spinner background={'rgba(0,0,0,0);'} />
    ) : null;
    return (
      <KeyboardAwareScrollView style={styles.container}>
        {loading}
        <HeaderForm description={I18n.getTranslation().business.desc_identity}>
          <IdentityIcon width={600} />
        </HeaderForm>
        <BusinessForm
          raiseUpdates={(key, value) => {
            this.setState({...this.state, [key]: value});
          }
          }
          isFormSubmitted={this.state.isFormSubmitted}
          business={this.props.business}
        />
        <TermsAndCondition
          cgu={'https://sportaabe.com/condition-general-utilisation/'}
          privacy={'https://sportaabe.com/politique-de-confidentialite/'}
        />
        <DefaultButton
          style={{marginBottom: 10}}
          label={I18n.getTranslation().business.signup_pro}
          onPress={() => this.handlePress()}
        />
      </KeyboardAwareScrollView>
    );
  }

  async handlePress() {
    this.setState({isFormSubmitted: true});
    // if (BusinessRegistrationValidator.validate(this.state)) {

    const business = new BusinessBuilder()
      .withName(this.state.name)
      .withType(this.state.type)
      .withAddress(this.state.address)
      .withCity(this.state.city)
      .withCountry(this.state.country)
      .withZipCode(this.state.zipCode)
      .withActive(true)
      .withPhoneNumber(this.state.phoneNumber)
      .withMembership(Business.FREEMIUM);

    if (this.state.coordinates) {
      business
        .withLatitude(this.state.coordinates.latitude)
        .withLongitude(this.state.coordinates.longitude);
    } else {
      const res = await util.geocodeAddress(
        this.state.zipCode,
        this.state.city,
        this.state.country,
        this.state.address,
      );
  
      if (res?.latitude && res?.longitude) {
        business
          .withLatitude(res.latitude)
          .withLongitude(res.longitude);
      }
    }

    this.props.registerBusiness(
      business.build(),
      this.state.idProfessional,
      this.state.frontCard,
      this.state.backCard,
    );
    //}
  }

  goBack = () => {
    this.props.updateBusiness(null);
    this.props.navigation.goBack();
  };
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
    paddingHorizontal: 10,
    paddingBottom: 20,
  },
  header: {
    flex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerItem: {
    width: 120,
    height: 120,
    alignSelf: 'center',
  },
  content: {
    flex: 6,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
