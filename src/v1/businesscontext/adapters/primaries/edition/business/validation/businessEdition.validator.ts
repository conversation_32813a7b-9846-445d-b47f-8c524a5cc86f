import { I18n } from '../../../../../../configuration/i18n/i18n';
import { BusinessType } from '../businessEdition.types';

export class BusinessEditionValidator {

    static validate(business: BusinessType): number[] {
       if (!this.checkName(business.name) || !this.checkProfileImage(business.profileImage) || !this.checkActivity(business.activity))
            return [0]
        else if (!this.checkAddress(business.address.address) ||
            !this.checkCity(business.address.city) ||
            !this.checkCountry(business.address.country) ||
            !this.checkZipCode(business.address.zipCode))
            return [1]
        else if (!this.checkDescription(business.contacts.description) || !this.checkWebsite(business.contacts.website)
            || !this.checkEmail(business.contacts.email) || !this.checkPhoneNumber(business.contacts.phoneNumber))
            return [3]
        else return null
    }

    static checkName(name: string): boolean {
        return name && name.length > 2
    }

    static checkProfileImage(profileImage: string): boolean {
        return (profileImage !== undefined && profileImage !== null && profileImage !== '')
    }

    static checkActivity(activity: string): boolean {
        return (activity !== undefined && activity !== null && activity !== '')
    }

    static checkAddress(address: string): boolean {
        return address !== undefined && address !== null && address.length > 5
    }

    static checkCity(city: string): boolean {
        return city !== undefined && city !== null && city.length > 2
    }

    static checkCountry(country: string): boolean {
        return country !== undefined && country !== null && country.length > 1
    }

    static checkZipCode(zipCode: string): boolean {
        const regex = /^[a-zA-Z0-9]{4,6}$/;
        return zipCode !== null && regex.test(zipCode)
    }

    static checkDescription(description: string): boolean {
        return description && description.length > 5 && description !== I18n.getTranslation().business.description_default
    }

    static checkWebsite(website: string): boolean {
        const regex = /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
        if (website && website.length > 0)
            return regex.test(website)
        return true
    }

    static checkWww(website: string): boolean {
        const regex = /^www\.[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
        if (website && website.length > 0)
            return regex.test(website)
        return true
    }

    static checkEmail(email: string): boolean {
        const regex = new RegExp(['^(([^<>()[\\]\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\.,;:\\s@\"]+)*)',
            '|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.',
            '[0-9]{1,3}\])|(([a-zA-Z\\-0-9]+\\.)+',
            '[a-zA-Z]{2,}))$'].join(''));
        if (email && email.length > 0)
            return regex.test(email.toLowerCase())
        return false
    }

    static checkPhoneNumber(phoneNumber: string): boolean {
        return phoneNumber !== undefined && phoneNumber.length > 0
    }
}
