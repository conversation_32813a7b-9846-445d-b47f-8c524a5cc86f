import {connect, useDispatch, useSelector} from 'react-redux';
import {AppState} from '../../../../../configuration/AppState';
import {Business} from '../../../../domain/entities/business';
import {businessSelector} from '../../../../usecases/business.selector';
import {editBusiness} from '../../../../usecases/edition/businessEdition.actions';
import {
  editingBusinessSelector,
  errorEditingBusinessSelector,
  successEditingBusinessSelector,
} from '../../../../usecases/edition/businessEdition.selectors';
import {loadRemoteBusiness} from '../../../../usecases/remoteLoading/loadRemoteBusiness.actions';
import {
  loadingRemoteBusinessSelector,
  remoteBusinessSelector,
} from '../../../../usecases/remoteLoading/loadRemoteBusiness.selectors';
import {BusinessEditionContainer} from './businessEdition.container';
import {http, storage, util} from 'src/_helpers';
import {useEffect, useState} from 'react';
import {BusinessBuilder} from 'src/v1/businesscontext/domain/builder/business.builder';
import {Constants} from 'src/_constants';
import {Spinner} from 'src/_components';
import {Platform, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {updateAuthBusiness} from 'src/_reducers';
import fStorage from '@react-native-firebase/storage';

import { useQuery, useQueryClient } from '@tanstack/react-query';

const mapStateToProps = (appState: AppState) => ({
  business: remoteBusinessSelector(appState),
  loading:
    loadingRemoteBusinessSelector(appState) ||
    editingBusinessSelector(appState),
  success: successEditingBusinessSelector(appState),
  error: errorEditingBusinessSelector(appState),
  businessId: businessSelector(appState).id,
});

const mapDispatchToProps = dispatch => ({
  editBusiness: (business: Business) => dispatch(editBusiness(business)),
  loadBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
});

/*export const BusinessEditionPage = connect(
  mapStateToProps,
  mapDispatchToProps
)(BusinessEditionContainer);*/

export const BusinessEditionPage = (props: any) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [state, setState] = useState({
    loading: true,
    business: undefined,
  });
  const queryClient = useQueryClient();

  const loadBusiness = async () => {
    const user = await storage.get(Constants.USER_INFO_KEY);
    const company = user?.company;

    setState(prevState => ({
      ...prevState,
      loading: false,
      business: company?.uuid
        ? new BusinessBuilder()
            .withId(company.uuid)
            .withName(company.name)
            .withType(company.type)
            .withActivity(company.activity)
            .withAddress(company.location.address)
            .withZipCode(company.location.zipCode)
            .withCity(company.location.city)
            .withCountry(company.location.country)
            .withActive(company.active)
            .withLongitude(Number(company.location.longitude))
            .withLatitude(Number(company.location.latitude))
            .withSiren(company.metas.siren)
            .withCoverImage(company.profile.coverImage)
            .withProfileImage(company.profile.profileImage)
            .withOpeningHours(company.activeDays ?? [])
            .withDescription(company.description)
            .withWebsite(company.contact.website)
            .withEmail(company.contact.email)
            .withPhoneNumber(company.contact.phoneNumber)
            .build()
        : undefined,
    }));
  };

  useEffect(() => {
    loadBusiness();
  }, []);

  useEffect(() => {
    if (state.business) {
      dispatch(updateAuthBusiness(state.business));
    }
  }, [state.business]);

  return state.loading ? (
    <View
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
      }}>
      <Spinner background={'rgba(254, 148, 62, 0.4);'} />
    </View>
  ) : (
    <BusinessEditionContainer
      navigation={props.navigation}
      business={state.business}
      loading={false}
      success={false}
      error={''}
      businessId={state.business?.id}
      loadBusiness={function (businessId: string): void {}}
      editBusiness={async function (
          business: Business,
          idProfessional: string,
          frontCardPicture: string,
          backCardPicture: string
        ) {
        console.log(idProfessional, frontCardPicture, backCardPicture);
        setState(prevState => ({...prevState, business, loading: true}));
        if (business.profileImage?.indexOf('file:/') > -1 || Platform.OS==='ios') {
          try{
          const reference = fStorage().ref(
            `companyPics/${business.profileImage.split('/').pop()}`,
          );
          await reference.putFile(business.profileImage);
          const url = await reference.getDownloadURL();
          business.profileImage = url;
          }catch(error){}
        }console.log("IMAGE PRO avant", business.coverImage);
        if (business.coverImage?.indexOf('file:/') > -1 || Platform.OS==='ios') {
          try{

          
          const reference = fStorage().ref(
            `companyPics/${business.coverImage.split('/').pop()}`,
          );
          await reference.putFile(business.coverImage);
          const url = await reference.getDownloadURL();
          business.coverImage = url;
          console.log("IMAGE PRO", business.coverImage);
        } catch (error) {}
        }

        let payload = {...business};
        if (idProfessional?.indexOf('file:/') > -1 || Platform.OS==='ios') {
          try{
          const reference = fStorage().ref(
            `companyPics/${idProfessional.split('/').pop()}`,
          );
          await reference.putFile(idProfessional);
          const url = await reference.getDownloadURL();
          payload = {...payload, _idProfessional: url};
          }catch(error){}
        }
        if (frontCardPicture?.indexOf('file:/') > -1 || Platform.OS==='ios') {
          try{

          
          const reference = fStorage().ref(
            `companyPics/${frontCardPicture.split('/').pop()}`,
          );
          await reference.putFile(frontCardPicture);
          const url = await reference.getDownloadURL();
          payload = {...payload, _frontCardPicture: url};}
          catch(error){}
        }
        if (backCardPicture?.indexOf('file:/') > -1 || Platform.OS==='ios') {
          try{
          const reference = fStorage().ref(
            `companyPics/${backCardPicture.split('/').pop()}`,
          );
          await reference.putFile(backCardPicture);
          const url = await reference.getDownloadURL();
          payload = {...payload, _backCardPicture: url};
          }catch(error){}
        }

        const res = await http.post('/companies/updatecompany', payload);
        if (res.data?.uuid) {
          await storage.set(Constants.USER_INFO_KEY, res.data);
          //loadBusiness().then(() => navigation.goBack());
          dispatch(updateAuthBusiness(business));
          queryClient.invalidateQueries({
            queryKey: ['business', business.id],
          });
          navigation.goBack();
        }
        setState(prevState => ({...prevState, business, loading: false}));
      }}
    />
  );
};
