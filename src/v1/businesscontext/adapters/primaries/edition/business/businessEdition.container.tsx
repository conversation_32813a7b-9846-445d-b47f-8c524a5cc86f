import React, {PureComponent} from 'react';
import {StyleSheet} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ApplicationToast} from '../../../../../common/adapters/primaries/components/application.toast';
import {Spinner} from '../../../../../common/adapters/primaries/components/spinner.presentational';

import {I18n} from '../../../../../configuration/i18n/i18n';
import {ActiveDaysNames} from '../../../../../configuration/setting/app.settings';
import {BusinessBuilder} from '../../../../domain/builder/business.builder';
import {Business} from '../../../../domain/entities/business';
import {BusinessOpeningHours} from '../../../../domain/entities/businessOpeningHour';
import {
  BusinessAddressType,
  BusinessContactType,
  BusinessOpeningHoursType,
  BusinessType,
} from './businessEdition.types';
import {BusinessEditionAccordion} from './components/businessEdition.accordion';
import {BusinessEditionValidator} from './validation/businessEdition.validator';
import {HeaderButtonText} from 'src/_components';

interface Props {
  navigation: any;
  business: Business;
  loading: boolean;
  success: boolean;
  error: string;
  businessId: string;
  loadBusiness: (businessId: string) => void;
  editBusiness: (
    business: Business,
    idProfessional: string,
    frontCardPicture: string,
    backCardPicture: string,
  ) => void;
}

interface State {
  id: string;
  name: string;
  type: string;
  idProfessional: string;
  frontCard: string;
  backCard: string;
  activity: string;
  coverImage: string;
  profileImage: string;
  openingHours: BusinessOpeningHoursType[];
  contacts: BusinessContactType;
  address: BusinessAddressType;
  activeSection: number[];
  hasError: boolean;
}

export class BusinessEditionContainer extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      id: undefined,
      name: undefined,
      type: '',
      idProfessional: undefined,
      frontCard: undefined,
      backCard: undefined,
      activity: '',
      coverImage: '',
      profileImage: '',
      openingHours: [],
      contacts: null,
      address: null,
      activeSection: [0],
      hasError: null,
    };
  }

  static getDerivedStateFromProps(props, state) {
    if (!props.business && props.loading === false)
      props.loadBusiness(props.businessId);
    if (props.business && state.id === undefined)
      return {
        id: props.business.id,
        name: props.business.name,
        type: props.business.type,
        idProfessional: props.business.idProfessional,
        frontCard: props.business.frontCard,
        backCard: props.business.backCard,
        activity: props.business.activity,
        coverImage: props.business.coverImage,
        profileImage: props.business.profileImage,
        contacts: {
          description: props.business.contacts.description,
          website: props.business.contacts.website,
          email: props.business.contacts.email,
          phoneNumber: props.business.contacts.phoneNumber,
        },
        address: {
          address: props.business.address.address,
          zipCode: props.business.address.zipCode,
          city: props.business.address.city,
          country: props.business.address.country,
          longitude: props.business.address.longitude,
          latitude: props.business.address.latitude,
        },
        openingHours: BusinessEditionContainer.getOpeningHours(
          props.business.openingHours,
        ),
      };
    return null;
  }

  static getOpeningHours(
    openingHours: BusinessOpeningHours[],
  ): BusinessOpeningHoursType[] {
    const businessHours: BusinessOpeningHoursType[] = [];
    openingHours.map(item =>
      businessHours.push({
        id: item.id.toString(),
        openingDays: {
          startTime: item.startTime,
          endTime: item.endTime,
          key: item.openingDays,
          label: ActiveDaysNames[item.openingDays],
        },
        isPaused: item.isPaused,
      }),
    );
    return businessHours;
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderButtonText
          paddingRight={0}
          CTA={this.updateBusiness}
          title={I18n.getTranslation().instant.ok}
          iconName={''}
        />
      ),
    });
  }

  componentDidUpdate(prevProps) {
    if (this.props.success === true && this.props.success !== prevProps.success)
      this.props.navigation.navigate('business_owner_preview');
    if (this.props.error && this.props.error !== prevProps.error)
      ApplicationToast.show(
        I18n.getTranslation().business.error_business_edition,
      );
  }

  render() {
    const spinner =
      this.props.loading === true ? (
        <Spinner background={'rgba(0,0,0,0.3)'} />
      ) : null;
    return (
      <KeyboardAwareScrollView contentContainerStyle={styles.container}>
        {spinner}
        <BusinessEditionAccordion
          activeSection={this.state.activeSection}
          business={this.state}
          onChange={(key, value) => this.setState({...this.state, [key]: value})}
        />
      </KeyboardAwareScrollView>
    );
  }

  buildNewBusiness(business: BusinessType): Business {
    return new BusinessBuilder()
      .withId(business.id)
      .withName(business.name)
      .withType(business.type)
      .withActivity(business.activity)
      .withAddress(business.address.address)
      .withZipCode(business.address.zipCode && business.address.zipCode.trim() !== '' ? business.address.zipCode : '0000')
      .withCity(business.address.city)
      .withCountry(business.address.country)
      .withLongitude(business.address.longitude)
      .withLatitude(business.address.latitude)
      .withSiren(this.props.business.siren)
      .withCoverImage(business.coverImage)
      .withProfileImage(business.profileImage)
      .withOpeningHours(business.openingHours)
      .withDescription(business.contacts.description)
      .withWebsite(business.contacts.website)
      .withEmail(business.contacts.email)
      .withPhoneNumber(business.contacts.phoneNumber)
      .build();
  }

  updateBusiness = () => {
    /* const validation = BusinessEditionValidator.validate(this.state);
    if (validation !== null)
      this.setState({
        activeSection: validation,
        hasError: true,
      });
    else {*/
    this.setState({
      hasError: null,
    });
    this.props.editBusiness(
      this.buildNewBusiness(this.state),
      this.state.idProfessional,
      this.state.frontCard,
      this.state.backCard
    );
    //}
  };
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: '100%',
    backgroundColor: 'white',
  },
});
