import React, { PureComponent } from "react";
import { StyleSheet, View } from "react-native";
import { ScheduleForm } from "../../../../../../../common/adapters/primaries/components/form/businesshours/schedule.form";
import { SchedulePresentational } from "../../../../../../../common/adapters/primaries/components/form/businesshours/schedule.presentational";
import { DefaultButton } from "../../../../../../../common/adapters/primaries/components/form/fields/defaultButton.field";
import {
  ChoiceType,
  ScheduleType,
} from "../../../../../../../common/domain/entities/types/AppTypes";
import { I18n } from "../../../../../../../configuration/i18n/i18n";
import { ActiveDaysNames } from "../../../../../../../configuration/setting/app.settings";
import {
  BusinessOpeningHoursType,
  BusinessType,
} from "../../businessEdition.types";

interface Props {
  raiseUpdates: (value: BusinessOpeningHoursType[]) => void;
  business: BusinessType;
}

interface State {
  choiceOption: ChoiceType[];
  displayForm: boolean;
  schedules: ScheduleType[];
}

export class BusinessHoursEdition extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      choiceOption: this.generateInstantCategories(),
      displayForm: false,
      schedules: [],
    };
  }

  componentDidMount() {
    if (this.props.business && this.props.business.openingHours.length > 0) {
      const schedules: ScheduleType[] = [];
      this.props.business.openingHours.map((item, index) => {
        schedules.push({
          id: item.id,
          dateRange: {
            key: item.openingDays.key,
            label: item.openingDays.label,
            startTime: item.openingDays.startTime,
            endTime: item.openingDays.endTime,
          },
          isPaused: item.isPaused,
          prePoned: false,
        });
      });
      this.setState({ schedules });
    }
  }

  render() {
    const recurrentPreviews = this.state.schedules.map(
      (schedule: ScheduleType) => {
        return (
          <SchedulePresentational
            onPause={() => this.toggleScheduleStatus(schedule)}
            onRemove={() => this.removeSchedule(schedule)}
            style={{ marginBottom: 10 }}
            schedule={schedule}
            key={schedule.id}
          />
        );
      }
    );

    const addScheduleButton = !this.state.displayForm ? (
      <DefaultButton
        onPress={() => this.setState({ displayForm: true })}
        label={I18n.getTranslation().instant_submission.add_schedule}
      />
    ) : null;
    return (
      <View style={styles.container}>
        {recurrentPreviews}
        <ScheduleForm
          onSubmit={(schedule: ScheduleType) => this.addSchedule(schedule)}
          show={this.state.displayForm}
        />
        {addScheduleButton}
      </View>
    );
  }

  addSchedule(schedule: ScheduleType) {
    this.setState(
      {
        schedules: [...this.state.schedules, schedule],
        displayForm: false,
      },
      () => this.cleanAndUpdate()
    );
  }

  removeSchedule(schedule: ScheduleType) {
    this.setState(
      {
        schedules: this.state.schedules.filter(
          (item) => item.id !== schedule.id
        ),
      },
      () => this.cleanAndUpdate()
    );
  }

  toggleScheduleStatus(schedule: ScheduleType) {
    this.setState(
      {
        schedules: this.state.schedules.map((item) => {
          if (item.id === schedule.id)
            return { ...item, isPaused: !schedule.isPaused };
          return item;
        }),
      },
      () => this.cleanAndUpdate()
    );
  }

  generateOpeningHours = (
    schedules: ScheduleType[]
  ): BusinessOpeningHoursType[] => {
    const newOpeningHours: BusinessOpeningHoursType[] = [];
    schedules.map((item) => {
      newOpeningHours.push({
        id: item.id.toString(),
        openingDays: {
          key: item.dateRange.key,
          label: item.dateRange.label,
          startTime: item.dateRange.startTime,
          endTime: item.dateRange.endTime,
        },
        isPaused: item.isPaused,
      });
    });
    return newOpeningHours;
  };
  generateInstantCategories = (): ChoiceType[] => {
    const result = [];
    for (const key in ActiveDaysNames)
      if (ActiveDaysNames.hasOwnProperty(key))
        result.push({
          value: key,
          label: ActiveDaysNames[key],
        });

    return result;
  };
  private cleanAndUpdate() {
    const cleanedSchedules = this.state.schedules.map((item) => {
      if (this.isUUID(item.id)) return { ...item, id: "" };
      return item;
    });
    this.props.raiseUpdates(this.generateOpeningHours(cleanedSchedules));
  }

  private isUUID(id: string) {
    const regexp = new RegExp(
      /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i
    );
    return regexp.test(id);
  }
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    backgroundColor: "white",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    width: "100%",
  },
});
