import React, {PureComponent} from 'react';
import {<PERSON><PERSON><PERSON>iew} from 'react-native';
import {DefaultTextInput} from '../../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import {ChoicePickerContainer} from '../../../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import {CustomizedCameraContainer} from '../../../../../../../common/adapters/primaries/components/form/pickers/customizedCamera.container';
import {PicturePickerContainer} from '../../../../../../../common/adapters/primaries/components/form/pickers/picturePicker.container';
import {ChoiceType} from '../../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {Categories} from '../../../../../../../configuration/setting/app.settings';
import {ThemeRGBA} from '../../../../../../../configuration/theme/app.theme';
import {BusinessTypes} from '../../../../../../configuration/registration/settings';
import {
  BusinessEditionFormKey,
  BusinessType,
} from '../../businessEdition.types';
import {BusinessEditionValidator} from '../../validation/businessEdition.validator';

interface Props {
  raiseUpdates: (key: BusinessEditionFormKey, value: string) => void;
  business: BusinessType;
}

interface State {
  profileImage: string;
  coverImage: string;
  name: string;
  type: string;
  activity: string;
  nameError: boolean;
  imageError: boolean;
  activityError: boolean;
}

export class BusinessBasicInfoEdition extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      profileImage: undefined,
      coverImage: undefined,
      name: undefined,
      type: undefined,
      activity: undefined,
      nameError: null,
      imageError: null,
      activityError: null,
    };
  }

  static getDerivedStateFromProps(props, state) {
    return {
      profileImage: props.business.profileImage,
      coverImage: props.business.coverImage,
      name: props.business.name,
      type: props.business.type,
      activity: props.business.activity,
    };
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.business.hasError !== this.props.business.hasError &&
      this.props.business.hasError === true
    )
      this.setState({
        imageError: !BusinessEditionValidator.checkProfileImage(
          this.state.profileImage,
        ),
        nameError: !BusinessEditionValidator.checkName(this.state.name),
        activityError: !BusinessEditionValidator.checkActivity(
          this.state.activity,
        ),
      });
  }

  render() {
    return (
      <ScrollView>
        <CustomizedCameraContainer
          style={{marginTop: 10}}
          name={I18n.getTranslation().business.profile_picture}
          imagePlaceholder={this.state.profileImage}
          error={this.state.imageError ? true : null}
          onPictureTaken={imageURI => {
            this.props.raiseUpdates('profileImage', imageURI);
            this.setState({
              imageError: !BusinessEditionValidator.checkProfileImage(imageURI),
            });
          }}
        />
        <CustomizedCameraContainer
          style={{marginTop: 10}}
          name={I18n.getTranslation().business.cover_picture}
          imagePlaceholder={this.state.coverImage}
          onPictureTaken={imageURI => {
            this.props.raiseUpdates('coverImage', imageURI);
          }}
        />

        <DefaultTextInput
          style={{marginTop: 10}}
          placeholder={I18n.getTranslation().business.store_name + ' (' + I18n.getTranslation().common.required_message + ")"}
          value={this.state.name}
          error={this.state.nameError ? true : null}
          onChange={name => {
            this.props.raiseUpdates('name', name);
            this.setState({
              nameError: !BusinessEditionValidator.checkName(name),
            });
          }}
        />
        <ChoicePickerContainer
          placeholder={I18n.getTranslation().business.type + ' (' + I18n.getTranslation().common.required_message + ")"}
          options={this.getBusinessTypeListe()}
          style={{marginTop: 10}}
          color={ThemeRGBA.black}
          value={this.state.type}
          onChange={type => this.props.raiseUpdates('type', type.value)}
        />
        <ChoicePickerContainer
          placeholder={I18n.getTranslation().business.activity_placeholder + ' (' + I18n.getTranslation().common.required_message + ")"}
          options={this.getListeActivities()}
          style={{marginTop: 10}}
          error={this.state.activityError ? true : null}
          color={ThemeRGBA.black}
          value={this.state.activity}
          onChange={activity => {
            this.props.raiseUpdates('activity', activity.value);
            this.setState({
              activityError: !BusinessEditionValidator.checkActivity(
                activity.value,
              ),
            });
          }}
        />
      </ScrollView>
    );
  }

  getBusinessTypeListe(): ChoiceType[] {
    const result = [];
    BusinessTypes().map((type, index) => {
      result.push({
        label: type.label,
        value: type.value,
      });
    });
    return result;
  }

  private getListeActivities(): ChoiceType[] {
    const result = [];
    for (const iconName in Categories)
      if (Categories.hasOwnProperty(iconName))
        result.push({
          value: iconName,
          label: I18n.getTranslation().common.settings.categories[iconName],
          iconName,
        });
    return result;
  }
}
