import React, {PureComponent} from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import Accordion from 'react-native-collapsible/Accordion';
import {AccordionHeader} from '../../../../../../common/adapters/primaries/components/accordion/accordionHeader.presentational';
import {AccordionType} from '../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {BusinessEditionFormKey, BusinessType} from '../businessEdition.types';
import {BusinessAddressEdition} from './sections/address';
import {BusinessBasicInfoEdition} from './sections/businessBasicInfoEdition';
import {BusinessContactEdition} from './sections/businessContactEdition';
import {BusinessHoursEdition} from './sections/businessHoursEdition';
import {BusinessIdentityPicturesEdition} from './sections/businessIdentityPicturesEdition';

interface Props {
  onChange: (key: BusinessEditionFormKey, value) => void;
  business: BusinessType;
  activeSection: number[];
}

interface State {
  activeSection: number[];
  sections: AccordionType[];
}

export class BusinessEditionAccordion extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      activeSection: this.props.activeSection,
      sections: [],
    };
  }

  static getDerivedStateFromProps(props) {
    return {
      sections: [
        {
          rank: 0,
          title: I18n.getTranslation().business.basic_information,
          subTitle: I18n.getTranslation().business.basic_information_sub_title,
          content: (
            <BusinessBasicInfoEdition
              raiseUpdates={(key, value) => props.onChange(key, value)}
              business={props.business}
            />
          ),
        },
        {
          rank: 1,
          title: I18n.getTranslation().business.location,
          subTitle: I18n.getTranslation().business.location_sub_title,
          content: (
            <BusinessAddressEdition
              raiseUpdates={value => props.onChange('address', value)}
              business={props.business}
            />
          ),
        },
        {
          rank: 2,
          title: I18n.getTranslation().business.opening_hours,
          subTitle: I18n.getTranslation().business.opening_hours_sub_title,
          content: (
            <BusinessHoursEdition
              raiseUpdates={value => props.onChange('openingHours', value)}
              business={props.business}
            />
          ),
        },
        {
          rank: 3,
          title: I18n.getTranslation().business.contacts_info,
          subTitle: I18n.getTranslation().business.contacts_info_sub_title,
          content: (
            <BusinessContactEdition
              raiseUpdates={value => props.onChange('contacts', value)}
              business={props.business}
            />
          ),
        },
        {
          rank: 4,
          title: I18n.getTranslation().business.id_pictures,
          subTitle: I18n.getTranslation().business.id_pictures_sub_title,
          content: (
            <BusinessIdentityPicturesEdition
              raiseUpdates={(key ,value) => props.onChange(key ,value)}
              business={props.business}
            />
          ),
        },
      ],
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.activeSection !== this.props.activeSection)
      this.setState({activeSection: this.props.activeSection});
  }

  render() {
    return (
      <ScrollView contentContainerStyle={styles.scrollView}>
        <Accordion
          activeSections={this.state.activeSection}
          sections={this.state.sections}
          containerStyle={{width: '96%', backgroundColor: 'white'}}
          renderHeader={this.renderHeader}
          renderContent={this.renderContent}
          onChange={this.updateSections}
        />
      </ScrollView>
    );
  }

  private renderContent = section => {
    return section.content;
  };

  private renderHeader = section => {
    return (
      <AccordionHeader
        isOpened={this.state.activeSection.includes(section.rank)}
        rank={section.rank}
        title={section.title}
        subTitle={section.subTitle}
      />
    );
  };

  private updateSections = activeSection => {
    this.setState({activeSection});
  };
}

const styles = StyleSheet.create({
  scrollView: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
});
