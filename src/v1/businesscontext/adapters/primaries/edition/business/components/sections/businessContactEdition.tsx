import React, {PureComponent} from 'react';
import {Keyboard, ScrollView} from 'react-native';
import {DefaultTextInput} from '../../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import {PhoneInputField} from '../../../../../../../common/adapters/primaries/components/form/inputs/phone.input';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {BusinessContactType, BusinessType} from '../../businessEdition.types';
import {BusinessEditionValidator} from '../../validation/businessEdition.validator';

interface Props {
  raiseUpdates: (value: BusinessContactType) => void;
  business: BusinessType;
}

interface State {
  description: string;
  website: string;
  email: string;
  phoneNumber: string;
  descriptionError: boolean;
  websiteError: boolean;
  emailError: boolean;
  phoneNumberError: boolean;
}

export class BusinessContactEdition extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      description: undefined,
      website: undefined,
      email: undefined,
      phoneNumber: undefined,
      descriptionError: null,
      websiteError: null,
      emailError: null,
      phoneNumberError: null,
    };
  }

  componentDidMount() {
    if (this.props.business.contacts)
      this.setState({
        description:
          this.props.business.contacts.description !== null
            ? this.props.business.contacts.description
            : I18n.getTranslation().business.description_default,
        website: this.props.business.contacts.website,
        email: this.props.business.contacts.email,
        phoneNumber: this.props.business.contacts.phoneNumber,
      });
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.business.hasError !== this.props.business.hasError &&
      this.props.business.hasError === true
    )
      this.setState({
        descriptionError: !BusinessEditionValidator.checkDescription(
          this.state.description,
        ),
        websiteError: !BusinessEditionValidator.checkWebsite(
          this.state.website,
        ),
        emailError: !BusinessEditionValidator.checkEmail(this.state.email),
        phoneNumberError: !BusinessEditionValidator.checkPhoneNumber(
          this.state.phoneNumber,
        ),
      });
  }

  render() {
    return (
      <ScrollView style={{marginBottom: 20}}>
        <DefaultTextInput
          style={{marginTop: 10}}
          value={this.state.description}
          placeholder={I18n.getTranslation().business.description}
          numberOfLines={15}
          error={this.state.descriptionError ? true : null}
          onChange={description =>
            this.setState(
              {
                description,
                descriptionError:
                  !BusinessEditionValidator.checkDescription(description),
              },
              () => this.props.raiseUpdates(this.state),
            )
          }
        />
        <DefaultTextInput
          style={{marginTop: 10}}
          autoCapitalize={'none'}
          value={this.state.website}
          placeholder={I18n.getTranslation().business.website}
          error={this.state.websiteError ? true : null}
          onChange={website => this.onWebSiteChange(website)}
        />
        <DefaultTextInput
          style={{marginTop: 10}}
          value={this.state.email}
          autoCapitalize={'none'}
          keyboardType={'email-address'}
          placeholder={I18n.getTranslation().business.email + ' (' + I18n.getTranslation().common.required_message + ")"}
          error={this.state.emailError ? true : null}
          onChange={email =>
            this.setState(
              {
                email,
                emailError: !BusinessEditionValidator.checkEmail(email),
              },
              () => this.props.raiseUpdates(this.state),
            )
          }
        />
        {this.state.phoneNumber && (
          <PhoneInputField
            style={{marginTop: 10}}
            placeholder={I18n.getTranslation().business.phone_number}
            value={this.state.phoneNumber ?? ''}
            error={this.state.phoneNumberError ? false : null}
            onChange={(phoneNumber, isValid) =>
              this.onPhoneNumberChangeHandler(phoneNumber, isValid)
            }
          />
        )}
        {!this.state.phoneNumber && (
          <PhoneInputField
            style={{marginTop: 10}}
            placeholder={I18n.getTranslation().business.phone_number}
            value={''}
            error={this.state.phoneNumberError ? false : null}
            onChange={(phoneNumber, isValid) =>
              this.onPhoneNumberChangeHandler(phoneNumber, isValid)
            }
          />
        )}
      </ScrollView>
    );
  }

  onPhoneNumberChangeHandler = (phoneNumber: string, isValid: boolean) => {
    if (isValid)
      this.setState({phoneNumber, phoneNumberError: false}, () => {
        Keyboard.dismiss();
        this.props.raiseUpdates(this.state);
      });
    else
      this.setState({phoneNumber: undefined, phoneNumberError: true}, () =>
        this.props.raiseUpdates(this.state),
      );
  };

  onWebSiteChange = (website: string) => {
    const regexHttp = BusinessEditionValidator.checkWebsite(website);
    const regexWww = BusinessEditionValidator.checkWww(website);
    if (regexHttp)
      this.setState(
        {
          website,
          websiteError: !regexHttp,
        },
        () => this.props.raiseUpdates(this.state),
      );
    else if (regexWww)
      this.setState(
        {
          website: 'http://' + website,
          websiteError: !regexWww,
        },
        () => this.props.raiseUpdates(this.state),
      );
    else
      this.setState(
        {
          website,
          websiteError: !regexHttp,
        },
        () => this.props.raiseUpdates(this.state),
      );
  };
}
