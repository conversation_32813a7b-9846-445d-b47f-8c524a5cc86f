import {connect} from 'react-redux';
import {
  geocodeAddressCoordinatesSelector,
  geocodeAddressErrorSelector,
  geocodeAddressLoadingSelector,
} from '../../../../../../../../common/usecases/addressgeocode/adressgeocode.selector';
import {geocodeAddress} from '../../../../../../../../common/usecases/addressgeocode/geocodeaddress.action';
import {AppState} from '../../../../../../../../configuration/AppState';
import {BusinessAddressEditionContainer} from './businessAddressEdition.container';
import {BusinessAddressType} from '../../../businessEdition.types';
import {faker, http, util} from 'src/_helpers';
import {RealApplicationRemoteService} from 'src/v1/common/adapters/secondaries/real/realApplicationRemoteService';
import {useState} from 'react';

const mapStateToProps = (state: AppState) => ({
  location: geocodeAddressCoordinatesSelector(state),
  loading: geocodeAddressLoadingSelector(state),
  geocodeError: geocodeAddressErrorSelector(state),
});

const mapDispatchToProps = dispatch => ({
  geocodeAddress: (
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ) => dispatch(geocodeAddress(zipCode, city, country, address)),
});

/*export const BusinessAddressEdition = connect(
    mapStateToProps,
    mapDispatchToProps
)(BusinessAddressEditionContainer)*/

export const BusinessAddressEdition = (props: any) => {
  const [location, setLocation] = useState({
    longitude: props.business?.address?.longitude ?? 0,
    latitude: props.business?.address?.latitude ?? 0,
  });

  const geocodeInputAddress = async (
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ) => {
    const coors = await util.geocodeAddress(zipCode, city, country, address);
    if (coors) {
      setLocation(coors);
    }
  };

  return (
    <BusinessAddressEditionContainer
      raiseUpdates={function (value: BusinessAddressType): void {
        console.log(value);
        props.raiseUpdates(value);
      }}
      business={props.business}
      geocodeAddress={geocodeInputAddress}
      geocodeError={''}
      location={location}
      loading={false}
    />
  );
};
