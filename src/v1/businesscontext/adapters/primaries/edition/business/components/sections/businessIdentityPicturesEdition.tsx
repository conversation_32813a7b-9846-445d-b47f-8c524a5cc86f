import React, { Component } from 'react';
import { ScrollView } from 'react-native';
import { CustomizedCameraContainer } from '../../../../../../../common/adapters/primaries/components/form/pickers/customizedCamera.container';
import { I18n } from '../../../../../../../configuration/i18n/i18n';
import { RGBAColor } from 'src/_styles';
import { BusinessRegistrationValidator } from '../../../../registration/businessRegistration.validator';
import { BusinessEditionFormKey } from '../../businessEdition.types';
import { BusinessType } from '../../businessEdition.types';

interface Props {
  raiseUpdates: (key: BusinessEditionFormKey, value: string) => void;
  business: BusinessType;
}

interface State {
  idProfessional: string;
  frontCard: string;
  backCard: string;
}

export class BusinessIdentityPicturesEdition extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      idProfessional: '',
      frontCard: '',
      backCard: '',
    };
  }

  render() {
    const { idProfessional, frontCard, backCard } = this.state;
    const { raiseUpdates } = this.props;

    return (
      <ScrollView>
        <CustomizedCameraContainer
          style={{ marginTop: 10 }}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.id_professional_placeholder}
          imagePlaceholder={idProfessional || ''}
          onPictureTaken={(imageURI) => {
            this.setState({ idProfessional: imageURI }, () =>
              raiseUpdates('idProfessional', imageURI)
            );
          }}
        />

        <CustomizedCameraContainer
          style={{ marginTop: 10 }}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.card_front}
          imagePlaceholder={frontCard || ''}
          onPictureTaken={(imageURI) => {
            this.setState({ frontCard: imageURI }, () =>
              raiseUpdates('frontCard', imageURI),
            );
          }}
        />

        <CustomizedCameraContainer
          style={{ marginTop: 10 }}
          transparent={false}
          color={new RGBAColor(0, 0, 0, 1)}
          name={I18n.getTranslation().business.card_back}
          imagePlaceholder={backCard || ''}
          onPictureTaken={(imageURI) => {
            this.setState({ backCard: imageURI }, () =>
              raiseUpdates('backCard', imageURI),
            );
          }}
        />
      </ScrollView>
    );
  }
}
