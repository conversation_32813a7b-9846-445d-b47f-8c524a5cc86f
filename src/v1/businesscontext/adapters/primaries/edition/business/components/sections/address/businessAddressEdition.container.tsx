import React, { PureComponent } from 'react'
import { <PERSON>rollView, StyleSheet, Text, View } from 'react-native';
import { AddressMap } from '../../../../../../../../common/adapters/primaries/components/form/fields/addressMap.field';
import { DefaultTextInput } from '../../../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import { ChoicePickerContainer } from '../../../../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import { Coordinates } from '../../../../../../../../common/domain/entities/Coordinates';
import { ChoiceType } from '../../../../../../../../common/domain/entities/types/AppTypes';
import { FormValidation } from '../../../../../../../../common/usecases/specifications/formValidation';
import { I18n } from '../../../../../../../../configuration/i18n/i18n';
import { Countries } from '../../../../../../../../configuration/setting/app.settings';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';
import { BusinessAddressType, BusinessEditionFormKey, BusinessType } from '../../../businessEdition.types';
import { BusinessEditionValidator } from '../../../validation/businessEdition.validator';
import { SubmissionFormValidator } from 'src/v1/instantSubmissioncontext/adapters/primaries/form_common/validation/submission.validator';

interface Props {
    raiseUpdates: (value: BusinessAddressType) => void
    business: BusinessType
    geocodeAddress: (zipCode: string, city: string, country: string, address: string) => void
    geocodeError: string
    location: Coordinates
    loading: boolean
}

interface State {
    address: BusinessAddressType
    hasAddressError: boolean
    addressError: boolean
    cityError: boolean
    countryError: boolean
    zipError: boolean
    latitudeError  : boolean
    longitudeError : boolean
    localLatitude: string
    localLongitude: string
}

export class BusinessAddressEditionContainer extends PureComponent<Props, State> {
    private previousAddress: { zipCode: string, city: string, country: string, address: string }

    constructor(props) {
        super(props)
        this.state = {
            address        : {
                address  : undefined,
                city     : undefined,
                country  : undefined,
                zipCode  : undefined,
                longitude: undefined,
                latitude : undefined
            },
            hasAddressError: false,
            addressError   : null,
            cityError      : null,
            zipError       : null,
            countryError   : null,
            latitudeError  : null,
            longitudeError : null,
            localLatitude: '',
            localLongitude: '',
        }
        this.previousAddress = {
            address: undefined,
            city   : undefined,
            country: undefined,
            zipCode: undefined
        }
    }

    static getDerivedStateFromProps(props: Props, state: State) {
        if (props?.business?.address && state?.address?.address === undefined) {
            const address = props.business.address;
            return {
                address: {
                    address: address?.address ?? '',
                    city: address?.city ?? '',
                    country: address?.country ?? '',
                    zipCode: address?.zipCode ?? '',
                    longitude: address?.longitude ?? 0,
                    latitude: address?.latitude ?? 0
                }
            }
        }
        return null;
    }

    componentDidUpdate(prevProps) {
        if (prevProps.business.hasError !== this.props.business.hasError && this.props.business.hasError === true)
            this.setState({
                addressError: !BusinessEditionValidator.checkAddress(this.state.address.address),
                cityError   : !BusinessEditionValidator.checkCity(this.state.address.city),
                zipError    : !BusinessEditionValidator.checkZipCode(this.state.address.zipCode),
                countryError: !BusinessEditionValidator.checkCountry(this.state.address.country)
            })

        if (this.props.location.latitude !== undefined && prevProps.location !== this.props.location)
            this.setState({
                address: {
                    ...this.state.address,
                    longitude: this.props.location.longitude,
                    latitude : this.props.location.latitude
                },
                localLatitude: this.props.location.latitude.toString(),
                localLongitude: this.props.location.longitude.toString(),
            }, () => this.props.raiseUpdates(this.state.address))
        else if (this.props.business.address.address !== undefined && this.props.business.address.latitude === undefined)
            this.getCoordinatesFromAddress()
    }

    componentDidMount() {
        if (this.props.business.address)
            this.setState({
                address: {
                    address  : this.props.business.address.address,
                    city     : this.props.business.address.city,
                    country  : this.props.business.address.country,
                    zipCode  : this.props.business.address.zipCode,
                    longitude: this.props.business.address.longitude,
                    latitude : this.props.business.address.latitude
                },
                localLatitude: this.props.location.latitude.toString(),
                localLongitude: this.props.location.longitude.toString(),
            }, () => {
                this.previousAddress = {
                    address: this.props.business.address.address,
                    city   : this.props.business.address.city,
                    country: this.props.business.address.country,
                    zipCode: this.props.business.address.zipCode
                }
            })
    }

    render() {
        const addressErrorView = this.props.geocodeError ? (
            <View style={styles.errorView}>
                <Text style={styles.error}>{I18n.getTranslation().business.address_not_found}</Text>
            </View>
        ) : null

        return (
            <ScrollView>
                <DefaultTextInput value={this.state.address.address}
                                  style={{ marginTop: 10 }}
                                  placeholder={I18n.getTranslation().business.address + ' (' + I18n.getTranslation().common.required_message + ")"}
                                  onBlur={() => this.getCoordinatesFromAddress()}
                                  error={this.state.addressError ? true : null}
                                  onChange={address => {
                                      this.onChangeHandler('address', address)
                                      this.setState({
                                          addressError: !BusinessEditionValidator.checkAddress(address)
                                      })
                                  }}
                />
                <View style={styles.addressContainer}>
                    <DefaultTextInput value={this.state.address.city}
                                      style={{ marginTop: 10, width: '58%' }}
                                      placeholder={I18n.getTranslation().business.city + ' (' + I18n.getTranslation().common.required_message + ")"}
                                      onBlur={() => this.getCoordinatesFromAddress()}
                                      error={this.state.cityError ? true : null}
                                      onChange={city => {
                                          this.onChangeHandler('city', city)
                                          this.setState({
                                              cityError: !BusinessEditionValidator.checkCity(city)
                                          })
                                      }}
                    />
                    <DefaultTextInput value={String(this.state.address.zipCode)}
                                      keyboardType={'numeric'}
                                      style={{ marginTop: 10, width: '38%' }}
                                      placeholder={I18n.getTranslation().business.zipcode}
                                      onBlur={() => this.getCoordinatesFromAddress()}
                                      error={this.state.zipError ? true : null}
                                      onChange={zipCode => {
                                          this.onChangeHandler('zipCode', zipCode)
                                          this.setState({
                                              zipError: !BusinessEditionValidator.checkZipCode(zipCode)
                                          })
                                      }}
                    />

                </View>
                <ChoicePickerContainer style={{ marginTop: 10 }}
                                       placeholder={I18n.getTranslation().instant_submission.country + ' (' + I18n.getTranslation().common.required_message + ")"}
                                       value={this.state.address.country}
                                       options={this.getCountryList()}
                                       error={this.state.countryError}
                                       onChange={country => {
                                           this.onChangeHandler('country', country.value)
                                       }}/>
                {addressErrorView}
                <AddressMap style={{ marginVertical: 10 }}
                            position={this.state.address}/>
                <DefaultTextInput
                    style={{marginTop: 10}}
                    placeholder={I18n.getTranslation().instant_submission.latitude + ' (' + I18n.getTranslation().common.required_message + ")"}
                    keyboardType={'numeric'}
                    onChange={value => {
                        this.setState({
                            localLatitude: value,
                            latitudeError: !SubmissionFormValidator.checkCoordinate(value),
                        });
                        console.log("Latitude : ", this.state.address.latitude);
                    }}
                    error={this.state.latitudeError}
                    value={this.state.localLatitude}
                    onBlur={() => this.raiseLatitude()}
                />
                <DefaultTextInput
                    style={{marginTop: 10}}
                    placeholder={I18n.getTranslation().instant_submission.longitude + ' (' + I18n.getTranslation().common.required_message + ")"}
                    keyboardType={'numeric'}
                    onChange={value => {
                        this.setState({
                            localLongitude: value,
                            longitudeError: !SubmissionFormValidator.checkCoordinate(value),
                        });
                        console.log("Longitude : ", this.state.address.longitude);
                    }}
                    error={this.state.longitudeError}
                    value={this.state.localLongitude}
                    onBlur={() => this.raiseLongitude()}
                />
            </ScrollView>
        )
    }

    onChangeHandler = (key: BusinessEditionFormKey, value) => {
        this.setState({
            address: {
                ...this.state.address,
                [key]: value
            }
        }, () => {
            this.props.raiseUpdates(this.state.address)
        })
    }

    onCoordsChangeHandler = (key, value) => {
        this.setState({
            address: {
                ...this.state.address,
                [key]: value
            }
        }, () => {
            this.props.raiseUpdates(this.state.address)
        })
    }

    getCoordinatesFromAddress() {
        if (this.isValidForm() && this.compareTheNewAndPreviousAddress()) {
            this.previousAddress = {
                address: this.state.address.address,
                city   : this.state.address.city,
                country: this.state.address.country,
                zipCode: this.state.address.zipCode
            }
            this.props.geocodeAddress(this.state.address.zipCode, this.state.address.city, this.state.address.country, this.state.address.address)
        }
    }

    private getCountryList(): ChoiceType[] {
        const result = []
        Countries().map(item => {
            result.push({
                label: item.name,
                value: item.code
            })
        })
        return result
    }

    private isValidForm() {
        return this.state.address.address.length > 5 &&
            this.state.address.city.length > 3 &&
            this.state.address.country.length > 0 &&
            FormValidation.validation('zipCode', this.state.address.zipCode)
    }

    private compareTheNewAndPreviousAddress(): boolean {
        return this.state.address.address !== this.previousAddress.address ||
            this.state.address.zipCode !== this.previousAddress.zipCode ||
            this.state.address.city !== this.previousAddress.city ||
            this.state.address.country !== this.previousAddress.country
    }

    private raiseLongitude() {
        let Longitude;
        if (this.state.localLongitude) {
            Longitude =  parseFloat(
                parseFloat(this.state.localLongitude).toFixed(7),
              );
        }

        if (!isNaN(Longitude)) {
            this.setState({
                address: {
                    ...this.state.address,
                    longitude: Longitude
                }
            }, () => {
                this.props.raiseUpdates(this.state.address)
            })
        } else {
            return;
        }
      }

      private raiseLatitude() {
        let Latitude;
        if (this.state.localLatitude) {
            Latitude =  parseFloat(
                parseFloat(this.state.localLatitude).toFixed(7),
              );
        }

        if (!isNaN(Latitude)) {
            this.setState({
                address: {
                    ...this.state.address,
                    latitude: Latitude
                }
            }, () => {
                this.props.raiseUpdates(this.state.address)
            })
        } else {
            return;
        }
      }
    }

const styles = StyleSheet.create({
    content         : {
        width        : '100%',
        paddingTop   : 0,
        paddingBottom: 0
    },
    addressContainer: {
        flexDirection : 'row',
        justifyContent: 'space-between'

    },
    errorView       : {
        paddingTop   : 5,
        paddingBottom: 5
    },
    error           : {
        fontSize  : 14,
        color     : Theme.flamingo,
        fontFamily: 'U8 Bold'
    }
})
