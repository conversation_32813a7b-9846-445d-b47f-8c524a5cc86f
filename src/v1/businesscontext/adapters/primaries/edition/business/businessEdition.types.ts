import { DateRangeType } from '../../../../../common/domain/entities/types/AppTypes';

export type BusinessEditionFormKey =
    'name'
    | 'type'
    | 'activity'
    | 'frontCard'
    | 'backCard'
    | 'idProfessional'
    | 'coverImage'
    | 'profileImage'
    | 'description'
    | 'address'
    | 'coordinate'
    | 'city'
    | 'country'
    | 'zipCode'
    | 'website'
    | 'email'
    | 'contacts'
    | 'phoneNumber'
    | 'openingHours'

export interface BusinessContactType {
    description: string
    website: string
    email: string
    phoneNumber: string
}

export interface BusinessAddressType {
    address: string
    city: string
    country: string
    zipCode: string
    longitude: number
    latitude: number
}

export interface BusinessOpeningHoursType {
    id: string
    openingDays: DateRangeType
    isPaused: boolean
}

export interface BusinessType {
    id: string
    name: string
    type: string
    activity: string
    coverImage: string
    profileImage: string
    openingHours: BusinessOpeningHoursType[]
    contacts: BusinessContactType
    address: BusinessAddressType
    hasError: boolean
}
