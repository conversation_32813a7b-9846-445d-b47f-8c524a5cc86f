import React, {PureComponent} from 'react';
import {StyleSheet} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {HTMLEditorInput} from '../../../../../common/adapters/primaries/components/form/inputs/HTMLEditor.input';
import {PicturePickerContainer} from '../../../../../common/adapters/primaries/components/form/pickers/picturePicker.container';
import {
  HeaderButtonIcon,
  HeaderButtonText,
  HeaderTitle,
} from '../../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../configuration/theme/app.theme';

interface State {
  serviceDetail: string;
  servicesPicture: string;
}

export class ServicesPageContainer extends PureComponent<any, State> {
  constructor(props) {
    super(props);
    this.state = {
      serviceDetail: '',
      servicesPicture: '',
    };
  }

  static navigationOptions = ({navigation}) => {
    return {
      headerTitle: (
        <HeaderTitle title={I18n.getTranslation().business.services} />
      ),
      headerRight: (
        <HeaderButtonText
          CTA={() => navigation.navigate('preview')}
          title={I18n.getTranslation().business.done}
          color={'rgba(156, 159, 192, 0.6)'}
        />
      ),
      headerLeft: (
        <HeaderButtonIcon
          CTA={() => navigation.navigate('instants')}
          iconName={'angle-left'}
        />
      ),
      headerStyle: {
        backgroundColor: Theme.blue,
        borderBottomColor: Theme.blue,
        elevation: 0,
        shadowOpacity: 0,
        height: 40,
      },
    };
  };

  render() {
    return (
      <KeyboardAwareScrollView style={styles.container}>
        <PicturePickerContainer
          style={{marginTop: 30}}
          name={I18n.getTranslation().business.picture_services}
          onPictureTaken={imageURI =>
            this.setState({servicesPicture: imageURI})
          }
        />

        <HTMLEditorInput
          value={this.state.serviceDetail}
          style={{height: 300}}
          placeholder={I18n.getTranslation().business.details_services}
          onBlur={textValue => {
            this.setState({serviceDetail: textValue});
          }}
        />
      </KeyboardAwareScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  placeholder: {
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    flex: 1,
    paddingTop: 50,
  },
});
