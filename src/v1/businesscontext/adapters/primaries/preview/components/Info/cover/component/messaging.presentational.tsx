import React, { PureComponent } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome'
import { I18n } from '../../../../../../../../configuration/i18n/i18n';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';

interface Props {
    navigation: any
}

export class MessagingHeader extends PureComponent<Props, any> {
    render() {
        return (
            <View style={styles.container}>
                <TouchableOpacity style={styles.btn} onPress={() => this.props.navigation.navigate('chat')}>
                    <IconFA name={'envelope'} size={20} color={Theme.sunglow}/>
                    <Text style={styles.title}>
                        {I18n.getTranslation().business.start_conversation}
                    </Text>
                </TouchableOpacity>
            </View>

        )
    }
}

const styles = StyleSheet.create({
    container: {
        height           : 60,
        backgroundColor  : 'white',
        justifyContent   : 'center',
        alignItems       : 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#EEE',
        borderTopWidth   : 1,
        borderTopColor   : '#EEE'
    },
    btn      : {
        flexDirection: 'row'
    },
    title    : {
        paddingLeft: 10,
        fontSize   : 16,
        fontFamily: 'U8 Bold'

    }
})
