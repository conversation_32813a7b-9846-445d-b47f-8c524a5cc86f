import React, { PureComponent } from 'react'
import { Image, ImageBackground, ImageRequireSource, ImageURISource, StyleSheet, Text, View } from 'react-native';
import { Theme, ThemeRGBA } from '../../../../../../../configuration/theme/app.theme';

interface Props {
    title: string
    subtitle: string
    profilePicture: ImageURISource | ImageURISource[] | ImageRequireSource;
    coverPicture: ImageURISource | ImageURISource[] | ImageRequireSource;
}

export class CoverPublic  extends PureComponent<Props>{
    render() {
        return(
            <View style={{ height: 150 }}>
                <ImageBackground source={this.props.coverPicture} style={{
                    width          : '100%',
                    height         : '100%'
                }}>
                    <View style={styles.infoView}>
                        <View style={styles.img}>
                            <Image resizeMode="cover" style={styles.img} source={this.props.profilePicture}/>
                        </View>
                        <View style={styles.viewTitle}>
                            <Text style={styles.title} numberOfLines={1}>
                                {this.props.title}
                            </Text>
                            <Text style={styles.subtitle}>{this.props.subtitle}</Text>
                        </View>
                    </View>
                </ImageBackground>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    infoView: {
        flexDirection: 'row',
        paddingLeft  : 0,
        position     : 'absolute',
        bottom       : 15,
        left: 15,
        width: 250,
        borderRadius: 5,
        backgroundColor: ThemeRGBA.black.generateOpacity(0.2)
    },
    imgView : {
        flex          : 2,
        borderRadius  : 5,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    img     : {
        width          : 60,
        height         : 60,
        borderRadius   : 5,
        backgroundColor: Theme.sunglow
    },

    title    : {
        fontSize  : 18,
        lineHeight: 18,
        fontFamily: 'U8 Bold',
        color     : 'white',
        padding   : 5,
        paddingTop: 0,
        flexShrink: 1
    },
    viewTitle: {
        paddingLeft  : 10,
        paddingTop: 10,
        width        : 180
    },
    subtitle : {
        fontSize: 14,
        fontFamily: 'U8 Regular',
        padding   : 5,
        color   : 'white'
    }
})
