import React, {PureComponent} from 'react';
import {StyleSheet, View} from 'react-native';
import {I18n} from '../../../../../../../../configuration/i18n/i18n';
import {BusinessOpeningHours} from '../../../../../../../domain/entities/businessOpeningHour';
import {BusinessSpecifications} from '../../../../../../../usecases/businessSpecifications';
import {BusinessHoursItem} from './businessHoursItem.presentational';
import {format} from 'date-fns';
import {stringToDate} from 'src/_helpers';

interface Props {
  openingHours: BusinessOpeningHours[];
}

interface OpeningHoursItemType {
  day: string;
  time: string;
}

interface State {
  openingHoursList: OpeningHoursItemType[];
}

export class BusinessHoursDetails extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      openingHoursList: [
        {day: I18n.getTranslation().business.monday, time: '-'},
        {day: I18n.getTranslation().business.tuesday, time: '-'},
        {day: I18n.getTranslation().business.wednesday, time: '-'},
        {day: I18n.getTranslation().business.thursday, time: '-'},
        {day: I18n.getTranslation().business.friday, time: '-'},
        {day: I18n.getTranslation().business.saturday, time: '-'},
        {day: I18n.getTranslation().business.sunday, time: '-'},
      ],
    };
  }

  static getDerivedStateFromProps(props, state) {
    props.openingHours.map(item => {
      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.monday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[0].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.tuesday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[1].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.wednesday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[2].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.thursday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[3].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.friday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[4].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.saturday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[5].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');

      if (
        BusinessSpecifications.isInRange(
          I18n.getTranslation().business.sunday,
          item.openingDays,
          item.isPaused,
        )
      )
        state.openingHoursList[6].time =
          format(stringToDate(item.startTime), 'HH:mm') +
          ' - ' +
          format(stringToDate(item.endTime), 'HH:mm');
    });

    return state;
  }

  render() {
    const openingHoursList = this.state.openingHoursList.map((item, i) =>
      item.time !== '-' ? (
        <BusinessHoursItem key={i} day={item.day} time={item.time} />
      ) : null,
    );
    return <View style={styles.container}>{openingHoursList}</View>;
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 20,
    flexDirection: 'column',
  },
});
