import React, {PureComponent} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {AddressMap} from '../../../../../../../common/adapters/primaries/components/form/fields/addressMap.field';
import {Theme} from '../../../../../../../configuration/theme/app.theme';
import {BusinessAddress} from '../../../../../../domain/entities/businessAddress';
interface Props {
  address: BusinessAddress;
}
export class BusinessLocationInfo extends PureComponent<Props, any> {
  render() {
    return (
      <View style={styles.addressView}>
        <AddressMap position={this.props.address.coordinates} />
        <Text style={styles.address}>{this.props.address.fullAddress}</Text>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  addressView: {
    width: '100%',
    padding: 20,
    borderWidth: 1,
    borderColor: Theme.gainsboro,
    borderRadius: 0,
  },
  address: {
    paddingTop: 10,
    marginTop: 10,
    textAlign: 'center',
    fontSize: 15,
    borderTopWidth: 1,
    borderTopColor: Theme.gainsboro,
    fontFamily: 'U8 Bold',
  },
});
