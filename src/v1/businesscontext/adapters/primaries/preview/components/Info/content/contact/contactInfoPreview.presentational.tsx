import React, { PureComponent } from 'react'
import { Linking, StyleSheet, Text, View } from 'react-native';
import { WebViewBrowser } from '../../../../../../../../common/adapters/primaries/components/modal/webViewBrowser.modal';
import { I18n } from '../../../../../../../../configuration/i18n/i18n';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';
import { ItemContactInfo } from './itemContactInfo.presentational';

interface Props {
    description: string
    website: string
    email: string
    phone: string
}

interface State {
    visibleModal: boolean
}

export class ContactInfoPreview extends PureComponent<Props, State> {

    constructor(props) {
        super(props)
        this.state = {
            visibleModal: false
        }
    }

    render() {
        return (
            <View style={styles.container}>
                <View style={styles.descriptionView}>
                    <Text style={styles.title}>{I18n.getTranslation().business.description}</Text>
                    <Text style={styles.text}>{this.props.description}</Text>
                </View>
                <ItemContactInfo label={I18n.getTranslation().business.website} value={this.props.website}
                                 onClick={() => this.setState({ visibleModal: true })}/>
                <ItemContactInfo label={I18n.getTranslation().business.email} value={this.props.email}
                                 onClick={this.sendEmail}/>
                <ItemContactInfo label={I18n.getTranslation().business.phone} value={this.props.phone}
                                 onClick={this.callPhoneNumber}/>
                <WebViewBrowser onCloseModal={() => this.setState({ visibleModal: false })}
                                visible={this.state.visibleModal}
                                urlBrowser={this.props.website}/>
            </View>
        )
    }

    sendEmail = () => {
        Linking.openURL('mailto:' + this.props.email)
    }

    callPhoneNumber = () => {
        Linking.openURL('tel:' + this.props.phone)
    }
}

const styles = StyleSheet.create({
    container      : {
        flexDirection    : 'column',
        alignItems       : 'stretch',
        justifyContent   : 'center',
        flex             : 1,
        width            : '100%',
        paddingHorizontal: 10
    },
    descriptionView: {
        flexDirection    : 'column',
        alignItems       : 'stretch',
        justifyContent   : 'center',
        borderBottomWidth: 1,
        borderBottomColor: Theme.gainsboro,
        paddingBottom    : 10
    },
    title          : {
        fontSize       : 15,
        fontFamily: 'U8 Bold',
        paddingVertical: 10,
        textAlign      : 'left'
    },
    text           : {
        flexDirection: 'row',
        fontSize     : 14,
        fontFamily: 'U8 Regular',
        flexWrap     : 'wrap',
        textAlign    : 'left',
        lineHeight   : 25
    }
})
