import React from 'react'
import { StyleSheet, Text, View } from 'react-native';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';

export const BusinessHoursItem = ({ day, time }) =>
    <View style={styles.container}>
        <View style={styles.left}>
            <Text style={styles.title}>{day}</Text>
        </View>
        <View style={styles.right}>
            <Text style={styles.time}>{time}</Text>
        </View>
    </View>

const styles = StyleSheet.create({
    container: {
        flex           : 1,
        flexDirection  : 'row',
        justifyContent : 'space-between',
        alignItems     : 'stretch',
        width          : '100%',
        paddingVertical: 5
    },
    left     : {
        flex: 2
    },
    right    : {
        flex: 2
    },
    title    : {
        fontSize  : 15,
        color     : '#000',
        fontFamily: 'U8 Bold'
    },
    time     : {
        fontSize : 14,
        fontFamily: 'U8 Regular',
        color    : Theme.logan,
        textAlign: 'right'
    }

})
