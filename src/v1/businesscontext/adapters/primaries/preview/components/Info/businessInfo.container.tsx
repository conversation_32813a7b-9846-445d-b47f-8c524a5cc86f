import React, {PureComponent} from 'react';
import {<PERSON><PERSON>, View} from 'react-native';
import Accordion from 'react-native-collapsible/Accordion';
import {AccordionHeader} from '../../../../../../common/adapters/primaries/components/accordion/accordionHeader.presentational';
import {AccordionType} from '../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {Business} from '../../../../../domain/entities/business';
import {BusinessSpecifications} from '../../../../../usecases/businessSpecifications';
import {BusinessContactInfo} from './content/businessContactInfo.presentational';
import {BusinessHoursDetails} from './content/businesshours/businessHours.presentational';
import {BusinessLocationInfo} from './content/businessLocationInfo.presentational';
import {BusinessHeaderType} from './types/businessHeaderType';
import SubscriptionCard from 'src/_components/shared/SubscriptionCard';

interface Props {
  business: Business;
  navigation: any;
  user: any;
}

interface State {
  activeSection: number[];
  sections: AccordionType[];
}

export class BusinessInfoContainer extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      activeSection: [0],
      sections: [],
    };
  }

  static getDerivedStateFromProps(props) {
    return {
      sections: [
        {
          rank: 0,
          title: I18n.getTranslation().business.opening_hours,
          subTitle: BusinessSpecifications.openingHoursStatus(
            props.business.openingHours,
          ),
          content: (
            <BusinessHoursDetails openingHours={props.business.openingHours} />
          ),
          iconName: 'md-time',
        },
      ],
    };
  }

  render() {
    console.log("BusinessInfoContainer business: " ,this.props.business);
    return (
      <View style={{flex: 1, marginBottom: 80}}>
        {this.props.user.company.uuid === this.props.business.id
          ? <SubscriptionCard
              navigation={this.props.navigation}
            />
          : null}
        <Accordion
          activeSections={this.state.activeSection}
          sections={this.state.sections}
          containerStyle={{width: '100%'}}
          renderHeader={this.renderHeader}
          renderContent={this.renderContent}
          onChange={this.updateSections}
        />
        <BusinessLocationInfo address={this.props.business.address} />
        <BusinessContactInfo contacts={this.props.business.contacts} />
      </View>
    );
  }

  generateBusinessHeader(business: Business): BusinessHeaderType {
    return {
      name: business.name,
      type: business.type,
      profileImage:
        business.profileImage === null || business.profileImage === undefined
          ? require('../../../../../../assets/img/ProProfile-Logo2.jpg')
          : {uri: business.profileImage},
      coverImage:
        business.coverImage === null || business.coverImage === undefined
          ? require('../../../../../../assets/img/ProProfile-BackgroundImage2.jpg')
          : {uri: business.coverImage},
    };
  }

  private renderContent = section => {
    return section.content;
  };

  private renderHeader = section => {
    return (
      <AccordionHeader
        isOpened={this.state.activeSection.includes(section.rank)}
        rank={section.rank}
        iconName={section.iconName}
        title={section.title}
        subTitle={section.subTitle}
      />
    );
  };

  private updateSections = activeSection => {
    this.setState({activeSection});
  };
}
