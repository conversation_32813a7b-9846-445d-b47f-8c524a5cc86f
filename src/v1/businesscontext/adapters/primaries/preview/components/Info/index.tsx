import {connect} from 'react-redux';
import {AppState} from '../../../../../../configuration/AppState';
import {loadRemoteBusiness} from '../../../../../usecases/remoteLoading/loadRemoteBusiness.actions';
import {remoteBusinessSelector} from '../../../../../usecases/remoteLoading/loadRemoteBusiness.selectors';
import {BusinessInfoContainer} from './businessInfo.container';
import {faker} from 'src/_helpers';
import { useGetBusiness } from 'src/_hooks/useGetBusiness';
import { useBusiness } from 'src/_hooks';

const mapStateToProps = (state: AppState) => ({
  business: remoteBusinessSelector(state),
});

const mapDispatchToProps = dispatch => ({
  loadBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
});

/*export const BusinessInfo: any = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessInfoContainer);*/

export const BusinessInfo = (props: any) => {
  const [business, loading, user] = useBusiness();
  console.log(user);

  return (loading ? null :
    <BusinessInfoContainer
      business={props.business}
      navigation={props.navigation}
      user={user}
    />
  );
};
