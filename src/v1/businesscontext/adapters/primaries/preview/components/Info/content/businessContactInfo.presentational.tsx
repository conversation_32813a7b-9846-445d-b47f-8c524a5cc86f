import React, { PureComponent } from 'react'
import { StyleSheet, View } from 'react-native';
import { Theme } from '../../../../../../../configuration/theme/app.theme';
import { BusinessContact } from '../../../../../../domain/entities/businessContact';
import { ContactInfoPreview } from './contact/contactInfoPreview.presentational';

interface Props {
    contacts: BusinessContact
}

export class BusinessContactInfo extends PureComponent<Props, any> {
    render() {
        return (
            <View style={styles.container}>
                <ContactInfoPreview description={this.props.contacts.description}
                                    website={this.props.contacts.website}
                                    email={this.props.contacts.email}
                                    phone={this.props.contacts.phoneNumber}
                />
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container  : {
        paddingHorizontal: 10,
        flexDirection    : 'column',
        justifyContent: 'center',
        alignItems    : 'center',
        marginTop     : 10,
        backgroundColor: 'white'
    },
    addressView: {
        width       : '100%',
        borderWidth : 1,
        borderColor : Theme.gainsboro,
        borderRadius: 5
    },
    address    : {
        paddingVertical: 10,
        textAlign      : 'center',
        fontSize       : 15,
        borderTopWidth : 1,
        borderTopColor : Theme.gainsboro,
        fontFamily: 'U8 Bold'
    }
})
