import React, { PureComponent } from 'react'
import {
    Image,
    ImageBackground,
    ImageRequireSource,
    ImageURISource,
    StyleSheet,
    Text,
    View,
    ViewStyle
} from 'react-native';
import { Theme, ThemeRGBA } from '../../../../../../../../configuration/theme/app.theme';

interface Props {
    title: string
    subtitle: string
    profilePicture: ImageURISource | ImageURISource[] | ImageRequireSource;
    coverPicture: ImageURISource | ImageURISource[] | ImageRequireSource;
    style?: ViewStyle
    compact?: boolean
}

export class CoverInfo extends PureComponent<Props, any> {
    static defaultProps = {
        compact: false
    }

    render() {
        return (
            <View style={this.props.style}>
                <ImageBackground source={this.props.coverPicture} style={{
                    width          : '100%',
                    height         : '100%'
                }}>
                    <View style={styles.overlay} />
                    <View style={styles.infoView}>
                    <View style={styles.img}>
                        <Image resizeMode="cover" style={styles.img} source={this.props.profilePicture}/>
                    </View>
                        <View style={this.props.compact ? styles.viewTitle : styles.titleContainer}>
                            <Text style={styles.title}>
                                {this.props.title}
                            </Text>
                            <Text style={styles.subtitle}>{this.props.subtitle}</Text>
                        </View>
                    </View>
                </ImageBackground>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: ThemeRGBA.black.generateOpacity(0.2)
    },
    infoView: {
        flexDirection: 'row',
        paddingLeft  : 15,
        position     : 'absolute',
        bottom       : 15
    },
    imgView : {
        flex          : 2,
        borderRadius  : 5,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    img     : {
        width          : 60,
        height         : 60,
        borderRadius   : 5,
        backgroundColor: Theme.sunglow
    },
    titleContainer: {
        paddingLeft  : 10,
        flexShrink: 1
    },
    title    : {
        fontSize  : 18,
        lineHeight: 18,
        fontFamily: 'U8 Bold',
        color     : 'white',
        padding   : 5
    },
    viewTitle: {
        paddingLeft  : 10,
        width        : 250
    },
    subtitle : {
        fontSize: 14,
        fontFamily: 'U8 Regular',
        color   : 'white',
        padding : 5
    }
})
