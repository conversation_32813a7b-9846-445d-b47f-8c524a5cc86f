import React, { PureComponent } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';

interface Props {
    label: string,
    value: string,
    onClick: () => void
}

export class ItemContactInfo extends PureComponent<Props, any> {
    render() {
        return (
            <View style={styles.itemInfo}>
                <View>
                    <Text style={styles.title}>{this.props.label}</Text>
                </View>
                <View>
                    <TouchableOpacity onPress={this.props.onClick}>
                        <Text style={styles.link}>{this.props.value}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    title   : {
        fontSize       : 15,
        fontFamily: 'U8 Bold',
        paddingVertical: 10,
        textAlign      : 'left'
    },
    itemInfo: {
        flexDirection    : 'row',
        justifyContent   : 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: Theme.gainsboro
    },
    link    : {
        color          : Theme.blue,
        fontSize       : 14,
        fontFamily: 'U8 Regular',
        textAlign      : 'right',
        paddingVertical: 10
    }
})
