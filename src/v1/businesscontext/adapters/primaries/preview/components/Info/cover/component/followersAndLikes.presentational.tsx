import React, { PureComponent } from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native';
import { I18n } from '../../../../../../../../configuration/i18n/i18n';
import { Theme } from '../../../../../../../../configuration/theme/app.theme';
import { FollowersAndLikesItem } from './followersAndLikesItem.presentational';

interface Props {
    followersNumber?: number
    likesNumber?: number
    compact: boolean
    style?: ViewStyle
}

interface State {
    activeFollows: boolean
    activeNotification: boolean
}

export class FollowersAndLikes extends PureComponent<Props, State> {
    constructor(props) {
        super(props)
        this.state = {
            activeFollows     : true,
            activeNotification: false
        }
    }

    render() {
        return (
            <View style={[styles.container, this.props.style]}>
                <View style={styles.left}>
                    <FollowersAndLikesItem count={this.props.followersNumber}
                                           active={this.state.activeFollows}
                                           activeColor={Theme.flamingo}
                                           onUpdate={() => this.setState({ activeFollows: !this.state.activeFollows })}
                                           iconName={'heart'}
                                           withText={!this.props.compact}
                                           label={I18n.getTranslation().business.followers}/>
                </View>
                <View style={styles.right}>
                    <FollowersAndLikesItem count={this.props.likesNumber}
                                           active={this.state.activeNotification}
                                           activeColor={'#f4a63f'}
                                           onUpdate={() => this.setState({ activeNotification: !this.state.activeNotification })}
                                           iconName={'bell'}
                                           withText={!this.props.compact}
                                           label={I18n.getTranslation().business.likes}/>
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container   : {
        flexDirection: 'row'
    },
    left        : {
        flex          : 2,
        justifyContent: 'center',
        alignItems    : 'center'
    },
    right       : {
        flex          : 2,
        justifyContent: 'center',
        alignItems    : 'center'
    },
    infoFollower: {
        flex          : 3,
        flexDirection : 'row',
        alignItems    : 'center',
        justifyContent: 'center'
    },
    viewIcon    : {
        flex          : 2,
        borderRadius  : 5,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    icon        : {
        padding     : 10,
        width       : 40,
        height      : 40,
        borderRadius: 5
    }
})
