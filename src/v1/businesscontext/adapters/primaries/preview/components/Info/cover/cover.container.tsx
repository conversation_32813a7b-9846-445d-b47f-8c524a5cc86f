import React, { PureComponent } from 'react'
import { StyleSheet, View } from 'react-native';
import { BusinessHeaderType } from '../types/businessHeaderType';
import { CoverInfo } from './component/coverInfo.presentational';

interface Props {
    navigation: any
    business: BusinessHeaderType
    compact?: boolean
}

export class CoverContainer extends PureComponent<Props, any> {
    static defaultProps = {
        compact: false
    }

    render() {
        return (
            <View style={[styles.container, this.props.compact ? { height: 90 } : { height: '100%' }]}>
                <CoverInfo title={this.props.business.name}
                           subtitle={this.props.business.type}
                           profilePicture={this.props.business.profileImage}
                           coverPicture={this.props.business.coverImage}
                           style={this.props.compact ? { flex: 3 } : { flex: 2 }}
                           compact={this.props.compact}
                />
            </View>

        )
    }
}

const styles = StyleSheet.create({
    container: {
        height: '100%',
        width : '100%'
    }
})
