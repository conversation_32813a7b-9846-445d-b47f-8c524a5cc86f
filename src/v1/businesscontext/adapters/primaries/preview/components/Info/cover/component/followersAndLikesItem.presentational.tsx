import React, { PureComponent } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome'
import { Theme } from '../../../../../../../../configuration/theme/app.theme';

interface Props {
    iconName: string
    withText: boolean
    count?: number
    label?: string
    active?: boolean
    activeColor?: string
    onUpdate: () => void
}

interface State {
    colorIcon: string
    backgroundIcon: string
}

export class FollowersAndLikesItem extends PureComponent<Props, State> {

    constructor(props) {
        super(props)
        this.state = {
            colorIcon     : this.props.active ? 'white' : Theme.gainsboro,
            backgroundIcon: this.props.active ? this.props.activeColor : 'white'
        }
    }

    render() {
        const labels = this.props.withText ?
            <View style={styles.viewInfo}>
                <Text style={styles.number}>{this.props.count}</Text>
                <Text style={styles.label}>{this.props.label}</Text>
            </View> : null
        return (
            <TouchableOpacity style={styles.container} onPress={() => this.handlePress()}>
                <View style={styles.viewIcon}>
                    <View style={[styles.icon, {
                        backgroundColor: this.state.backgroundIcon,
                        borderColor    : this.state.backgroundIcon === this.props.activeColor ? this.state.backgroundIcon : Theme.gainsboro
                    }]}>
                        <IconFA name={this.props.iconName} color={this.state.colorIcon} size={20}/>
                    </View>
                </View>
                {labels}
            </TouchableOpacity>
        )
    }

    handlePress() {
        this.props.onUpdate()
        setTimeout(() => {
            this.props.active === true ?
                this.setState({ colorIcon: 'white', backgroundIcon: this.props.activeColor })
                : this.setState({ colorIcon: Theme.gainsboro, backgroundIcon: 'white' })
        }, 500)
    }
}

const styles = StyleSheet.create({
    container: {
        flex          : 1,
        flexDirection : 'row',
        alignItems    : 'center',
        justifyContent: 'center'
    },
    viewIcon : {
        flex          : 4,
        borderRadius  : 5,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    icon     : {
        padding     : 9,
        width       : 40,
        height      : 40,
        borderRadius: 5,
        borderWidth : 1
    },
    viewInfo : {
        flex          : 6,
        marginLeft    : 5,
        flexDirection : 'column',
        alignItems    : 'flex-start',
        justifyContent: 'center'
    },
    number   : {
        fontSize  : 16,
        fontFamily: 'U8 Bold',
        color     : 'black'
    },
    label    : {
        color     : Theme.gainsboro,
        fontSize  : 12,
        fontFamily: 'U8 Bold',
        fontStyle : 'italic'
    }

})
