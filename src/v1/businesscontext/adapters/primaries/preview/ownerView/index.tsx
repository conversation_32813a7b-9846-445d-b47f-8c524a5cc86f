import {connect, useSelector} from 'react-redux';
import {AppState} from '../../../../../configuration/AppState';

import {businessSelector} from '../../../../usecases/business.selector';
import {loadRemoteBusiness} from '../../../../usecases/remoteLoading/loadRemoteBusiness.actions';
import {remoteBusinessSelector} from '../../../../usecases/remoteLoading/loadRemoteBusiness.selectors';
import {OwnerBusinessInfoContainer} from './ownerBusinessInfo.container';
import {faker, storage, util} from 'src/_helpers';
import {useEffect, useState} from 'react';
import {Constants} from 'src/_constants';
import {BusinessBuilder} from 'src/v1/businesscontext/domain/builder/business.builder';
import {View} from 'react-native';
import {Spinner} from 'src/_components';
import {getAuth} from 'src/_reducers';

const mapStateToProps = (state: AppState) => ({
  business: remoteBusinessSelector(state),
  localBusiness: businessSelector(state),
  businessId: businessSelector(state).id,
});

const mapDispatchToProps = dispatch => ({
  loadBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
});

/*export const OwnerBusinessInfo = connect(
    mapStateToProps,
    mapDispatchToProps
)(OwnerBusinessInfoContainer)*/

export const OwnerBusinessInfo = (props: any) => {
  const auth = useSelector(getAuth);
  const [state, setState] = useState({
    loading: true,
    business: undefined,
  });

  useEffect(() => {
    (async () => {
      if (state.business) {
        setState(prevState => ({
          ...prevState,
          loading: true,
        }));
        await util.sleep(2000);
      }

      const user = await storage.get(Constants.USER_INFO_KEY);
      const company = user?.company;
      console.log(company)
      setState(prevState => ({
        ...prevState,
        loading: false,

        business: company?.uuid
          ? new BusinessBuilder()
              .withId(company.uuid)
              .withName(company.name)
              .withType(company.type)
              .withActivity(company.activity)
              .withAddress(company.location.address)
              .withZipCode(company.location.zipCode)
              .withCity(company.location.city)
              .withCountry(company.location.country)
              .withActive(company.active)
              .withLongitude(Number(company.location.longitude))
              .withLatitude(Number(company.location.latitude))
              .withSiren(company.metas.siren)
              .withIsVerified(company.metas.is_verified === "0" ? false : true)
              .withCoverImage(company.profile.coverImage)
              .withProfileImage(company.profile.profileImage)
              .withOpeningHours(company.activeDays ?? [])
              .withDescription(company.description)
              .withWebsite(company.contact.website)
              .withEmail(company.contact.email)
              .withPhoneNumber(company.contact.phoneNumber)
              .withPlan(company.subscription?.plan)
              .withStatus(company.subscription?.status)
              .withStartDate(company.subscription?.startDate)
              .withEndDate(company.subscription?.endDate)
              .withIsSubscriptionActive(company.subscription?.active)
              .build()
          : undefined,
      }));
    })();
  }, [auth.business]);

  return state.loading ? (
    <View
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
      }}>
      <Spinner background={'rgba(254, 148, 62, 0.4);'} />
    </View>
  ) : (
    <OwnerBusinessInfoContainer
      navigation={props.navigation}
      business={state.business}
      localBusiness={state.business}
      businessId={state.business?.id}
      loadBusiness={(businessId: string) => {
        console.log(businessId);
      }}
    />
  );
};
