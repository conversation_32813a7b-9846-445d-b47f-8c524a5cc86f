import React, {PureComponent} from 'react';
import {Dimensions, ScrollView, StyleSheet, View, Platform} from 'react-native';
import {Spinner} from '../../../../../common/adapters/primaries/components/spinner.presentational';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {ThemeRGBA} from '../../../../../configuration/theme/app.theme';
import {BusinessTypes} from '../../../../configuration/registration/settings';
import {Business} from '../../../../domain/entities/business';
import {BusinessInfo} from '../components/Info';
import {CoverContainer} from '../components/Info/cover/cover.container';
import {BusinessHeaderType} from '../components/Info/types/businessHeaderType';

interface Props {
  navigation: any;
  business: Business;
  businessId: string;
  loadBusiness: (businessId: string) => void;
  localBusiness: Business;
}

export class OwnerBusinessInfoContainer extends PureComponent<Props> {
  render() {
    if (this.props.business !== null)
      return (
        <View style={{flex: 1}}>
          <ScrollView style={styles.scrollContainer}>
            <View
              style={{
                backgroundColor: ThemeRGBA.black.generateOpacity(0.2),
                height: 250,
              }}>
              <CoverContainer
                business={this.generateBusinessHeader(this.props.business)}
                navigation={this.props.navigation}
                compact={false}
              />
            </View>
            <BusinessInfo
              business={this.props.business}
              navigation={this.props.navigation}
            />
          </ScrollView>
        </View>
      );
    return <Spinner background={'rgba(0,0,0,0.3);'} />;
  }

  generateBusinessHeader(business: Business): BusinessHeaderType {
    return {
      name: business.name,
      type: this.getLabelFromValue(business.type),
      profileImage:
        business.profileImage === null ||
        business.profileImage === undefined ||
        business.profileImage === ''
          ? require('../../../../../assets/img/ProProfile-Logo2.jpg')
          : {uri: business.profileImage},
      coverImage:
        business.coverImage === null ||
        business.coverImage === undefined ||
        business.coverImage === ''
          ? require('../../../../../assets/img/ProProfile-BackgroundImage2.jpg')
          : {uri: business.coverImage},
    };
  }
  getLabelFromValue(value: string): string {
    for (const type of BusinessTypes())
      if (type.value === value) return type.label;
  }
}
const height = Dimensions.get('window').height;

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
    left: 0,
    right: 0,
    height: Platform.OS === 'android' ? height - 50 : height,
    backgroundColor: 'white',
  },
});
