import {publicBusinessId} from '../../../../../common/usecases/status/applicationStatus.selectors';
import {AppState} from '../../../../../configuration/AppState';
import {loadRemoteBusiness} from '../../../../usecases/remoteLoading/loadRemoteBusiness.actions';
import {remoteBusinessSelector} from '../../../../usecases/remoteLoading/loadRemoteBusiness.selectors';
import {PublicBusinessInfoContainer} from './publicBusinessInfo.container';
import {useState} from 'react';
import {View} from 'react-native';
import {useRoute} from '@react-navigation/native';

const mapStateToProps = (state: AppState) => ({
  business: remoteBusinessSelector(state),
  businessId: publicBusinessId(state),
});

const mapDispatchToProps = dispatch => ({
  loadBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
});

/*export const PublicBusinessInfo = connect(
    mapStateToProps,
    mapDispatchToProps
)(PublicBusinessInfoContainer)*/

export const PublicBusinessInfo = (props: any) => {
  const route = useRoute();

  const [state] = useState({
    business: (route?.params as any)?.business,
  });

  if (!state.business) {
    return <View />;
  }
  return (
    <PublicBusinessInfoContainer
      navigation={props.navigation}
      business={state.business}
      businessId={state.business.id}
      loadBusiness={(businessId: string) => {
        console.log(businessId);
      }}
    />
  );
};
