import React, { PureComponent } from 'react';
import { Dimensions, Platform, ScrollView, StyleSheet, View } from 'react-native';
import { Spinner } from '../../../../../common/adapters/primaries/components/spinner.presentational';
import { HeaderButtonIcon, HeaderWithLogo } from '../../../../../common/adapters/primaries/navigation/navigationHeader';
import { ThemeRGBA } from '../../../../../configuration/theme/app.theme';
import { BusinessTypes } from '../../../../configuration/registration/settings';
import { Business } from '../../../../domain/entities/business';
import { BusinessInfo } from '../components/Info';
import { CoverPublic } from '../components/Info/cover/coverPublic.presentational';
import { BusinessHeaderType } from '../components/Info/types/businessHeaderType';

interface Props {
    navigation: any
    business: Business
    businessId: string
    loadBusiness: (businessId: string) => void
}

const height = Dimensions.get('window').height

export class PublicBusinessInfoContainer extends PureComponent<Props, any> {
    static navigationOptions = ({ navigation }) => {
        return {
            headerTitle    : <HeaderWithLogo/>,
            headerRight    : <View/>,
            headerLeft     : <HeaderButtonIcon
                CTA={() => navigation.navigate('instant_details', { instantId: navigation.getParam('instantId') })}
                iconName={'angle-left'} color={'black'} backgroundColor={'white'}/>,
            headerStyle    : {
                backgroundColor  : 'white',
                borderBottomColor: 'white',
                elevation        : 0,
                shadowOpacity    : 0,
                height           : 40
            },
            gesturesEnabled: false
        }
    }

    render() {
        if (this.props.business !== null)
            return (<View>
                <View style={{ backgroundColor: ThemeRGBA.black.generateOpacity(0.3), height: 150 }}>
                    <CoverPublic coverPicture={this.generateBusinessHeader(this.props.business).coverImage}
                                 title={this.generateBusinessHeader(this.props.business).name}
                                 profilePicture={this.generateBusinessHeader(this.props.business).profileImage}
                                 subtitle={this.generateBusinessHeader(this.props.business).type}/>
                </View>
                <ScrollView style={styles.scrollContainer}>
                    <BusinessInfo business={this.props.business}
                                  navigation={this.props.navigation}/>
                </ScrollView>
            </View>)
        return <Spinner background={'rgba(0,0,0,0.1);'}/>
    }

    generateBusinessHeader(business: Business): BusinessHeaderType {
        return {
            name        : business.name,
            type        : this.getLabelFromValue(business.type),
            profileImage: business.profileImage === null || business.profileImage === undefined || business.profileImage === '' ?
                require('../../../../../assets/img/ProProfile-Logo2.jpg')
                : { uri: business.profileImage },
            coverImage  : business.coverImage === null || business.coverImage === undefined || business.coverImage === '' ?
                require('../../../../../assets/img/ProProfile-BackgroundImage2.jpg')
                : { uri: business.coverImage }
        }
    }

    getLabelFromValue(value: string): string {
        for (const type of BusinessTypes())
            if (type.value === value)
                return type.label
    }
}

const styles = StyleSheet.create({
    scrollContainer: {
        left           : 0,
        right          : 0,
        height         : Platform.OS === 'android' ? height - 250 : height - 300,
        backgroundColor: 'white'
    }
})
