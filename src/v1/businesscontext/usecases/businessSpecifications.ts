import {util} from 'src/_helpers';
import {I18n} from '../../configuration/i18n/i18n';
import {BusinessOpeningHours} from '../domain/entities/businessOpeningHour';
import {isAfter, isBefore} from 'date-fns';

export class BusinessSpecifications {
  static openingHoursStatus(openingHours: BusinessOpeningHours[]): string {
    let status = I18n.getTranslation().business.close_hours;
    openingHours.map(item => {
      if (
        this.isInRange(
          util.getDayWeekTitle(new Date()),
          item.openingDays,
          item.isPaused,
        ) &&
        isAfter(new Date(), new Date(item.startTime)) &&
        isBefore(new Date(), new Date(item.endTime))
      )
        status = I18n.getTranslation().business.open_hours;
    });
    return status;
  }

  static isInRange(day: string, range: string, isPaused: boolean) {
    switch (day) {
      case I18n.getTranslation().business.monday:
        return (
          (range === 'all_days' ||
            range === 'mondays_to_fridays' ||
            range === 'all_mondays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.tuesday:
        return (
          (range === 'all_days' ||
            range === 'mondays_to_fridays' ||
            range === 'all_tuesdays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.wednesday:
        return (
          (range === 'all_days' ||
            range === 'mondays_to_fridays' ||
            range === 'all_wednesdays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.thursday:
        return (
          (range === 'all_days' ||
            range === 'mondays_to_fridays' ||
            range === 'all_thursdays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.friday:
        return (
          (range === 'all_days' ||
            range === 'mondays_to_fridays' ||
            range === 'all_fridays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.saturday:
        return (
          (range === 'all_days' ||
            range === 'saturdays_and_sundays' ||
            range === 'all_saturdays') &&
          isPaused === false
        );
      case I18n.getTranslation().business.sunday:
        return (
          (range === 'all_days' ||
            range === 'saturdays_and_sundays' ||
            range === 'all_sundays') &&
          isPaused === false
        );
      default:
        return false;
    }
  }
}
