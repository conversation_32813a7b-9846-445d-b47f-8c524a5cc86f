export const LOAD_LOCAL_BUSINESS = 'LOAD_LOCAL_BUSINESS'
export const LOAD_LOCAL_BUSINESS_SUCCEEDED = 'LOAD_LOCAL_BUSINESS_SUCCEEDED'
export const LOAD_LOCAL_BUSINESS_FAILED = 'LOAD_LOCAL_BUSINESS_FAILED'

export const loadLocalBusiness = () => ({
    type: LOAD_LOCAL_BUSINESS
})

export const loadLocalBusinessSucceeded = () => ({
    type: LOAD_LOCAL_BUSINESS_SUCCEEDED
})

export const loadLocalBusinessFailed = (error: string) => ({
    type   : LOAD_LOCAL_BUSINESS_FAILED,
    payload: error
})
