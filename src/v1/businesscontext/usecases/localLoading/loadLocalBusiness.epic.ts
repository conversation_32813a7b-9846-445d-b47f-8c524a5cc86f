import {ofType} from 'redux-observable';
import {of} from 'rxjs';
import {catchError, flatMap, switchMap} from 'rxjs/internal/operators';
import {Business} from '../../domain/entities/business';
import {BusinessRepository} from '../../domain/gateways/business.repository';
import {updateReduxStoreBusiness} from '../business.action';
import {
  LOAD_LOCAL_BUSINESS,
  loadLocalBusinessFailed,
  loadLocalBusinessSucceeded,
} from './loadLocalBusiness.actions';

export const loadLocalBusinessEpic: any = (
  action$,
  store,
  {businessRepository}: {businessRepository: BusinessRepository},
) =>
  action$.pipe(
    ofType(LOAD_LOCAL_BUSINESS),
    switchMap(() =>
      businessRepository.getLocalBusiness().pipe(
        flatMap((business: Business) => [
          updateReduxStoreBusiness(business),
          loadLocalBusinessSucceeded(),
        ]),
        catchError(err => of(loadLocalBusinessFailed(err))),
      ),
    ),
  );
