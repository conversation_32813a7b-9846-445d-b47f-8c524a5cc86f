import { LoadLocalBusinessState } from '../../configuration/localLoading/loadLocalBusinessState';
import {
    LOAD_LOCAL_BUSINESS,
    LOAD_LOCAL_BUSINESS_FAILED,
    LOAD_LOCAL_BUSINESS_SUCCEEDED
} from './loadLocalBusiness.actions';

const initialState: LoadLocalBusinessState = {
    isLoading: false,
    error    : undefined
}

export const loadLocalBusinessReducer = (state = initialState, action) => {
    switch (action.type) {
        case LOAD_LOCAL_BUSINESS:
            return {
                ...state,
                isLoading: true,
                error    : undefined
            }
        case LOAD_LOCAL_BUSINESS_SUCCEEDED:
            return {
                ...state,
                isLoading: false,
                error    : undefined
            }
        case LOAD_LOCAL_BUSINESS_FAILED:
            return {
                ...state,
                isLoading: false,
                error    : action.payload
            }
        default:
            return state
    }
}
