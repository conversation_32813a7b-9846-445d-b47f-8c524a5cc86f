import { Business } from '../../domain/entities/business';

export const EDIT_BUSINESS = 'EDIT_BUSINESS'
export const EDIT_BUSINESS_SUCCEEDED = 'EDIT_BUSINESS_SUCCEEDED'
export const EDIT_BUSINESS_FAILED = 'EDIT_BUSINESS_FAILED'
export const EDIT_BUSINESS_ABORT = 'EDIT_BUSINESS_ABORT'

export const editBusiness = (business: Business) => ({
    type: EDIT_BUSINESS,
    payload: business
})

export const editBusinessSucceeded = () => ({
    type: EDIT_BUSINESS_SUCCEEDED
})

export const editBusinessFailed = (error: string) => ({
    type: EDIT_BUSINESS_FAILED,
    payload: error
})

export const editBusinessAbort = () => ({
    type: EDIT_BUSINESS_ABORT
})
