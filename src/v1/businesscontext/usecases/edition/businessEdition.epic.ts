import { ofType } from 'redux-observable';
import { of } from 'rxjs';
import { catchError, flatMap, switchMap, takeUntil } from 'rxjs/internal/operators';
import { Business } from '../../domain/entities/business';
import { BusinessRepository } from '../../domain/gateways/business.repository';
import { BusinessRemoteService } from '../../domain/gateways/businessRemoteService';
import { updateReduxStoreBusiness } from '../business.action';
import { loadRemoteBusiness } from '../remoteLoading/loadRemoteBusiness.actions';
import { EDIT_BUSINESS, EDIT_BUSINESS_ABORT, editBusinessFailed, editBusinessSucceeded } from './businessEdition.actions';

export const businessEditionEpic = (action$, store, { businessRepository, businessRemoteService }:
    { businessRepository: BusinessRepository, businessRemoteService: BusinessRemoteService }) =>
    action$.pipe(
        ofType(EDIT_BUSINESS),
        switchMap<{ payload: Business }, any>(action => businessRepository.editBusiness(action.payload)
            .pipe(
                switchMap(() => businessRemoteService.editRemoteBusiness(action.payload)
                    .pipe(
                        flatMap(() => [
                            editBusinessSucceeded(),
                            loadRemoteBusiness(action.payload.id),
                            updateReduxStoreBusiness(action.payload)
                        ]),
                        catchError(err => of(editBusinessFailed(err)))
                    )),
                catchError(err => of(editBusinessFailed(err))),
                takeUntil(action$.ofType(EDIT_BUSINESS_ABORT))
            )
        )
    )
