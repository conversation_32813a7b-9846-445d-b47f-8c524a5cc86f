import { BusinessEditionState } from '../../configuration/edition/businessEdition.state';
import { EDIT_BUSINESS, EDIT_BUSINESS_ABORT, EDIT_BUSINESS_FAILED, EDIT_BUSINESS_SUCCEEDED } from './businessEdition.actions';

const initialState: BusinessEditionState = {
    loading: false,
    error  : undefined,
    success: null
}

export const businessEditionReducer = (state = initialState, action) => {
    switch (action.type) {
        case EDIT_BUSINESS:
            return {
                loading: true,
                error  : undefined,
                success: null
            }
        case EDIT_BUSINESS_SUCCEEDED:
            return {
                loading: false,
                error  : undefined,
                success: true
            }
        case EDIT_BUSINESS_FAILED:
            return {
                loading: false,
                error  : action.payload,
                success: null
            }
        case EDIT_BUSINESS_ABORT:
            return {
                loading: false,
                error  : undefined,
                success: null
            }
        default:
            return state
    }
}
