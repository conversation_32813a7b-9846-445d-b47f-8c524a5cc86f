import { BusinessInfoState } from '../configuration/business.state';
import { UPDATE_BUSINESS } from './business.action';

const initialState: BusinessInfoState = {
    business: null
}
export const businessReducer = (state = initialState, action) => {
    switch (action.type) {
        case UPDATE_BUSINESS:
            return {
                ...state,
                business: action.payload
            }
        default:
            return state
    }
}
