import { BusinessRegistrationState } from '../../configuration/registration/business.registration.state';
import {
    BUSINESS_REGISTRATION_ERROR, BUSINESS_REGISTRATION_SUCCESS,
    LOAD_BUSINESS_BY_SIREN,
    LOAD_BUSINESS_BY_SIREN_FAILED,
    LOAD_BUSINESS_BY_SIREN_SUCCEEDED, REGISTER_BUSINESS
} from './businessRegistration.actions';

const initialState: BusinessRegistrationState = {
    isLoading: false,
    error    : undefined,
    success  : null,
    siren    : undefined
}
export const businessRegistrationReducer = (state = initialState, action) => {
    switch (action.type) {
        case REGISTER_BUSINESS:
            return {
                ...state,
                isLoading: true,
                success  : null,
                error    : undefined
            }
        case BUSINESS_REGISTRATION_ERROR:
            return {
                ...state,
                isLoading: false,
                success: null,
                error: action.payload
            }
        case BUSINESS_REGISTRATION_SUCCESS:
            return {
                ...state,
                isLoading: false,
                success: true,
                error: undefined
            }
        case LOAD_BUSINESS_BY_SIREN:
            return {
                ...state,
                isLoading: true,
                siren: action.payload,
                error: undefined
            }
        case LOAD_BUSINESS_BY_SIREN_FAILED:
            return{
                ...state,
                isLoading: false,
                error: action.payload
            }
        case LOAD_BUSINESS_BY_SIREN_SUCCEEDED:
            return{
                ...state,
                isLoading: false,
                error    : undefined
            }
        default:
            return state
    }
}
