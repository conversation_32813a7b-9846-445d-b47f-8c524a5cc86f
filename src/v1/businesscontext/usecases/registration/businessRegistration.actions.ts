import { Business } from '../../domain/entities/business';

export const REGISTER_BUSINESS = 'REGISTER_BUSINESS'
export const BUSINESS_REGISTRATION_ERROR = 'BUSINESS_REGISTRATION_ERROR'
export const BUSINESS_REGISTRATION_SUCCESS = 'BUSINESS_REGISTRATION_SUCCESS'
export const LOAD_BUSINESS_BY_SIREN = 'LOAD_BUSINESS_BY_SIREN'
export const LOAD_BUSINESS_BY_SIREN_SUCCEEDED = 'LOAD_BUSINESS_BY_SIREN_SUCCEEDED'
export const LOAD_BUSINESS_BY_SIREN_FAILED = 'LOAD_BUSINESS_BY_SIREN_FAILED'

export const registerBusiness = (business: Business, professionalID: string, frontCardPicture: string, backCardPicture: string) => ({
    type   : REGISTER_BUSINESS,
    payload: { business, professionalID, frontCardPicture, backCardPicture }
})

export const businessRegistrationError = (error: string) => ({
    type   : BUSINESS_REGISTRATION_ERROR,
    payload: error
})

export const businessRegistrationSuccess = () => ({
    type: BUSINESS_REGISTRATION_SUCCESS
})

export const loadBusinessBySiren = (siren: string) => ({
    type   : LOAD_BUSINESS_BY_SIREN,
    payload: siren
})

export const loadBusinessBySirenSucceeded = () => ({
    type: LOAD_BUSINESS_BY_SIREN_SUCCEEDED
})
export const loadBusinessBySirenFailed = (error: string) => ({
    type   : LOAD_BUSINESS_BY_SIREN_FAILED,
    payload: error
})
