import { ofType } from 'redux-observable';
import { of } from 'rxjs';
import { catchError, flatMap, mergeMap, switchMap } from 'rxjs/operators';
import { BusinessBuilder } from '../../domain/builder/business.builder';
import { Business } from '../../domain/entities/business';
import { BusinessRepository } from '../../domain/gateways/business.repository';
import { BusinessRemoteService } from '../../domain/gateways/businessRemoteService';
import { updateReduxStoreBusiness } from '../business.action';
import { resetRemoteBusiness } from '../remoteLoading/loadRemoteBusiness.actions';
import {
    businessRegistrationError,
    businessRegistrationSuccess,
    LOAD_BUSINESS_BY_SIREN,
    loadBusinessBySirenFailed,
    loadBusinessBySirenSucceeded,
    REGISTER_BUSINESS
} from './businessRegistration.actions';

export const businessRegistrationEpics = (action$, store, { businessRemoteService, businessRepository }:
    { businessRemoteService: BusinessRemoteService, businessRepository: BusinessRepository }) =>
    action$.pipe(
        ofType(REGISTER_BUSINESS),
        switchMap<{ payload: { business: Business, professionalID: string, frontCardPicture: string, backCardPicture: string } }, any>(
            action => businessRemoteService.registerBusiness(action.payload.business,
                action.payload.professionalID,
                action.payload.frontCardPicture,
                action.payload.backCardPicture)
                .pipe(
                    mergeMap((BusinessId: string) => {
                            const business = new BusinessBuilder()
                                .withId(BusinessId)
                                .withName(action.payload.business.name)
                                .withType(action.payload.business.type)
                                .withAddress(action.payload.business.address.address)
                                .withZipCode(action.payload.business.address.zipCode)
                                .withCity(action.payload.business.address.city)
                                .withCountry(action.payload.business.address.country)
                                .withActive(action.payload.business.active)
                                .withPhoneNumber(action.payload.business.contacts.phoneNumber)
                                .withMembership(action.payload.business.membership)
                                .build()
                            return businessRepository.saveBusiness(business)
                                .pipe(
                                    flatMap(() => [
                                            businessRegistrationSuccess(),
                                            resetRemoteBusiness(),
                                            updateReduxStoreBusiness(business)
                                        ]
                                    ), catchError(err => of(businessRegistrationError(err)))
                                )
                        }
                    ), catchError(err => of(businessRegistrationError(err)))
                )
        )
    )

export const loadBusinessBySirenEpic = (action$, store, { businessRemoteService }: { businessRemoteService: BusinessRemoteService }) =>
    action$.pipe(
        ofType(LOAD_BUSINESS_BY_SIREN),
        switchMap<{ payload: string }, any>(
            action => businessRemoteService.getBusinessBySiren(action.payload)
                .pipe(
                    flatMap((business: Business) => [
                        loadBusinessBySirenSucceeded(),
                        updateReduxStoreBusiness(business)
                    ]),
                    catchError(error => of(loadBusinessBySirenFailed(error)))
                )
        )
    )
