import { Business } from '../../domain/entities/business';

export const LOAD_REMOTE_BUSINESS = 'LOAD_REMOTE_BUSINESS'
export const LOAD_REMOTE_BUSINESS_SUCCEEDED = 'LOAD_REMOTE_BUSINESS_SUCCEEDED'
export const LOAD_REMOTE_BUSINESS_FAILED = 'LOAD_REMOTE_BUSINESS_FAILED'
export const RESET_REMOTE_BUSINESS = 'RESET_REMOTE_BUSINESS'

export const loadRemoteBusiness = (businessId: string) => ({
    type   : LOAD_REMOTE_BUSINESS,
    payload: businessId
})

export const loadRemoteBusinessSucceeded = (business: Business) => ({
    type   : LOAD_REMOTE_BUSINESS_SUCCEEDED,
    payload: business
})

export const loadRemoteBusinessFailed = (error: string) => ({
    type   : LOAD_REMOTE_BUSINESS_FAILED,
    payload: error
})

export const resetRemoteBusiness = () => ({
    type: RESET_REMOTE_BUSINESS
})
