import { AppState } from '../../../configuration/AppState';

export const loadingRemoteBusinessSelector = (appState: AppState) => appState.business.loadRemoteBusiness.isLoading
export const errorRemoteLoadingBusinessSelector = (appState: AppState) => appState.business.loadRemoteBusiness.error
export const remoteBusinessSelector = (appState: AppState) => appState.business.loadRemoteBusiness.remoteBusiness
