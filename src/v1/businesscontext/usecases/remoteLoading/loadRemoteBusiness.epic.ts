import { ofType } from 'redux-observable';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/internal/operators';
import { Business } from '../../domain/entities/business';
import { BusinessRemoteService } from '../../domain/gateways/businessRemoteService';
import {
    LOAD_REMOTE_BUSINESS,
    loadRemoteBusinessFailed,
    loadRemoteBusinessSucceeded
} from './loadRemoteBusiness.actions';

export const loadRemoteBusinessEpic = (action$, store, { businessRemoteService }: { businessRemoteService: BusinessRemoteService }) =>
    action$.pipe(
        ofType(LOAD_REMOTE_BUSINESS),
        switchMap<{ payload: string }, any>(action => businessRemoteService.loadRemoteBusiness(action.payload)
            .pipe(
                map((business: Business) => loadRemoteBusinessSucceeded(business)),
                catchError(err => of(loadRemoteBusinessFailed(err)))
            )
        )
    )
