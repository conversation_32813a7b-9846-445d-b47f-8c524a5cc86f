import { LoadRemoteBusinessState } from '../../configuration/remoteLoading/loadLocalBusinessState';
import {
    LOAD_REMOTE_BUSINESS,
    LOAD_REMOTE_BUSINESS_FAILED,
    LOAD_REMOTE_BUSINESS_SUCCEEDED, RESET_REMOTE_BUSINESS
} from './loadRemoteBusiness.actions';

const initialState: LoadRemoteBusinessState = {
    isLoading     : false,
    error         : undefined,
    remoteBusiness: null
}

export const loadRemoteBusinessReducer = (state = initialState, action) => {
    switch (action.type) {
        case LOAD_REMOTE_BUSINESS:
            return {
                ...state,
                isLoading     : true,
                error         : undefined,
                remoteBusiness: null
            }
        case LOAD_REMOTE_BUSINESS_SUCCEEDED:
            return {
                ...state,
                isLoading     : false,
                error         : undefined,
                remoteBusiness: action.payload
            }
        case LOAD_REMOTE_BUSINESS_FAILED:
            return {
                ...state,
                isLoading     : false,
                error         : action.payload,
                remoteBusiness: null
            }
        case RESET_REMOTE_BUSINESS:
            return{
                ...state,
                isLoading     : false,
                error         : undefined,
                remoteBusiness: null
            }
        default:
            return state
    }
}
