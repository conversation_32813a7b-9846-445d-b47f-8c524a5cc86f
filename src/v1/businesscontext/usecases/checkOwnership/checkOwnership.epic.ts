import { ofType } from 'redux-observable';
import { of } from 'rxjs';
import { catchError, flatMap, mergeMap, switchMap } from 'rxjs/internal/operators';
import { Business } from '../../domain/entities/business';
import { BusinessRepository } from '../../domain/gateways/business.repository';
import { BusinessRemoteService } from '../../domain/gateways/businessRemoteService';
import { updateReduxStoreBusiness } from '../business.action';
import { CHECK_OWNERSHIP, checkOwnershipFailed, checkOwnershipSucceeded } from './checkOwnership.actions';

export const checkOwnershipEpic = (action$, store, { businessRemoteService, businessRepository }:
    {
        businessRemoteService: BusinessRemoteService,
        businessRepository: BusinessRepository
    }) =>
    action$.pipe(
        ofType(CHECK_OWNERSHIP),
        switchMap(() => businessRemoteService.checkOwnership()
            .pipe(
                mergeMap(
                    (businessId: string) => businessRemoteService.loadRemoteBusiness(businessId)
                        .pipe(
                            mergeMap((business: Business) => businessRepository.saveBusiness(business)
                                .pipe(
                                    flatMap(() => [
                                            checkOwnershipSucceeded(),
                                            updateReduxStoreBusiness(business)
                                        ]
                                    )
                                )), catchError(err => of(checkOwnershipFailed(err)))
                        )
                ), catchError(err => of(checkOwnershipFailed(err)))
            )
        )
    )
