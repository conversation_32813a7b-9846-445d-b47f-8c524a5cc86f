import { CheckOwnershipState } from '../../configuration/business.state';
import { CHECK_OWNERSHIP, CHECK_OWNERSHIP_FAILED, CHECK_OWNERSHIP_SUCCEEDED } from './checkOwnership.actions';

const initialState: CheckOwnershipState = {
    loading: false,
    error  : undefined,
    success: null
}

export const checkOwnershipReducer = (state = initialState, action) => {
    switch (action.type) {
        case CHECK_OWNERSHIP:
            return {
                loading: true,
                error  : undefined,
                success: null
            }
        case CHECK_OWNERSHIP_SUCCEEDED:
            return {
                loading: false,
                error  : undefined,
                success: true
            }
        case CHECK_OWNERSHIP_FAILED:
            return {
                loading: false,
                error  : action.payload,
                success: null
            }
        default:
            return state
    }
}
