import {connect, useDispatch} from 'react-redux';
import {AppState} from '../../../../configuration/AppState';
import {SubmissionInstant} from '../../../domain/entities/submissionInstant';
import {editInstant} from '../../../usecases/edition/editinstant.actions';
import {
  errorEditInstantSelector,
  isEditInstantLoadingSelector,
  successEditInstantSelector,
} from '../../../usecases/edition/editinstant.selectors';
import {loadInstantByIdForEdition} from '../../../usecases/loadById/instantLoadingById.actions';
import {
  errorLoadInstantForEditingSelector,
  isLoadInstantForEditingLoadingSelector,
  loadedInstantForEditionSelector,
} from '../../../usecases/loadById/instantLoadingById.selectors';
import {InstantEditionContainer} from './instantedition.container';
import {useEffect, useState} from 'react';
import {http, storage, util} from 'src/_helpers';
import {SubmissionInstantBuilder} from 'src/v1/instantSubmissioncontext/domain/entities/builder/submissionInstant.builder';
import {Spinner} from 'src/_components';
import {Constants} from 'src/_constants';
import {BusinessBuilder} from 'src/v1/businesscontext/domain/builder/business.builder';
import {updateAuthBusiness} from 'src/_reducers';
import fStorage from '@react-native-firebase/storage';
import { Platform } from 'react-native';

const mapStateToProps = (state: AppState) => ({
  loadByIdLoading: isLoadInstantForEditingLoadingSelector(state),
  editionLoading: isEditInstantLoadingSelector(state),
  retrievedInstant: loadedInstantForEditionSelector(state),
  successEdition: successEditInstantSelector(state),
  errorEdition: errorEditInstantSelector(state),
  errorLoadById: errorLoadInstantForEditingSelector(state),
});
/*
const mapDispatchToProps = dispatch => ({
  editInstant: (submissionInstant: SubmissionInstant) =>
    dispatch(editInstant(submissionInstant)),
  loadById: (id: string) => dispatch(loadInstantByIdForEdition(id)),
});
export const InstantEdition = connect(
  mapStateToProps,
  mapDispatchToProps,
)(InstantEditionContainer);*/

export const InstantEdition = (props: any) => {
  const dispatch = useDispatch();
  const [state, setState] = useState({
    retrievedInstant: undefined,
    loadByIdLoading: true,
    editionLoading: false,
    errorLoadById: '',
    errorEdition: '',
    successEdition: false,
    business: undefined,
  });

  const loadBusiness = async () => {
    const user = await storage.get(Constants.USER_INFO_KEY);
    const company = user?.company;

    setState(prevState => ({
      ...prevState,
      loading: false,
      business: company?.uuid
        ? new BusinessBuilder()
            .withId(company.uuid)
            .withName(company.name)
            .withType(company.type)
            .withActivity(company.activity)
            .withAddress(company.location.address)
            .withZipCode(company.location.zipCode)
            .withCity(company.location.city)
            .withCountry(company.location.country)
            .withActive(company.active)
            .withLongitude(Number(company.location.longitude))
            .withLatitude(Number(company.location.latitude))
            .withSiren(company.metas.siren)
            .withCoverImage(company.profile.coverImage)
            .withProfileImage(company.profile.profileImage)
            .withOpeningHours(company.activeDays ?? [])
            .withDescription(company.description)
            .withWebsite(company.contact.website)
            .withEmail(company.contact.email)
            .withPhoneNumber(company.contact.phoneNumber)
            .build()
        : undefined,
    }));
  };

  const loadInstant = async () => {
    const res = await http.get(
      `/activities/pro/${props.route.params.instantId}`,
    );
    console.log(res);

    let retrievedInstant = undefined;
    if (res.data?.id) {
      retrievedInstant = new SubmissionInstantBuilder()
        .withId(res.data.uuid)
        .withBusinessId(res.data.information.businessId)
        .withCategory(res.data.information.category)
        .withTitle(res.data.information.title)
        .withShortDescription(res.data.information.shortDescription)
        .withLongDescription(res.data.information.longDescription)
        .withPicture(res.data.information.picture)
        .withAddress(res.data.address.address)
        .withCity(res.data.address.city)
        .withCountry(res.data.address.country)
        .withPrice(res.data.information.price)
        .withZipcode(res.data.address.zipCode)
        .withCoords(res.data.address.coords)
        .withActivationTimes(res.data.activationTimes)
        .withPartners(res.data.partners)
        .withProOnly(res.data.pro_only)
        .build();
    }
    setState(prevState => ({
      ...prevState,
      retrievedInstant,
      loadByIdLoading: false,
    }));
  };

  useEffect(() => {
    loadInstant();
    loadBusiness();
  }, [props.route.params.instantId]);

  return (
    <InstantEditionContainer
      loadById={function (id: string): void {
        console.log(id);
      }}
      editInstant={async function (submissionInstant: SubmissionInstant) {
        setState(prev => ({...prev, loading: true}));
        
        let payload: any = {...submissionInstant};
        if (
          submissionInstant?.instantInformation?.picture?.startsWith('file:/') || (Platform.OS==="ios")
        ) {
          setState(prev => ({...prev, editionLoading: true}));
          if(submissionInstant?.instantInformation?.picture)
            {
              try {
                const reference = fStorage().ref(
                  `activityPics/${submissionInstant.instantInformation.picture
                    .split('/')
                    .pop()}`,
                );
                await reference.putFile(
                  submissionInstant.instantInformation.picture,
                );
                const url = await reference.getDownloadURL();
    
                payload = {
                  ...payload,
                  _instantInformation: {
                    ...payload._instantInformation,
                    _picture: url,
                  },
                };
              } catch (error) {}
            }

        }

        setState(prev => ({...prev, editionLoading: true}));

        //payload = await util.tryGeocodeInstantPayloadAddress(payload);
        console.log("Payload: ", payload);
        const res = await http.post(
          '/activities/edit/' + submissionInstant.instantInformation.id,
          payload,
        );

        if (res.data?.id) {
          dispatch(updateAuthBusiness(state.business));
          props.navigation.goBack();
        } else {
          setState(prev => ({...prev, editionLoading: true}));
        }
      }}
      retrievedInstant={state.retrievedInstant}
      loadByIdLoading={state.loadByIdLoading}
      editionLoading={state.editionLoading}
      errorLoadById={state.errorLoadById}
      navigation={props.navigation}
      errorEdition={state.errorEdition}
      successEdition={state.successEdition}
    />
  );
};
