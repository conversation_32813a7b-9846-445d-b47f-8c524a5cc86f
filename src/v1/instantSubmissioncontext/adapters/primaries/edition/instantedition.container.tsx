import React, {PureComponent} from 'react';
import {StyleSheet, Text} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ApplicationToast} from '../../../../common/adapters/primaries/components/application.toast';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';
import {ApplicationContext} from '../../../../common/configuration/application.context';
import {ScheduleType} from '../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../configuration/i18n/i18n';
import {ActiveDaysNames} from '../../../../configuration/setting/app.settings';
import {InstantActivationTime} from '../../../domain/entities/_instantActivationTime';
import {SubmissionInstantBuilder} from '../../../domain/entities/builder/submissionInstant.builder';
import {SubmissionInstant} from '../../../domain/entities/submissionInstant';
import {
  InstantSubmissionFormKey,
  InstantSubmissionType,
} from '../form_common/formTypes';
import {SubmissionPreview} from '../form_common/preview/submissionpreview.presentational';
import {SubmissionFormValidator} from '../form_common/validation/submission.validator';
import {InstantEditionAccordion} from './instantedition.accordion';
import {HeaderButtonIcon, HeaderButtonText} from 'src/_components';
import {format} from 'date-fns';
import {stringToDate} from 'src/_helpers';

interface Props {
  loadById: (id: string) => void;
  editInstant: (instant: SubmissionInstant) => void;
  retrievedInstant: SubmissionInstant;
  loadByIdLoading: boolean;
  editionLoading: boolean;
  errorLoadById: string;
  navigation: any;
  errorEdition: string;
  successEdition: boolean;
}

interface State {
  instant: InstantSubmissionType;
  activeSection: number[];
  errorSection: number;
}

export class InstantEditionContainer extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      instant: null,
      activeSection: [0],
      errorSection: undefined,
    };
  }

  componentDidUpdate(prevProps: Props) {
    if (
      this.props.retrievedInstant &&
      this.props.retrievedInstant !== prevProps.retrievedInstant
    )
      this.setState({
        instant: this.mapToInstantSubmissionType(this.props.retrievedInstant),
      });
    else if (
      this.props.successEdition &&
      this.props.successEdition !== prevProps.successEdition
    )
      this.props.navigation.goBack();
    else if (
      this.props.errorEdition &&
      this.props.errorEdition !== prevProps.errorEdition
    )
      switch (this.props.errorEdition) {
        case '500':
          ApplicationToast.error(
            I18n.getTranslation().instant_submission.server_error,
          );
          break;
        case '415':
          ApplicationToast.error(
            I18n.getTranslation().instant_submission.over_sized_image_error,
          );
          break;
        case '404':
          ApplicationToast.error(
            I18n.getTranslation().instant_submission.form_data_error,
          );
          break;
        default:
          ApplicationToast.error(
            I18n.getTranslation().instant_submission.server_error,
          );
      }
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderButtonText
          paddingRight={0}
          CTA={this.editInstant}
          title={I18n.getTranslation().instant_submission.publish}
          iconName={''}
        />
      ),
      headerLeft: () => (
        <HeaderButtonIcon
          paddingLeft={0}
          CTA={this.props.navigation.goBack}
          iconName={'angle-left'}
          color={'black'}
        />
      ),
    });
  }

  render() {
    if (this.props.errorLoadById)
      return (
        <Text style={styles.bannerText}>
          {I18n.getTranslation().instant_submission.server_error}
        </Text>
      );
    else if (this.props.loadByIdLoading || this.props.editionLoading)
      return <Spinner background={'rgba(255,255,255,0.4)'} />;
    else if (this.state.instant)
      return (
        <KeyboardAwareScrollView style={styles.container}>
          <SubmissionPreview
            description={this.state.instant.description}
            picture={
              this.state.instant.picture ? this.state.instant.picture : ''
            }
            title={this.state.instant.title}
            instantType={this.state.instant.type}
          />
          <InstantEditionAccordion
            instant={this.state.instant}
            errorSection={this.state.errorSection}
            activeSection={this.state.activeSection}
            onChange={(key, value) => this.updateInstantState(key, value)}
          />
        </KeyboardAwareScrollView>
      );
    return null;
  }

  updateInstantState(key: InstantSubmissionFormKey, value: string) {
    console.log(key, value);
    const instant = {...this.state.instant, [key]: value};
    this.setState({instant});
  }

  editInstant = () => {
    const submissionResult = SubmissionFormValidator.validate(
      this.state.instant,
    );
    if (submissionResult === undefined && this.props.editionLoading === false) {
      console.log('instant: ', this.state.instant);
      const instantSubmission = new SubmissionInstantBuilder()
        .withId(this.state.instant.id)
        .withBusinessId(this.state.instant.businessId)
        .withCategory(this.state.instant.type)
        .withTitle(this.state.instant.title)
        .withShortDescription(this.state.instant.description)
        .withLongDescription(this.state.instant.longDescription)
        .withPicture(this.state.instant.picture)
        .withAddress(this.state.instant.address)
        .withCity(this.state.instant.city)
        .withCountry(this.state.instant.country)
        .withPrice(this.state.instant.price)
        .withZipcode(this.state.instant.zip)
        .withCoords(this.state.instant.coordinates)
        .withActivationTime(this.state.instant.schedules)
        .withPartners(this.state.instant.partners)
        .withProOnly(this.state.instant.proOnly)
        .build();
        console.log('instantSubmission: ', instantSubmission);
      this.props.editInstant(instantSubmission);
    } else
      this.setState({
        errorSection: submissionResult,
        activeSection: [submissionResult],
      });
  };

  private mapToInstantSubmissionType(
    instant: SubmissionInstant,
  ): InstantSubmissionType {
    const scheduleKey =
      instant.instantActivationTimes[0]?.activeDayType === 'fixed'
        ? 'fixed'
        : 'recurrent';
    const schedules: ScheduleType[] = instant.instantActivationTimes.map(
      (item: InstantActivationTime) => {
        return {
          id: item.id,
          dateRange: {
            key: item.activeDayType,
            label:
              scheduleKey === 'fixed'
                ? this.getLabelFromDates(item.startDateTime, item.endDateTime)
                : ActiveDaysNames[item.activeDayType],
            startTime: item.startDateTime,
            endTime: item.endDateTime,
          },
          isPaused: !item.isEnabled,
          prePoned:
            item.activationTimeType === 'just_before_and_during_instant',
        };
      },
    );

    return {
      id: instant.instantInformation.id,
      businessId: instant.instantInformation.businessId,
      title: instant.instantInformation.title,
      description: instant.instantInformation.shortDescription,
      type: instant.instantInformation.category,
      picture: instant.instantInformation.picture,
      longDescription: instant.instantInformation.longDescription,
      address: instant.address.address,
      city: instant.address.city,
      country: instant.address.country,
      price: instant.instantInformation.price,
      zip: instant.address.zipCode.toString(),
      coordinates: instant.address.coords,
      schedules: {key: scheduleKey, schedule: schedules},
      partners: instant.instantPartner,
      virtualLink: Number(
        instant?.address?.address?.indexOf('http') !== -1,
      ).toString(),
      proOnly: instant.proOnly,
    };
  }

  private getLabelFromDates(startTime: string, endTime: string): string {
    const formattedStartDate = format(stringToDate(startTime), 'dd MMM yyyy', {
      locale: I18n.getDateLang(),
    });
    const formattedEndDate = format(stringToDate(endTime), 'dd MMM yyyy', {
      locale: I18n.getDateLang(),
    });

    return formattedStartDate === formattedEndDate
      ? `Le ${formattedStartDate}`
      : `${formattedStartDate} au ${formattedEndDate}`;
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
  },
  bannerText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    fontFamily: 'U8 Bold',
  },
});
