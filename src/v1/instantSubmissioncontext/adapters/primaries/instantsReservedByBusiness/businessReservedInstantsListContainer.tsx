import React, {PureComponent, useEffect, useState} from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SectionList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {Business} from '../../../../businesscontext/domain/entities/business';
import {ApplicationToast} from '../../../../common/adapters/primaries/components/application.toast';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../configuration/i18n/i18n';
import {Theme} from '../../../../configuration/theme/app.theme';
import {ApiInstantsLoader} from '../../../../instantscontext/adapters/secondaries/real/apiInstants.loader';
import {InstantInformation} from '../../../domain/entities/instantInformation';
import {StaticInstantItem2} from '../listing_common/staticInstantItem';
import { InstantHeader } from 'src/v1/instantscontext/domain/entities/instantHeader';
import { http, t, util } from 'src/_helpers';
import { InstantMapper } from 'src/v1/instantscontext/adapters/secondaries/real/mappers/instant.mapper';

interface Props {
  navigation: any;
  loadInstantsByBusiness: (businessId: string) => void;
  reloadInstantsByBusiness: (businessId: string) => void;
  deleteInstant: (instantId: string, businessId: string) => void;
  loading: boolean;
  errorLoading: string;
  successLoading: boolean;
  instants: InstantInformation[];
  businessId: string;
  business: Business;
  profile: any;
  userPosition: any;
  loadRemoteBusiness: (businessId: string) => void;
}

interface State {
  infoInstant: InstantInformation[];
  headerInstant: InstantHeader[];
  delLoading: boolean;
  userPosition: any;
}

export class BusinessReservedInstantsListContainer extends PureComponent<
  Props,
  State
> {
  constructor(props) {
    super(props);
    this.state = {
      infoInstant: [],
      headerInstant: [],
      delLoading: false,
      userPosition: null,
    };
  }

  static navigationOptions = ({navigation}) => {
    return {
      headerTitle: (
        <HeaderTitle
          title={I18n.getTranslation().business.my_reserved_instants}
        />
      ),
      headerLeft: (
        <HeaderButtonIcon
          CTA={() => navigation.navigate('more')}
          iconName={'angle-left'}
          size={20}
          color={'black'}
        />
      ),
      headerStyle: {
        backgroundColor: 'white',
        elevation: 0,
        shadowOpacity: 0,
        height: 40,
      },
    };
  };

  static getDerivedStateFromProps(props: Props, state: State) {
    if (props.instants && state.infoInstant !== props.instants)
      return {...state, infoInstant: props.instants};
    if (props.errorLoading)
      ApplicationToast.error(
        I18n.getTranslation().instant_submission.server_error,
      );
    return null;
  }

  componentDidMount() {
    if (!this.props.business)
      this.props.loadRemoteBusiness(this.props.businessId);
    /*if (!this.props.instants)
      this.props.loadInstantsByBusiness(this.props.businessId);*/
    util.getCurrentPosition().then(userPosition => {
      this.setState({
        userPosition
      });
    });
  }
/*
  fetchActivityData = async (instantId) => {
    try {
      const res = await http.get(`/api/v2/activities/pro/${instantId}`);
      console.log("Res", res);
      
      return InstantMapper.mapFromRawObjectToInstantHeader(res.data);
    } catch (error) {
      console.error("Error fetching activity data:", error);
    }
  };
*/
  render() {
    return (
      <View style={styles.container}>
        {!this.state.delLoading && (
          <InstantList
            {...this.props}
            loading={this.state.delLoading}
            renderItem={data => this.renderItem(data)}
            renderDimmedItem={data => this.renderDimmedItem(data)}
          />
        )}
        {this.state.delLoading && <Spinner background={null} />}
        {/*  {this.renderBody()} */}
      </View>
    );
  }

  renderBody(): JSX.Element {
    if (this.props.loading) return <Spinner background={null} />;
    else if (this.props.instants && this.props.instants.length > 0)
      return (
        <FlatList
          data={this.state.infoInstant}
          renderItem={data => this.renderItem(data.item)}
          keyExtractor={(item, index) => index.toString()}
        />
      );
    else
      return (
        <Text style={styles.bannerText}>
          {I18n.getTranslation().instant_submission.no_instants_found}
        </Text>
      );
  }

  renderItem(item: any) {
    console.log("Rendering item: ", item);
    return (
      <View style={{marginTop: 15}}>
        <StaticInstantItem2
          instant={item}
          displayDetails={() =>
            this.props.navigation.navigate('instant_details', {
              instantId: item.id_activity,
              dateStart: new Date(item.activity_date_time).toJSON(),
            })
          }
          deleteInstant={(instantId: string, title: string) =>
            this.deleteInstant(instantId, title)
          }
          navigation={this.props.navigation}
          userPosition={this.state.userPosition}
        />
      </View>
    );
  }

  renderDimmedItem(item: any) {
    console.log("Rendering item: ", item);
    return (
      <View style={{marginTop: 15, opacity: 0.5}}>
        <StaticInstantItem2
          instant={item}
          displayDetails={() =>
            this.props.navigation.navigate('instant_details', {
              instantId: item.id_activity,
              dateStart: new Date(item.activity_date_time).toJSON(),
            })
          }
          deleteInstant={(instantId: string, title: string) =>
            this.deleteInstant(instantId, title)
          }
          navigation={this.props.navigation}
          userPosition={this.state.userPosition}
        />
      </View>
    );
  }

  deleteInstant = (instantId: string, title: string) => {
    Alert.alert(
      '',
      I18n.getTranslation().instant_submission.cancel_instant(title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: () => {
            this.setState({delLoading: true});
            ApiInstantsLoader.removeReservedInstants(instantId).then(res => {
              if (!res) {
                ApplicationToast.error('An error occured whiile cancelling !');
              }
              this.setState({delLoading: false});
            });
          },
        },
      ],
      {cancelable: false},
    );
  };
}

const InstantList = (props: any) => {
  console.log('Instants props : ', props);
  const [instants, setInstants] = useState<any[]>([]);
  const [activityHeader, setActivityHeader] = useState([]);
  const [sections, setSections] = useState([]);
  const [loading, setLoading] = useState(
    activityHeader.length > 0 ? props.loading : true,
  );

  // Define the order of statuses
  const statusOrder = {
    'INITIATED': 1,
    'PENDING': 2,
    'SUCCESSFUL': 3,
    'FREE': 4,
    'FAILED': 5,
    'EXPIRED': 6,
  };

  // Map to a new array with more expressive labels
  const statusDescriptions = {
    'SUCCESSFUL': { label: t("Réservations confirmées", "Confirmed reservations") },
    'FREE': { label: t("Gratuit", "Free") },
    'INITIATED': { label: t("Paiement inachevé", "Payment incomplete") },
    'PENDING': { label: t("En attente", "Pending") },
    'FAILED': { label: t("Échoué", "Failed") },
    'EXPIRED': { label: t("Expiré", "Expired") }
  };

  const getStatusLabel = (status) => statusDescriptions[status]?.label || t("Inconnu", "Unknown");

  const fetchActivityData = async (instantId: string) => {
    try {
      const res = await http.get(`/activities/pro/${instantId}`);
      
      return InstantMapper.mapFromRawObjectToInstantHeader(res.data);
    } catch (error) {
      console.error("Error fetching activity data:", error);
    }
  };

  useEffect(() => {
    ApiInstantsLoader.loadReservedInstantsByBusiness(
      props.businessId,
      props.profile.email,
    ).then(res => {
      console.log('Reserved activities: ', res);
      // Assuming res is an array of items
      const fetchPromises = res.map((item: any) => {
        return fetchActivityData(item.id_activity).then(itemHeader => {
          setActivityHeader(prevState => [...prevState, {
            Header: itemHeader,
            paymentStatus: item.status}]);
          console.log('itemHeader: ', itemHeader);
        });
      });

      Promise.all(fetchPromises).then(() => {
        // This block executes after all fetchActivityData promises have resolved
        setLoading(false);
        console.log('activityHeader: ', activityHeader);
      }).catch(error => {
        // Handle any errors if necessary
        console.error('Error fetching data:', error);
        setLoading(false); // Ensure setLoading is called even in case of errors
      });
    });
  }, [props.loading]);

  useEffect(() => {
    const sortedArray = activityHeader.sort((a, b) => {
      return (statusOrder[a._status] || 99) - (statusOrder[b._status] || 99);
    });
    console.log('sortedArray: ', sortedArray);

    const groupedByStatus = sortedArray.reduce((acc, item) => {
      const status = item.paymentStatus;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(item);
      return acc;
    }, {});
    console.log('groupedByStatus: ', groupedByStatus);

    // Transform grouped data into the format expected by SectionList
    const sections = Object.entries(groupedByStatus).map(([status, data]) => ({
      title: status,
      data: data
    })).sort((a, b) => (statusOrder[a.title] || 99) - (statusOrder[b.title] || 99));
    console.log('sections: ', sections);
    setSections(sections);
  }, [loading]);

  if (loading) return <Spinner background={null} />;
  else if (activityHeader.length > 0) {
    console.log('activityHeader: ', activityHeader);
    const renderSectionHeader = ({ section: { title } }) => {
      const label = getStatusLabel(title);
      console.log("title: ", title);
      console.log("label: ", label);
      return (
        <View style={styles.sectionHeader}>
        <Text style={styles.sectionHeaderText}>{label}</Text>
      </View>
      )
    };
  
    return (
      <SectionList
        sections={sections}
        keyExtractor={(item, index) => index.toString()}
        renderItem={item => (item.item.paymentStatus === 'EXPIRED' ? props.renderDimmedItem(item.item.Header) : props.renderItem(item.item.Header))}
        renderSectionHeader={renderSectionHeader}
      />
    );
  }
  else
    return (
      <Text style={styles.bannerText}>
        {I18n.getTranslation().instant_submission.no_instants_found}
      </Text>
    );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  viewButton: {
    borderColor: Theme.logan,
    backgroundColor: 'white',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderRadius: 5,
    height: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    width: '90%',
    zIndex: 3,
    marginVertical: 20,
  },
  textButton: {
    paddingLeft: 10,
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  bannerText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    paddingHorizontal: 15,
    paddingTop: 45,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
  },
  item: {
    backgroundColor: 'white',
    padding: 20,
    marginVertical: 8,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'U8 Bold',
  },
  sectionHeader: {
    backgroundColor: '#f2f2f2',
    padding: 0,
    marginTop: 30,
  },
  sectionHeaderText: {
    fontSize: 16,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
  },
});
