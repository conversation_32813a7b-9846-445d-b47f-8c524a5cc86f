import {connect} from 'react-redux';
import {profileSelector} from '../../../../accountcontext/profilecontext/usecases/profile.selectors';
import {businessSelector} from '../../../../businesscontext/usecases/business.selector';
import {loadRemoteBusiness} from '../../../../businesscontext/usecases/remoteLoading/loadRemoteBusiness.actions';
import {remoteBusinessSelector} from '../../../../businesscontext/usecases/remoteLoading/loadRemoteBusiness.selectors';
import {AppState} from '../../../../configuration/AppState';
import {retrieveInstantsByBusinessId} from '../../../../instantscontext/usecases/instantsByBusiness/instantsByBusinessListing.actions';
import {deleteInstant} from '../../../usecases/delete/deleteInstant.actions';
import {deleteInstantSuccessSelector} from '../../../usecases/delete/deleteInstant.selectors';
import {loadInstantsByBusiness} from '../../../usecases/loadByBusiness/loadByBusiness.actions';
import {
  loadedInstantsByBusinessSelector,
  loadInstantByBusinessErrorSelector,
  loadInstantByBusinessLoadingSelector,
  loadInstantByBusinessSuccessSelector,
} from '../../../usecases/loadByBusiness/loadByBusiness.selector';
import {BusinessReservedInstantsListContainer} from './businessReservedInstantsListContainer';
import {faker} from 'src/_helpers';
import {useBusiness, useLocalUser} from 'src/_hooks';
import {View} from 'react-native';

const mapStateToProps = (state: AppState) => ({
  instants: loadedInstantsByBusinessSelector(state),
  loading: loadInstantByBusinessLoadingSelector(state),
  errorLoading: loadInstantByBusinessErrorSelector(state),
  successLoading: loadInstantByBusinessSuccessSelector(state),
  successDelete: deleteInstantSuccessSelector(state),
  businessId: businessSelector(state).id,
  business: remoteBusinessSelector(state),
  profile: profileSelector(state),
});

const mapDispatchToProps = dispatch => ({
  loadInstantsByBusiness: (businessId: string) =>
    dispatch(loadInstantsByBusiness(businessId)),
  loadRemoteBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
  reloadInstantsByBusiness: (businessId: string) =>
    dispatch(retrieveInstantsByBusinessId(businessId)),
  deleteInstant: (instantId: string, businessId: string) =>
    dispatch(deleteInstant(instantId, businessId)),
});

/*export const BusinessReservedInstantsList = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessReservedInstantsListContainer);*/

export const BusinessReservedInstantsList = (props: any) => {
  const [business] = useBusiness();
  const [user, loading, profile] = useLocalUser();
  if (!profile) {
    return <View />;
  }
  return (
    <BusinessReservedInstantsListContainer
      navigation={props.navigation}
      loadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      reloadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      deleteInstant={function (instantId: string, businessId: string): void {
        console.log(arguments);
      }}
      profile={profile}
      loading={false}
      errorLoading={''}
      successLoading={false}
      instants={[]}
      businessId={business?.id}
      business={business}
      loadRemoteBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
    />
  );
};
