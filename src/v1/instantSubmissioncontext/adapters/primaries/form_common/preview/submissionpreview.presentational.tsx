import React, {PureComponent} from 'react';
import {Dimensions, Image, StyleSheet, Text, View} from 'react-native';
import IconIon from 'react-native-vector-icons/Ionicons';
import {ApplicationIcons} from '../../../../../common/adapters/primaries/applicationIcons';
import {ApplicationIconsColor} from '../../../../../common/adapters/primaries/applicationIconsColor';
import FlexibleImage from 'src/_components/shared/FlexibleImage';

interface Props {
  title: string;
  description: string;
  picture: string;
  instantType: string;
}

export class SubmissionPreview extends PureComponent<Props, any> {
  render() {
    return (
      <View style={styles.container}>
        <View style={styles.backgroundBlueBanner} />
        <View style={styles.itemContainer}>
          <View
            style={[
              styles.item,
              {
                borderLeftColor:
                  ApplicationIconsColor()[this.props.instantType],
              },
            ]}>
            <View style={styles.itemHeader}>
              {this.props.picture && (
                <FlexibleImage
                  style={styles.imageContainer}
                  source={{uri: this.props.picture}}
                />
              )}
              <View style={styles.detailContainer}>
                <View style={styles.itemInformation}>
                  <Text style={styles.title} numberOfLines={1}>
                    {this.props.title}
                  </Text>
                  <View style={styles.icon}>
                    {ApplicationIcons(26, this.props.instantType)}
                  </View>
                </View>
                <View>
                  <Text numberOfLines={2} style={styles.description}>
                    {this.props.description}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.timeLineContainer}>
              <View style={styles.timeLineProgress} />
            </View>
            <View style={styles.itemMore}>
              <View style={styles.more}>
                <IconIon name="ios-timer-outline" size={12} />
                <Text style={styles.textItemMore}>Active</Text>
              </View>
              <View style={styles.more}></View>
            </View>
          </View>
          <View style={styles.shadow} />
        </View>
        <View style={styles.backgroundWhiteBanner} />
      </View>
    );
  }
}

const viewableWidth = Dimensions.get('window').width * 0.94;
const detailContainerWidth = viewableWidth - 94;
const titleContainerWidth = viewableWidth - 94 - 26;

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  backgroundBlueBanner: {
    position: 'absolute',
    backgroundColor: 'white',
    height: 83,
    width: '101%',
    zIndex: 1,
  },
  itemContainer: {
    width: '93%',
    zIndex: 2,
  },
  backgroundWhiteBanner: {
    position: 'absolute',
    backgroundColor: '#FFF',
    marginTop: 83,
    width: '100%',
    height: 35,
    zIndex: 1,
  },
  item: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 5,
    shadowColor: '#555',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 1,
  },
  shadow: {
    height: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    height: 62,
  },
  imageContainer: {
    borderRadius: 10,
    height: 62,
    width: 62,
    backgroundColor: '#CCC',
    overflow: 'hidden',
  },
  image: {
    backgroundColor: '#CCC',
    height: 62,
    width: 62,
    borderColor: '#FFF',
    overflow: 'hidden',
  },
  detailContainer: {
    marginLeft: 8,
    width: detailContainerWidth,
  },
  itemInformation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontFamily: 'U8 Bold',
    width: titleContainerWidth,
  },
  description: {
    fontSize: 12,
    fontFamily: 'U8 Regular',
    color: '#666',
    flexWrap: 'wrap',
  },
  icon: {
    alignSelf: 'flex-start',
  },
  timeLineContainer: {
    marginTop: 10,
    marginBottom: 10,
    height: 2,
    width: '100%',
    backgroundColor: 'lightgrey',
    borderRadius: 4,
  },
  timeLineProgress: {
    height: 2,
    borderRadius: 4,
    width: '40%',
    backgroundColor: '#2b9f53',
  },
  itemMore: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  more: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  textItemMore: {
    fontSize: 12,
    fontFamily: 'U8 Regular',
    marginLeft: 5,
    color: '#666',
  },
});
