import React, {PureComponent} from 'react';
import {StyleSheet, View} from 'react-native';
import {ScheduleType} from '../../../../../../../common/domain/entities/types/AppTypes';
import {FixedScheduleForm} from './component/fixedScheduleForm';
import {FixedSchedulePreview} from './component/fixedSchedulePreview';

interface Props {
  schedule?: ScheduleType;
  onUpdate: (schedule: ScheduleType) => void;
  creation?: boolean;
}

interface State {
  schedule: ScheduleType;
}

export class FixedScheduleContainer extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      schedule: this.props.schedule,
    };
  }

  render() {
    const fixedPreviews = this.state.schedule ? (
      <FixedSchedulePreview
        onPause={() => this.toggleScheduleStatus(this.state.schedule)}
        onRemove={() => this.removeSchedule()}
        style={{marginBottom: 10}}
        schedule={this.state.schedule}
        creation={this.props.creation}
      />
    ) : null;

    return (
      <View style={styles.container}>
        {fixedPreviews}
        <FixedScheduleForm
          creation={this.props.creation}
          onSubmit={(schedule: ScheduleType) => this.addSchedule(schedule)}
          show={!this.state.schedule}
        />
      </View>
    );
  }

  addSchedule(schedule: ScheduleType) {
    this.setState({schedule}, () => {
      const cleanSchedule = this.cleanSchedule();
      this.props.onUpdate(cleanSchedule);
    });
  }

  removeSchedule() {
    this.setState(
      {
        schedule: null,
      },
      () => {
        this.props.onUpdate(null);
      },
    );
  }

  toggleScheduleStatus(schedule: ScheduleType) {
    this.setState(
      {
        schedule: {...schedule, isPaused: !schedule.isPaused},
      },
      () => {
        const cleanSchedule = this.cleanSchedule();
        this.props.onUpdate(cleanSchedule);
      },
    );
  }

  private cleanSchedule(): ScheduleType {
    return this.isUUID(this.state.schedule.id)
      ? {...this.state.schedule, id: ''}
      : this.state.schedule;
  }

  private isUUID(id: string) {
    const regexp = new RegExp(
      /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,
    );
    return regexp.test(id);
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
});
