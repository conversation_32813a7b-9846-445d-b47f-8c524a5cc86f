import React, {PureComponent} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';
import {ApplicationContext} from '../../../../../../../../common/configuration/application.context';
import {ScheduleType} from '../../../../../../../../common/domain/entities/types/AppTypes';
import {Theme} from '../../../../../../../../configuration/theme/app.theme';
import {format} from 'date-fns';
import {stringToDate} from 'src/_helpers';

interface Props {
  schedule: ScheduleType;
  style: ViewStyle;
  onPause: () => void;
  onRemove: () => void;
  creation?: boolean;
}

interface State {
  play: boolean;
}

export class FixedSchedulePreview extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      play: this.props.schedule ? !this.props.schedule.isPaused : true,
    };
  }

  render() {
    const iconColor = this.state.play ? Theme.mountainMeadow : Theme.boulder;
    return (
      <View style={[styles.container, this.props.style]}>
        <View style={styles.displayContainer}>
          <Text style={styles.dateRange}>
            {this.props.schedule.dateRange.label}
          </Text>
          {!this.props.creation && (
            <Text style={styles.duration}>
              {format(
                stringToDate(this.props.schedule.dateRange.startTime),
                'HH:mm',
              )}
              -{' '}
              {format(
                stringToDate(this.props.schedule.dateRange.endTime),
                'HH:mm',
              )}
            </Text>
          )}
        </View>
        <View style={styles.controlContainer}>
          {!this.props.creation && (
            <TouchableOpacity
              onPress={() => {
                this.setState({play: !this.state.play});
                this.props.onPause();
              }}>
              <IconFA
                style={{paddingRight: 20}}
                name={'play-circle'}
                size={30}
                color={iconColor}
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={() => this.props.onRemove()}>
            <IconFA name={'remove'} size={30} color={Theme.flamingo} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#EEE',
    paddingTop: 10,
    paddingBottom: 10,
    width: '100%',
  },
  displayContainer: {
    flex: 3,
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    paddingLeft: 10,
  },
  controlContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingRight: 5,
  },
  dateRange: {
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  duration: {
    fontSize: 14,
    fontFamily: 'U8 Regular',
  },
});
