import React, { PureComponent } from 'react';
import { StyleSheet, Switch, Text, View, ViewStyle } from 'react-native';
import { I18n } from '../../../../../../../../configuration/i18n/i18n';

interface Props {
    style?: ViewStyle
    onValueChange: (isActive) => void
}

interface State {
    isActive: boolean
}

export class TimeDelayInput extends PureComponent<Props, State> {
    constructor(props) {
        super(props)
        this.state = {
            isActive: false
        }
    }

    render() {
        return (
            <View style={[styles.container, this.props.style]}>
                <Text style={styles.text}>{I18n.getTranslation().instant_submission.show_two_hours_before}</Text>
                <Switch value={this.state.isActive}
                        onValueChange={isActive => this.setState({ isActive }, () => this.props.onValueChange(isActive))}/>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container: {
        marginTop     : 10,
        flexDirection : 'row',
        justifyContent: 'space-between',
        alignItems    : 'center'
    },
    text: {
        fontSize  : 14,
        fontFamily: 'U8 Regular'
    }
})
