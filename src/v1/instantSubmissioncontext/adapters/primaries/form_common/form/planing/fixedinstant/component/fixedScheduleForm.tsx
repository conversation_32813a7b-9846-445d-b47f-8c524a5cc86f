import React, {PureComponent} from 'react';
import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import {DefaultButton} from '../../../../../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import {ScheduleType} from '../../../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../../../../configuration/theme/app.theme';

import {v4 as uuidv4} from 'uuid';
import {DatePickerContainer} from 'src/_components';
import {format} from 'date-fns';
import {stringToDate} from 'src/_helpers';

interface State {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  showErrorMsg: boolean;
  error: boolean;
}

interface Props {
  style?: ViewStyle;
  show: boolean;
  creation?: boolean;
  onSubmit: (schedule: ScheduleType) => void;
}

export class FixedScheduleForm extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      startDate: undefined,
      endDate: undefined,
      startTime: undefined,
      endTime: undefined,
      showErrorMsg: null,
      error: null,
    };
  }

  render() {
    const errorMsg = this.state.showErrorMsg ? (
      <Text style={styles.error}>
        {I18n.getTranslation().instant_submission.activation_date_error}
      </Text>
    ) : null;
    if (this.props.show)
      return (
        <View style={[styles.container, this.props.style]}>
          <View style={styles.content}>
            <DatePickerContainer
              style={{width: !this.props.creation ? '48%' : '100%'}}
              value={this.state.startDate}
              mode={'date'}
              error={this.state.error}
              format={'dd MMM yyyy'}
              placeholder={
                this.props.creation
                  ? I18n.getTranslation().instant_submission.start_date2
                  : I18n.getTranslation().instant_submission.start_date
              }
              onChange={startDate =>
                {
                  this.setState({
                    startDate,
                    error: null,
                    showErrorMsg: null,
                  });
                }
              }
            />
            {!this.props.creation && (
              <DatePickerContainer
                style={{width: '48%'}}
                value={this.state.startTime}
                format={'HH:mm'}
                mode={'time'}
                error={this.state.error}
                placeholder={
                  this.props.creation
                    ? I18n.getTranslation().instant_submission.start_time2
                    : I18n.getTranslation().instant_submission.start_time
                }
                onChange={startTime =>
                  {
                    let tempStartDate = this.state.startDate ? new Date(this.state.startDate) : new Date();

                    startTime.setFullYear(tempStartDate.getFullYear());
                    startTime.setMonth(tempStartDate.getMonth());
                    startTime.setDate(tempStartDate.getDate());
                    
                    this.setState({
                      startTime,
                      error: null,
                      showErrorMsg: null,
                    });
                  }
                }
              />
            )}
          </View>
          {!this.props.creation && (
            <View style={[styles.content, {marginTop: 10}]}>
              <DatePickerContainer
                style={{width: '48%'}}
                value={this.state.endDate}
                mode={'date'}
                error={this.state.error}
                format={'dd MMM yyyy'}
                placeholder={I18n.getTranslation().instant_submission.end_date}
                onChange={endDate =>
                  {
                    this.setState({
                      endDate,
                      error: null,
                      showErrorMsg: null,
                    });
                  }
                }
              />
              <DatePickerContainer
                style={{width: '48%'}}
                value={this.state.endTime}
                format={'HH:mm'}
                mode={'time'}
                error={this.state.error}
                placeholder={I18n.getTranslation().instant_submission.end_time}
                onChange={endTime =>
                  {
                    let tempEndDate = this.state.endDate ? new Date(this.state.endDate) : new Date();

                    endTime.setFullYear(tempEndDate.getFullYear());
                    endTime.setMonth(tempEndDate.getMonth());
                    endTime.setDate(tempEndDate.getDate());
                    
                    this.setState({
                      endTime,
                      error: null,
                      showErrorMsg: null,
                    });
                  }
                }
              />
            </View>
          )}
          {errorMsg}
          <DefaultButton
            onPress={() => this.onSubmit()}
            style={{marginTop: 10}}
            label={I18n.getTranslation().instant_submission.save_time}
          />
        </View>
      );
    else return null;
  }

  onSubmit() {
    if (this.validateForm()) {
      const startDate = format(
        stringToDate(this.state.startDate),
        'dd MMM yyyy',
      );
      const endDate = format(stringToDate(this.state.endDate), 'dd MMM yyyy');
      
      const schedule: ScheduleType = {
        id: uuidv4(),
        dateRange: {
          startTime: this.state.startTime,
          endTime: this.state.endTime,
          label: !this.props.creation
            ? `${startDate} - ${endDate}`
            : `${startDate}`,
          key: 'fixed',
        },
        isPaused: false,
        prePoned: false,
      };
      this.props.onSubmit(schedule);
    } else this.setState({error: true});
  }

  validateForm() {
    if (!this.props.creation) {
      for (const key in this.state)
        if (key !== 'error' && this.state[key] === undefined) return false;
      /*const startDateTime = stringToDate(
        this.state.startDate + ' ' + this.state.startTime,
      );
      const endDateTime = stringToDate(
        this.state.endDate + ' ' + this.state.endTime,
      );

      const duration = .duration(endDateTime.diff(startDateTime));
      if (duration.asMinutes() < 5) {
        this.setState({showErrorMsg: true});
        return false;
      }*/
    } else {
      if (this.state.startDate === undefined) {
        return false;
      }
    }
    return true;
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 207,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  error: {
    color: Theme.flamingo,
    marginTop: 6,
  },
});
