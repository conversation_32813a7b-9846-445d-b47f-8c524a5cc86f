import React, {PureComponent} from 'react';
import {StyleSheet, View} from 'react-native';
import {ScheduleForm} from '../../../../../../../common/adapters/primaries/components/form/businesshours/schedule.form';
import {SchedulePresentational} from '../../../../../../../common/adapters/primaries/components/form/businesshours/schedule.presentational';
import {DefaultButton} from '../../../../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import {
  ChoiceType,
  ScheduleType,
} from '../../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {ActiveDaysNames} from '../../../../../../../configuration/setting/app.settings';
import {TimeDelayInput} from './component/timeDelay.input';

interface Props {
  schedules?: ScheduleType[];
  onUpdate: (schedules: ScheduleType[]) => void;
  creation?: boolean;
}

interface State {
  choiceOption: ChoiceType[];
  displayForm: boolean;
  preponed: boolean;
  schedules: ScheduleType[];
}

export class RecurrentSchedulerContainer extends PureComponent<Props, State> {
  static defaultProps = {
    schedules: [],
  };

  constructor(props) {
    super(props);
    this.state = {
      choiceOption: this.generateActiveDaysLabels(),
      displayForm: false,
      preponed: false,
      schedules: this.props.schedules,
    };
  }

  render() {
    const recurrentPreviews = this.state.schedules.map(
      (schedule: ScheduleType) => {
        return (
          <SchedulePresentational
            creation={this.props.creation}
            onPause={() => this.toggleScheduleStatus(schedule)}
            onRemove={() => this.removeSchedule(schedule)}
            style={{marginBottom: 10}}
            schedule={schedule}
            key={Math.random() + new Date().getTime()}
          />
        );
      },
    );

    const addScheduleButton = !this.state.displayForm ? (
      <DefaultButton
        onPress={() => this.setState({displayForm: true})}
        label={I18n.getTranslation().instant_submission.add_schedule}
      />
    ) : null;

    return (
      <View style={styles.container}>
        {recurrentPreviews}
        <ScheduleForm
          style={{height: 207}}
          creation={this.props.creation}
          onSubmit={(schedule: ScheduleType) => this.addSchedule(schedule)}
          show={this.state.displayForm}>
          {!this.props.creation && (
            <TimeDelayInput
              onValueChange={preponed => this.setState({preponed})}
            />
          )}
        </ScheduleForm>
        {addScheduleButton}
      </View>
    );
  }

  addSchedule(schedule: ScheduleType) {
    this.setState(
      {
        schedules: [
          ...this.state.schedules,
          {...schedule, prePoned: this.state.preponed},
        ],
        displayForm: false,
        preponed: false,
      },
      () => this.cleanAndUpdate(),
    );
  }

  removeSchedule(schedule: ScheduleType) {
    this.setState(
      {
        schedules: this.state.schedules.filter(item => item.id !== schedule.id),
      },
      () => this.cleanAndUpdate(),
    );
  }

  toggleScheduleStatus(schedule: ScheduleType) {
    this.setState(
      {
        schedules: this.state.schedules.map(item => {
          if (item.id === schedule.id)
            return {...item, isPaused: !schedule.isPaused};
          return item;
        }),
      },
      () => this.cleanAndUpdate(),
    );
  }

  generateActiveDaysLabels = (): ChoiceType[] => {
    const result = [];
    for (const key in ActiveDaysNames)
      if (ActiveDaysNames.hasOwnProperty(key))
        result.push({
          value: key,
          label: ActiveDaysNames[key],
        });

    return result;
  };

  private cleanAndUpdate() {
    const cleanedSchedules = this.state.schedules.map(item => {
      if (this.isUUID(item.id)) return {...item, id: ''};
      return item;
    });
    this.props.onUpdate(cleanedSchedules);
  }

  private isUUID(id: string) {
    const regexp = new RegExp(
      /^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,
    );
    return regexp.test(id);
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
  },
});
