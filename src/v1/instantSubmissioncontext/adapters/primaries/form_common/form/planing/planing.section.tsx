import React, {PureComponent} from 'react';
import {View} from 'react-native';
import {TabViewContainer} from '../../../../../../common/adapters/primaries/components/tabulation/TabView.container';
import {
  ScheduleType,
  TabulationType,
} from '../../../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {InstantSubmissionType} from '../../formTypes';
import {FixedScheduleContainer} from './fixedinstant/fixedschedule.container';
import {RecurrentSchedulerContainer} from './recurrentinstant/recurrentscheduler.container';

interface Props {
  instant?: InstantSubmissionType;
  raiseUpdates: (
    key: 'recurrent' | 'fixed',
    value: ScheduleType[] | ScheduleType,
  ) => void;
  hasError: boolean;
  creation?: boolean;
}

interface State {
  index: number;
  tabs: TabulationType[];
  fixedSchedule: ScheduleType;
  recurrentSchedule: ScheduleType[];
}

export class PlaningSection extends PureComponent<Props, State> {
  private fixedSchedule: ScheduleType;
  private recurrentSchedule: ScheduleType[];

  constructor(props) {
    super(props);

    if (this.props.instant.schedules) {
      this.fixedSchedule =
        this.props.instant.schedules.key === 'fixed'
          ? this.props.instant.schedules.schedule[0]
          : null;
      this.recurrentSchedule =
        this.props.instant.schedules.key === 'recurrent'
          ? this.props.instant.schedules.schedule
          : [];
    }

    this.state = {
      index: 0,
      tabs: [
        {
          key: 'fixed',
          title: this.props.creation
            ? I18n.getTranslation().instant_submission.fixed2
            : I18n.getTranslation().instant_submission.fixed,
          content: (
            <FixedScheduleContainer
              creation={this.props.creation}
              schedule={this.fixedSchedule}
              onUpdate={(schedule: ScheduleType) =>
                this.updates('fixed', schedule)
              }
            />
          ),
        },
        {
          key: 'recurrent',
          title: this.props.creation
            ? I18n.getTranslation().instant_submission.recurrent2
            : I18n.getTranslation().instant_submission.recurrent,
          content: (
            <RecurrentSchedulerContainer
              creation={this.props.creation}
              schedules={this.recurrentSchedule}
              onUpdate={(schedules: ScheduleType[]) =>
                this.updates('recurrent', schedules)
              }
            />
          ),
        },
      ],
      fixedSchedule: this.fixedSchedule,
      recurrentSchedule: this.recurrentSchedule,
    };
  }

  render() {
    return (
      <View>
        <TabViewContainer
          currentTab={this.fixedSchedule ? 0 : 1}
          onTabPress={(tabIndex: number) => this.onTabPress(tabIndex)}
          tabs={this.state.tabs}
        />
      </View>
    );
  }

  updates(key, schedule) {
    if (key === 'fixed')
      this.setState(
        {
          fixedSchedule: schedule,
        },
        () => this.props.raiseUpdates('fixed', [schedule]),
      );
    else
      this.setState(
        {
          recurrentSchedule: schedule,
        },
        () => this.props.raiseUpdates('recurrent', schedule),
      );
  }

  onTabPress(tabIndex: number) {
    if (tabIndex === 0)
      this.props.raiseUpdates('fixed', [this.state.fixedSchedule]);
    else this.props.raiseUpdates('recurrent', this.state.recurrentSchedule);
  }
}
