import React, {PureComponent} from 'react';
import {<PERSON>rollView, StyleSheet, Text, View} from 'react-native';
import {AddressMap} from '../../../../../../common/adapters/primaries/components/form/fields/addressMap.field';
import {DefaultTextInput} from '../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import {ChoicePickerContainer} from '../../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import {Spinner} from '../../../../../../common/adapters/primaries/components/spinner.presentational';
import {Coordinates} from '../../../../../../common/domain/entities/Coordinates';
import {ChoiceType} from '../../../../../../common/domain/entities/types/AppTypes';
import {FormValidation} from '../../../../../../common/usecases/specifications/formValidation';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {Countries} from '../../../../../../configuration/setting/app.settings';
import {Theme} from '../../../../../../configuration/theme/app.theme';
import {InstantSubmissionFormKey, InstantSubmissionType} from '../../formTypes';
import {SubmissionFormValidator} from '../../validation/submission.validator';

interface Props {
  instant?: InstantSubmissionType;
  raiseUpdates: (
    key: InstantSubmissionFormKey,
    value: string | Coordinates,
  ) => void;
  geocodeAddress: (
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ) => void;
  location: Coordinates;
  loading: boolean;
  virtual: boolean;
  geocodeError: string;
  iconType: string;
  hasError: boolean;
}

interface State {
  address: string;
  city: string;
  country: string;
  zip: string;
  addressPosition: Coordinates;
  positionTmp: {latitude: string; longitude: string};
  addressError: boolean;
  cityError: boolean;
  zipError: boolean;
  latitudeError: boolean;
  longitudeError: boolean;
}

export class LocationSectionContainer extends PureComponent<Props, State> {
  private previousAddress: {
    zipCode: string;
    city: string;
    country: string;
    address: string;
  };

  constructor(props) {
    super(props);

    this.state = {
      address: '',
      city: '',
      country: '',
      zip: '',
      addressPosition: {latitude: undefined, longitude: undefined},
      positionTmp: {latitude: undefined, longitude: undefined},
      addressError: null,
      cityError: null,
      zipError: null,
      latitudeError: null,
      longitudeError: null,
    };
  }

  static getDerivedStateFromProps(props: Props, state: State) {
    if (props.instant && state.address === '')
      return {
        address: props.virtual ? '' : props.instant.address,
        city: props.instant.city,
        country: props.instant.country,
        zip: props.instant.zip,
        addressPosition: {
          latitude: props.instant.coordinates?.latitude,
          longitude: props.instant.coordinates?.longitude,
        },
        positionTmp: {
          latitude: props.instant.coordinates?.latitude
            ? props.instant.coordinates?.latitude.toString()
            : '',
          longitude: props.instant.coordinates?.longitude
            ? props.instant.coordinates?.longitude.toString()
            : '',
        },
      };
    return null;
  }

  componentDidMount() {
    this.previousAddress = {
      address: this.state.address,
      city: this.state.city,
      country: this.state.country,
      zipCode: this.state.zip,
    };
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.location &&
      JSON.stringify(this.props.location) !== JSON.stringify(prevProps.location)
    )
      this.setState(
        {
          addressPosition: this.props.location,
          positionTmp: {
            latitude: this.props.location.latitude
              ? this.props.location.latitude.toString()
              : '',
            longitude: this.props.location.longitude
              ? this.props.location.longitude.toString()
              : '',
          },
        },
        () => {
          this.props.raiseUpdates('coordinates', this.props.location);
        },
      );
    if (this.props.hasError) this.checkIfThereIsError();
  }

  render() {
    const addressErrorView = this.props.geocodeError ? (
      <View style={styles.errorView}>
        <Text style={styles.error}>
          {I18n.getTranslation().instant_submission.address_not_found}
        </Text>
      </View>
    ) : null;

    const spinner = this.props.loading ? (
      <Spinner background={'rgba(200, 200, 200, 0.4)'} />
    ) : null;
    return (
      <ScrollView>
        {spinner}
        <DefaultTextInput
          value={this.state.address}
          style={{marginTop: 10}}
          placeholder={
            !this.props.virtual
              ? I18n.getTranslation().instant_submission.address
              : I18n.getTranslation().instant_submission.address2
          }
          error={this.state.addressError}
          onChange={address => {
            this.setState({address});
            this.props.raiseUpdates('address', address);
          }}
          onBlur={() => this.getCoordinatesFromAddress()}
        />

        {!this.props.virtual && (
          <>
            <View style={styles.addressContainer}>
              <DefaultTextInput
                value={this.state.city}
                style={{marginTop: 10, width: '58%'}}
                placeholder={I18n.getTranslation().instant_submission.city}
                error={this.state.cityError}
                onChange={city => {
                  this.setState({city});
                  this.props.raiseUpdates('city', city);
                }}
                onBlur={() => this.getCoordinatesFromAddress()}
              />
              <DefaultTextInput
                value={this.state.zip}
                keyboardType={'numeric'}
                style={{marginTop: 10, width: '38%'}}
                error={this.state.zipError}
                placeholder={I18n.getTranslation().instant_submission.zip}
                onChange={zip => {
                  this.setState({zip});
                  this.props.raiseUpdates('zip', zip);
                }}
                onBlur={() => this.getCoordinatesFromAddress()}
              />
            </View>
            <ChoicePickerContainer
              style={{marginTop: 10}}
              placeholder={I18n.getTranslation().instant_submission.country}
              value={this.state.country}
              options={this.getCountryList()}
              onChange={country => {
                this.setState({country: country.value}, () => {
                  this.props.raiseUpdates('country', country.value);
                  this.getCoordinatesFromAddress();
                });
              }}
            />
            {addressErrorView}
            <AddressMap
              style={{marginTop: 10}}
              iconType={this.props.iconType}
              position={this.state.addressPosition}
              draggableMarker={true}
              raiseUpdates={(key, value) => this.props.raiseUpdates(key, value)}
            />
            <DefaultTextInput
              style={{marginTop: 10}}
              placeholder={I18n.getTranslation().instant_submission.latitude}
              keyboardType={'numeric'}
              onChange={value => {
                const location = {
                  latitude: value,
                  longitude: this.state.positionTmp.longitude,
                };
                this.setState({
                  positionTmp: location,
                  latitudeError:
                    !SubmissionFormValidator.checkCoordinate(value),
                });
              }}
              error={this.state.latitudeError}
              value={this.state.positionTmp.latitude}
              onBlur={() => this.convertCoordinateToPosition()}
            />
            <DefaultTextInput
              style={{marginTop: 10}}
              placeholder={I18n.getTranslation().instant_submission.longitude}
              keyboardType={'numeric'}
              onChange={value => {
                /*const parts = value.split(".");
              const parts2 =
                parts[1].length > 7 ? parts[1].substring(0, 7) : parts[1];*/

                const location = {
                  latitude: this.state.positionTmp.latitude,
                  longitude: value,
                };
                /*const location = {
                latitude: this.state.positionTmp.latitude,
                longitude: `${parts[0]}.${parts2}`,
              };*/
                this.setState({
                  positionTmp: location,
                  longitudeError:
                    !SubmissionFormValidator.checkCoordinate(value),
                });
              }}
              error={this.state.longitudeError}
              onBlur={() => this.convertCoordinateToPosition()}
              value={this.state.positionTmp.longitude}
            />
          </>
        )}
      </ScrollView>
    );
  }

  private convertCoordinateToPosition() {
    let location: Coordinates = {latitude: undefined, longitude: undefined};
    if (this.state.positionTmp.latitude && this.state.positionTmp.longitude)
      location = {
        latitude: parseFloat(
          parseFloat(this.state.positionTmp.latitude).toFixed(7),
        ),
        longitude: parseFloat(
          parseFloat(this.state.positionTmp.longitude).toFixed(7),
        ),
      };
    this.setState({addressPosition: location}, () => {
      this.props.raiseUpdates('coordinates', location);
    });
  }

  private getCountryList(): ChoiceType[] {
    const result = [];
    Countries().map(item => {
      result.push({
        label: item.name,
        value: item.code,
      });
    });
    return result;
  }

  private getCoordinatesFromAddress() {
    /*if (
      !this.state.addressPosition.longitude &&
      !this.state.addressPosition.latitude
    )*/
    if (this.isValidForm() && this.compareTheNewAndPreviousAddress()) {
      this.previousAddress = {
        address: this.state.address,
        city: this.state.city,
        country: this.state.country,
        zipCode: this.state.zip,
      };
      this.props.geocodeAddress(
        this.state.zip,
        this.state.city,
        this.state.country,
        this.state.address,
      );
    }
  }

  private checkIfThereIsError() {
    this.setState({
      addressError: !FormValidation.validation('address', this.state.address),
      cityError: !SubmissionFormValidator.checkCity(this.state.city),
      zipError: !FormValidation.validation('zipCode', this.state.zip),
      latitudeError: !SubmissionFormValidator.checkCoordinate(
        this.state.positionTmp.latitude,
      ),
      longitudeError: !SubmissionFormValidator.checkCoordinate(
        this.state.positionTmp.longitude,
      ),
    });
  }

  private isValidForm() {
    return (
      this.state.address.length > 5 &&
      this.state.city.length > 3 &&
      this.state.country.length > 0 &&
      FormValidation.validation('zipCode', this.state.zip)
    );
  }

  private compareTheNewAndPreviousAddress(): boolean {
    return (
      this.state.address !== this.previousAddress.address ||
      this.state.zip !== this.previousAddress.zipCode ||
      this.state.city !== this.previousAddress.city ||
      this.state.country !== this.previousAddress.country
    );
  }
}

const styles = StyleSheet.create({
  content: {
    width: '100%',
    paddingTop: 0,
    paddingBottom: 0,
  },
  addressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  errorView: {
    paddingTop: 5,
    paddingBottom: 5,
  },
  error: {
    fontSize: 14,
    color: Theme.flamingo,
    fontFamily: 'U8 Bold',
  },
});
