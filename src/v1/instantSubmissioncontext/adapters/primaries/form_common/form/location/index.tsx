import {connect} from 'react-redux';
import {
  geocodeAddressCoordinatesSelector,
  geocodeAddressErrorSelector,
  geocodeAddressLoadingSelector,
} from '../../../../../../common/usecases/addressgeocode/adressgeocode.selector';
import {geocodeAddress} from '../../../../../../common/usecases/addressgeocode/geocodeaddress.action';
import {AppState} from '../../../../../../configuration/AppState';
import {LocationSectionContainer} from './locationSection.container';
import {Coordinates} from 'src/v1/common/domain/entities/Coordinates';
import {InstantSubmissionFormKey} from '../../formTypes';
import {faker, util} from 'src/_helpers';
import {useUserLocation} from 'src/_hooks';

const mapStateToProps = (state: AppState) => ({
  location: geocodeAddressCoordinatesSelector(state),
  loading: geocodeAddressLoadingSelector(state),
  geocodeError: geocodeAddressErrorSelector(state),
});
const mapStateToProps2 = (state: AppState) => ({
  location: geocodeAddressCoordinatesSelector(state),
  loading: geocodeAddressLoadingSelector(state),
  virtual: true,
  geocodeError: geocodeAddressErrorSelector(state),
});
const mapDispatchToProps = dispatch => ({
  geocodeAddress: (
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ) => dispatch(geocodeAddress(zipCode, city, country, address)),
});

/*export const LocationSection = connect(
    mapStateToProps,
    mapDispatchToProps
)(LocationSectionContainer)

export const LocationSection2 = connect(
    mapStateToProps2,
    mapDispatchToProps
)(LocationSectionContainer)*/

export const LocationSection = (props: any) => {
  const [location] = useUserLocation();

  return (
    <LocationSectionContainer
      raiseUpdates={function (
        key: InstantSubmissionFormKey,
        value: string | Coordinates,
      ): void {
        props.raiseUpdates(key, value);
      }}
      geocodeAddress={async function (
        zipCode: string,
        city: string,
        country: string,
        address: string,
      ) {
        const res = await util.geocodeAddress(zipCode, city, country, address);

        if (res?.longitude && res?.latitude) {
          props.raiseUpdates('coordinates', res);
        }
      }}
      location={props?.instant?.coordinates ?? location}
      loading={false}
      virtual={false}
      geocodeError={''}
      iconType={''}
      hasError={false}
      instant={props.instant}
    />
  );
};

export const LocationSection2 = (props: any) => {
  const [location] = useUserLocation();
  return (
    <LocationSectionContainer
      raiseUpdates={function (
        key: InstantSubmissionFormKey,
        value: string | Coordinates,
      ): void {
        props.raiseUpdates(key, value);
      }}
      geocodeAddress={function (
        zipCode: string,
        city: string,
        country: string,
        address: string,
      ): void {
        console.log(arguments);
      }}
      location={props?.instant?.coordinates ?? location}
      loading={false}
      virtual={true}
      geocodeError={''}
      iconType={''}
      hasError={false}
      instant={props.instant}
    />
  );
};
