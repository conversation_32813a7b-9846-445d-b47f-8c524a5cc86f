import React, { PureComponent } from 'react'
import { View } from 'react-native';
import { DefaultTextInput } from '../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import { I18n } from '../../../../../../configuration/i18n/i18n';

interface Props {
    price: string
    onChange: (price: string) => void
}

interface State {
    value: string
}

export class PriceSection extends PureComponent<Props, State> {
    constructor(props) {
        super(props)
        this.state = {
            value: this.props.price
        }
    }

    render() {
        return (
            <View style={{ flex: 1, width: '100%' }}>
                <DefaultTextInput placeholder={I18n.getTranslation().instant_submission.price}
                                  value={this.state.value}
                                  onChange={price => {
                                      this.setState({ value: price })
                                      this.props.onChange(price)
                                  }
                                  }/>
            </View>
        )
    }
}

export class PriceSection2 extends PureComponent<Props, State> {
    constructor(props) {
        super(props)
        this.state = {
            value: this.props.price
        }
    }

    render() {
        return (
            <View style={{ flex: 1, width: '100%' }}>
                <DefaultTextInput placeholder={I18n.getTranslation().instant_submission.price}
                                  value={this.state.value}
                                  onChange={price => {
                                      this.setState({ value: price })
                                      this.props.onChange(price)
                                  }
                                  }/>
            </View>
        )
    }
}