import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Theme } from 'src/v1/configuration/theme/app.theme';
import Icon from 'react-native-vector-icons/FontAwesome';
import CheckBox from 'expo-checkbox';

export const CheckboxComponent = ({ label, value, onValueChange }) => {
  const [checked, setChecked] = useState(value === 'true');
  
  return (
    <View
      style={styles.container}
    >
      <CheckBox
        value={checked}
        onValueChange={(value) => {
          setChecked(value);
          onValueChange(value.toString());
        }}
        color={checked ? '#FF00FF' : undefined}
      />
      <Text style={styles.label}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    flex: 1,
    width: '100%',
    borderColor   : Theme.logan,
    borderWidth   : 1,
    borderRadius  : 5,
    fontSize   : 14,
    paddingLeft: 10,
    height : 50,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#ccc',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#FF00FF',
    borderColor: '#FF00FF',
  },
  checkboxInner: {
    width: 12,
    height: 12,
    backgroundColor: '#fff',
    borderRadius: 2,
  },
  label: {
    fontSize: 16,
    fontFamily: 'U8 Bold',
    color: '#333',
    marginLeft: 10,
  },
});