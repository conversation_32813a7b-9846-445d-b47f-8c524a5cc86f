import React, { PureComponent, Component } from 'react'
import { ScrollView, View } from 'react-native';
import { TabViewContainer } from '../../../../../../common/adapters/primaries/components/tabulation/TabView.container';
import { TabulationType } from '../../../../../../common/domain/entities/types/AppTypes';
import { I18n } from '../../../../../../configuration/i18n/i18n';
import { PriceSection, PriceSection2 } from './price.section';

interface Props {
    value: string,
    onChange: (string) => void
}

interface State {
    index: number
    tabs: TabulationType[]
    free: string
}

export class PriceContainer extends PureComponent<Props, State> {
    private free: string

    constructor(props) {
        super(props)
        this.free = this.props.value === '0' || this.props.value === undefined ? 'free' : 'changed'
        this.state = {
            index: 0,
            tabs : [
                {
                    key    : 'free',
                    title  : I18n.getTranslation().instant_submission.free,
                    content: <View/>
                },
                {
                    key    : 'changed',
                    title  : I18n.getTranslation().instant_submission.changed,
                    content: <PriceSection price={this.props.value} onChange={this.props.onChange}/>
                }
            ],
            free : this.free
        }
    }

    render() {
        return (
            <ScrollView style={{ minHeight: 60 }}>
                <TabViewContainer currentTab={this.free === 'free' ? 0 : 1}
                                  onTabPress={(tabIndex: number) => this.onTabPress(tabIndex)} tabs={this.state.tabs}/>
            </ScrollView>
        )
    }

    onTabPress(tabIndex: number) {
        if (tabIndex === 0)
            this.props.onChange(0)
    }
}


export class PriceContainer2 extends Component<Props, State> {
    private free: string

    constructor(props) {
        super(props)
        this.free = this.props.value === '0' || this.props.value === undefined || this.props.value === '' ? 'free' : 'changed'
        this.state = {
            index: 0,
            tabs : [
                {
                    key    : 'free',
                    title  : I18n.getTranslation().instant_submission.physical,
                    content: <View/>
                },
                {
                    key    : 'changed',
                    title  : I18n.getTranslation().instant_submission.virtual,
                    content: <View/>
                }
            ],
            free : this.free
        }
    }

    render() {
        return (
            <ScrollView style={{ minHeight: 60 }}>
                <TabViewContainer currentTab={this.free === 'free' ? 0 : 1}
                                  onTabPress={(tabIndex: number) => this.onTabPress(tabIndex)} tabs={this.state.tabs}/>
            </ScrollView>
        )
    }

    onTabPress(tabIndex: number) {
        //if (tabIndex === 0)
        this.props.onChange(tabIndex)
    }
}
