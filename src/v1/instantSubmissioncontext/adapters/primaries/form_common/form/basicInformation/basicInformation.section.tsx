import React, {PureComponent} from 'react';
import {<PERSON><PERSON>View} from 'react-native';
import {DefaultTextInput} from '../../../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import {ChoicePickerContainer} from '../../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import {CustomizedCameraContainer} from '../../../../../../common/adapters/primaries/components/form/pickers/customizedCamera.container';
import {ChoiceType} from '../../../../../../common/domain/entities/types/AppTypes';
import {FormValidation} from '../../../../../../common/usecases/specifications/formValidation';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {Categories} from '../../../../../../configuration/setting/app.settings';
import {InstantSubmissionFormKey, InstantSubmissionType} from '../../formTypes';
import {SubmissionFormValidator} from '../../validation/submission.validator';
import {PriceContainer, PriceContainer2} from './price.container';
import { ProOnlyContainer } from './proOnlyContainer';
import { t } from 'src/_helpers';

interface Props {
  instant?: InstantSubmissionType;
  raiseUpdates: (key: InstantSubmissionFormKey, value: string) => void;
  hasError: boolean;
}

interface State {
  type: string;
  title: string;
  price: string;
  virtualLink: string;
  shortDescription: string;
  longDescription: string;
  picture: string;
  proOnly: string;
  typeError: boolean;
  titleError: boolean;
  shortDescriptionError: boolean;
  pictureError: boolean;
  longDescriptionError: boolean;
}

export class BasicInformationSection extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      type: '',
      title: '',
      price: '',
      virtualLink: '',
      shortDescription: '',
      longDescription: '',
      picture: undefined,
      proOnly: null,
      typeError: null,
      titleError: null,
      shortDescriptionError: null,
      pictureError: null,
      longDescriptionError: null,
    };
  }

  static getDerivedStateFromProps(props) {
    
    if (props.instant) {
      return {
        type: props.instant.type,
        price: props.instant.price !== undefined ? props.instant.price : '',
        title: props.instant.title,
        virtualLink: props.instant.virtualLink,
        shortDescription: props.instant.description,
        longDescription: props.instant.longDescription,
        picture: props.instant.picture,
        proOnly: props.instant.proOnly,
      };
    }
    return null;
  }

  componentDidUpdate() {
    if (this.props.hasError) this.checkIfThereIsError();
  }

  render() {
    return (
      <ScrollView>
        <PriceContainer
          value={this.state.price}
          onChange={price => {
            this.setState({price});
            this.props.raiseUpdates('price', price);
          }}
        />
        <ProOnlyContainer
          value={this.state.proOnly}
          onValueChange={proOnly => {
            console.log("Pro only: ", proOnly);
            this.setState({proOnly: proOnly.toString()});
            this.props.raiseUpdates('proOnly', proOnly.toString());
          }}
          label={t("Réserver aux comptes pro (Equipes, associations...)", "Reserve for pro accounts (Teams, associations...)")}
        />
        <PriceContainer2
          value={this.state.virtualLink}
          onChange={virtualLink => {
            this.setState({virtualLink});
            this.props.raiseUpdates('virtualLink', virtualLink);
          }}
        />
        <ChoicePickerContainer
          style={{marginTop: 10}}
          onChange={choice => {
            this.setState({type: choice.value});
            this.props.raiseUpdates('type', choice.value);
          }}
          placeholder={I18n.getTranslation().instant_submission.choose_category}
          error={this.state.typeError}
          value={this.state.type}
          options={this.generateInstantCategories()}
          icon={this.state.type}
        />

        <DefaultTextInput
          value={this.state.title}
          onChange={textValue => {
            this.setState({title: textValue});
            this.props.raiseUpdates('title', textValue);
          }}
          error={this.state.titleError}
          style={{marginTop: 10}}
          placeholder={I18n.getTranslation().instant_submission.title}
        />
        <DefaultTextInput
          value={this.state.shortDescription}
          onChange={textValue => {
            this.setState({shortDescription: textValue});
            this.props.raiseUpdates('description', textValue);
          }}
          style={{marginTop: 10}}
          error={false}
          placeholder={
            I18n.getTranslation().instant_submission.short_description
          }
          numberOfLines={3}
        />

        <CustomizedCameraContainer
          name={I18n.getTranslation().instant_submission.instant_photo}
          onPictureTaken={imageURI =>
            this.setState({picture: imageURI}, () =>
              this.props.raiseUpdates('picture', imageURI),
            )
          }
          transparent={false}
          imagePlaceholder={this.state.picture}
          error={false}
          style={{marginTop: 10}}
        />

        <DefaultTextInput
          //value={this.state.longDescription}
          defaultValue={this.props?.instant?.longDescription ?? I18n.getTranslation().instant_submission.long_description_text}
          style={{marginTop: 10}}
          numberOfLines={15}
          error={false}
          returnKeyType={'next'}
          blurOnSubmit={false}
          placeholder={
            I18n.getTranslation().instant_submission.long_description
          }
          onChange={textValue => {
            this.setState({longDescription: textValue}, () => {
              this.props.raiseUpdates('longDescription', textValue);
            });
          }}
        />
      </ScrollView>
    );
  }

  checkIfThereIsError() {
    this.setState({
      typeError: !SubmissionFormValidator.checkType(this.state.type),
      titleError: !SubmissionFormValidator.checkTitle(this.state.title),
      pictureError: !SubmissionFormValidator.checkPicture(this.state.picture),
      shortDescriptionError: !FormValidation.validation(
        'description',
        this.state.shortDescription,
      ),
      longDescriptionError: !SubmissionFormValidator.checkLongDescription(
        this.state.longDescription,
      ),
    });
  }

  generateInstantCategories = (): ChoiceType[] => {
    const result = [];
    for (const iconName in Categories)
      if (Categories.hasOwnProperty(iconName))
        result.push({
          value: iconName,
          label: I18n.getTranslation().common.settings.categories[iconName],
          iconName,
        });
    return result;
  };
}
