import React, {PureComponent} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';
import {Theme} from '../../../../../../configuration/theme/app.theme';

interface Props {
  name: string;
  id: string;
  icon: string;
  onRemove: () => void;
}

export class PreviewPartner extends PureComponent<Props> {
  render() {
    const icon =
      this.props.icon ||
      'https://e7.pngegg.com/pngimages/784/809/png-clipart-building-small-business-company-office-corporation-office-icon-insharepics-miscellaneous-blue.png';
    return (
      <View style={styles.container}>
        <View style={styles.iconView}>
          {icon && <Image source={{uri: icon}} style={styles.icon} />}
        </View>
        <Text style={styles.label}>{this.props.name}</Text>
        <TouchableOpacity
          style={styles.rightIcon}
          onPress={() => this.props.onRemove()}>
          <IconFA name={'close'} color={'red'} size={25} />
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomColor: Theme.gainsboro,
    borderBottomWidth: 1,
  },
  iconView: {
    flex: 1,
  },
  label: {
    flex: 6,
    fontSize: 16,
    fontFamily: 'U8 Regular',
  },
  rightIcon: {
    flex: 1,
    alignItems: 'flex-end',
  },
  icon: {
    width: 26,
    height: 26,
    borderRadius: 5,
    backgroundColor: Theme.ghost,
  },
});
