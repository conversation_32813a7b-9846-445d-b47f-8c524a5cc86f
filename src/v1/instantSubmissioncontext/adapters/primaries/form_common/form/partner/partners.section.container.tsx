import React, {PureComponent} from 'react';
import {StyleSheet, View} from 'react-native';
import {DefaultButton} from '../../../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import {ChoicePickerModal} from '../../../../../../common/adapters/primaries/components/form/pickers/modals/choiceSelector.modal';
import {ChoiceType} from '../../../../../../common/domain/entities/types/AppTypes';
import {ApplicationSpecification} from '../../../../../../common/usecases/specifications/application.specifications';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {InstantPartner} from '../../../../../domain/entities/instantPartner';
import {InstantSubmissionType} from '../../formTypes';
import {PreviewPartner} from './previewPartner.container';

interface Props {
  instant?: InstantSubmissionType;
  raiseUpdates: (key: 'partners', value: InstantPartner[]) => void;
  hasError: boolean;
  loadPartners: () => void;
  partnersList: InstantPartner[];
}

interface State {
  displayForm: boolean;
  instantPartners: InstantPartner[];
  choicePartners: ChoiceType[];
}

export class PartnersSectionContainer extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      displayForm: false,
      instantPartners: this.props.instant ? this.props.instant.partners : [],
      choicePartners: null,
    };
  }

  static getDerivedStateFromProps(props: Props, prevState: State) {
    if (prevState.choicePartners === null && props.partnersList)
      return {
        choicePartners: PartnersSectionContainer.choiceListPartners(
          props.partnersList,
          props.instant,
        ),
      };
    return prevState;
  }

  private static choiceListPartners(
    partnersList: InstantPartner[],
    instant: InstantSubmissionType,
  ): ChoiceType[] {
    let choiceList: ChoiceType[] = [];
    partnersList.map(item => {
      if (item.id !== instant.businessId) {
        choiceList.push({value: item.id, label: item.name, iconName: item.image});
      } else {
        console.log("Self can't be a partner: ", item);
      }
    });
    if (instant && instant.partners.length > 0)
      instant.partners.map(partner => {
        choiceList = choiceList.filter(choice => choice.value !== partner.id );
      });
    return choiceList;
  }

  componentDidMount() {
    if (!this.props.partnersList) this.props.loadPartners();
  }

  componentDidUpdate(prevProps: Props, prevState: State) {
    if (prevState.instantPartners !== this.state.instantPartners)
      this.setState({
        choicePartners: PartnersSectionContainer.choiceListPartners(
          this.props.partnersList,
          this.props.instant,
        ),
      });
  }

  render() {
    const previewPartners = this.state.instantPartners.map(
      (partner: InstantPartner) => {
        return (
          <PreviewPartner
            key={partner.id}
            name={partner.name}
            id={partner.id}
            icon={partner.image}
            onRemove={() => this.removePartner(partner.id)}
          />
        );
      },
    );
    return (
      <View style={styles.container}>
        {previewPartners}
        <DefaultButton
          style={{marginTop: 10}}
          onPress={() => this.setState({displayForm: true})}
          label={I18n.getTranslation().instant_submission.add_partner}
        />
        <ChoicePickerModal
          isVisible={this.state.displayForm}
          title={I18n.getTranslation().instant_submission.add_partner}
          onChange={(item: ChoiceType) => this.addPartner(item)}
          onClose={() => this.setState({displayForm: false})}
          options={ApplicationSpecification.sortByAlphabetize(
            this.state.choicePartners,
          )}
        />
      </View>
    );
  }

  addPartner(item: ChoiceType) {
    const partners = this.state.instantPartners;
    partners.push(new InstantPartner(item.value, item.label, item.iconName));
    const choicePartners = this.state.choicePartners.filter(
      partner => partner.value !== item.value,
    );
    this.setState({
      instantPartners: partners,
      displayForm: false,
      choicePartners,
    });
    this.props.raiseUpdates('partners', partners);
  }

  removePartner(id: string) {
    const partners = this.state.instantPartners.filter(item => item.id !== id);
    this.setState({instantPartners: partners});
    this.props.raiseUpdates('partners', partners);
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
    height: '100%',
    minHeight: 300,
  },
});
