import {connect} from 'react-redux';
import {AppState} from '../../../../../../configuration/AppState';
import {loadPartners} from '../../../../../usecases/loadPartners/loadPartners.actions';
import {partnersSelector} from '../../../../../usecases/loadPartners/loadPartners.selectors';
import {PartnersSectionContainer} from './partners.section.container';
import {InstantPartner} from 'src/v1/instantSubmissioncontext/domain/entities/instantPartner';
import {InstantSubmissionMapper} from 'src/v1/instantSubmissioncontext/adapters/secondaries/real/mapper/instantSubmissionMapper';
import {useEffect, useState} from 'react';
import {http} from 'src/_helpers';
import {View} from 'react-native';

const mapStateToProps = (state: AppState) => ({
  partnersList: partnersSelector(state),
});

const mapDispatchToProps = dispatch => ({
  loadPartners: () => dispatch(loadPartners()),
});

/*export const PartnersSection = connect(
    mapStateToProps,
    mapDispatchToProps
)(PartnersSectionContainer)*/

export const PartnersSection = (props: any) => {
  const [state, setState] = useState({loading: true, partners: []});

  useEffect(() => {
    (async () => {
      const res = await http.get('/activities/partners?showMine=0');
      if (Array.isArray(res?.data)) {
        setState(prev => ({...prev, partners: res.data, loading: false}));
      } else {
        setState(prev => ({...prev, partners: [], loading: false}));
      }
    })();
  }, []);
  if (state.loading) {
    return <View />;
  }
  return (
    <PartnersSectionContainer
      raiseUpdates={function (key: 'partners', value: InstantPartner[]): void {
        console.log(arguments);
        props.raiseUpdates(key, value);
      }}
      hasError={false}
      loadPartners={function (): void {
        console.log(arguments);
      }}
      partnersList={InstantSubmissionMapper.mapToPartnerArray(state.partners)}
      instant={props.instant}
    />
  );
};
