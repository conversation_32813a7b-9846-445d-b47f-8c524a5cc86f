import {Coordinates} from '../../../../../common/domain/entities/Coordinates';
import {FormValidation} from '../../../../../common/usecases/specifications/formValidation';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {InstantSubmissionScheduleType} from '../formTypes';

interface SubmissionFormProperty {
  title: string;
  description: string;
  type: string;
  picture: string;
  longDescription: string;
  address: string;
  city: string;
  zip: string;
  virtualLink?: string;
  coordinates: Coordinates;
  schedules: InstantSubmissionScheduleType;
}

export class SubmissionFormValidator {
  static validate(form: SubmissionFormProperty): number {
    if (
      !SubmissionFormValidator.checkType(form.type) ||
      !SubmissionFormValidator.checkTitle(form.title)
    ) {
      return 0;
    }
    return undefined;
    if (
      !SubmissionFormValidator.checkType(form.type) ||
      !SubmissionFormValidator.checkTitle(form.title) ||
      //!SubmissionFormValidator.checkPicture(form.picture) ||
      !FormValidation.validation('description', form.description) ||
      !SubmissionFormValidator.checkLongDescription(form.longDescription)
    )
      return 0;
    if (Number(form.virtualLink) === 1) {
      if (!FormValidation.validation('address', form.address)) {
        return 1;
      }
    } else {
      if (
        !FormValidation.validation('address', form.address) ||
        !SubmissionFormValidator.checkCity(form.city) ||
        !FormValidation.validation('zipCode', form.zip) ||
        !SubmissionFormValidator.checkCoordinate(form.coordinates.longitude) ||
        !SubmissionFormValidator.checkCoordinate(form.coordinates.latitude)
      )
        return 1;
    }
    if (!SubmissionFormValidator.checkSchedule(form.schedules)) return 2;
    else return undefined;
  }

  static checkType(type: string): boolean {
    return type !== '' && type !== undefined && type !== null;
  }

  static checkPicture(picture: string): boolean {
    return picture !== undefined;
  }

  static checkSchedule(schedules: InstantSubmissionScheduleType): boolean {
    if (schedules)
      if (
        schedules.key === 'recurrent' &&
        schedules.schedule instanceof Array &&
        schedules.schedule.length > 0
      )
        return true;
      else if (schedules.key === 'fixed' && schedules.schedule) return true;
    return false;
  }

  static checkCity(city: string): boolean {
    return city && city.length > 3;
  }

  static checkTitle(title: string) {
    return (
      title !== I18n.getTranslation().instant_submission.default_title &&
      title.length > 5
    );
  }

  static checkLongDescription(longDescription: string) {
    return (
      longDescription !==
        I18n.getTranslation().instant_submission.long_description_text &&
      FormValidation.validation('description', longDescription)
    );
  }

  static checkCoordinate(coordinate: string | number): boolean {
    const regex =
      /^(\+|-)?(?:90(?:(?:\.0{1,7})?)|(?:[0-9]|[1-8][0-9])(?:(?:\.[0-9]{1,7})?))$/;
    if (coordinate) return regex.test(coordinate.toString());
    return true;
  }
}
