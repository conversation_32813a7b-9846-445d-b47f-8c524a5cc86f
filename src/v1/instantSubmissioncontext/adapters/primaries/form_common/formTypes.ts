import { Coordinates } from '../../../../common/domain/entities/Coordinates';
import { ScheduleType } from '../../../../common/domain/entities/types/AppTypes';
import { InstantPartner } from '../../../domain/entities/instantPartner';

export type InstantSubmissionFormKey =
    'title'
    | 'price'
    | 'type'
    | 'description'
    | 'picture'
    | 'longDescription'
    | 'address'
    | 'coordinate'
    | 'schedules'
    | 'city'
    | 'country'
    | 'zip'
    | 'coordinates'
    | 'partners'
    | 'virtualLink'
    | 'proOnly'

export interface InstantSubmissionScheduleType {
    key: 'recurrent' | 'fixed'
    schedule: ScheduleType[]
}

export interface InstantSubmissionType {
    id: string
    businessId: string
    title: string
    description: string
    price: string
    virtualLink?: string
    type: string
    picture: string
    longDescription: string
    address: string
    city: string
    country: string
    zip: string
    coordinates: Coordinates
    schedules: InstantSubmissionScheduleType
    partners: InstantPartner[]
    proOnly: string
}
