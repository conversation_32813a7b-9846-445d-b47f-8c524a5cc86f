import {connect} from 'react-redux';
import {businessSelector} from '../../../../businesscontext/usecases/business.selector';
import {loadRemoteBusiness} from '../../../../businesscontext/usecases/remoteLoading/loadRemoteBusiness.actions';
import {remoteBusinessSelector} from '../../../../businesscontext/usecases/remoteLoading/loadRemoteBusiness.selectors';
import {AppPreference} from '../../../../common/domain/entities/appPreference';
import {AppState} from '../../../../configuration/AppState';
import {retrieveInstantsByBusinessId} from '../../../../instantscontext/usecases/instantsByBusiness/instantsByBusinessListing.actions';
import {deleteInstant} from '../../../usecases/delete/deleteInstant.actions';
import {deleteInstantSuccessSelector} from '../../../usecases/delete/deleteInstant.selectors';
import {loadInstantsByBusiness} from '../../../usecases/loadByBusiness/loadByBusiness.actions';
import {
  loadedInstantsByBusinessSelector,
  loadInstantByBusinessErrorSelector,
  loadInstantByBusinessLoadingSelector,
  loadInstantByBusinessSuccessSelector,
} from '../../../usecases/loadByBusiness/loadByBusiness.selector';
import {BusinessInstantsListContainer} from './businessInstantsListContainer';
import {BusinessRequestsListContainer} from './businessRequestsListContainer';
import {faker} from 'src/_helpers';

const mapStateToProps = (state: AppState) => ({
  instants: loadedInstantsByBusinessSelector(state),
  loading: loadInstantByBusinessLoadingSelector(state),
  errorLoading: loadInstantByBusinessErrorSelector(state),
  successLoading: loadInstantByBusinessSuccessSelector(state),
  successDelete: deleteInstantSuccessSelector(state),
  businessId: businessSelector(state).id,
  business: remoteBusinessSelector(state),
});

const mapDispatchToProps = dispatch => ({
  loadInstantsByBusiness: (businessId: string) =>
    dispatch(loadInstantsByBusiness(businessId)),
  loadInstantsByBusiness2: (businessId: string, filter: AppPreference) =>
    dispatch(loadInstantsByBusiness(businessId)),
  loadRemoteBusiness: (businessId: string) =>
    dispatch(loadRemoteBusiness(businessId)),
  reloadInstantsByBusiness: (businessId: string) =>
    dispatch(retrieveInstantsByBusinessId(businessId)),
  deleteInstant: (instantId: string, businessId: string) =>
    dispatch(deleteInstant(instantId, businessId)),
});

/*export const BusinessInstantsList = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessInstantsListContainer);

export const BusinessRequestsList = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessRequestsListContainer);

export const BusinessRequestsList2 = connect(
  mapStateToProps,
  mapDispatchToProps,
)(BusinessRequestsListContainer);*/

export const BusinessInstantsList = (props: any) => {
  return (
    <BusinessInstantsListContainer
      navigation={props.navigation}
      loadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      reloadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      deleteInstant={function (instantId: string, businessId: string): void {
        console.log(arguments);
      }}
      loading={false}
      errorLoading={''}
      successLoading={false}
      instants={[]}
      businessId={faker.getApplicationBusiness().id}
      business={faker.getApplicationBusiness()}
      loadRemoteBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
    />
  );
};

export const BusinessRequestsList = (props: any) => {
  return (
    <BusinessRequestsListContainer
      navigation={props.navigation}
      loadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      reloadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      deleteInstant={function (instantId: string, businessId: string): void {
        console.log(arguments);
      }}
      loading={false}
      errorLoading={''}
      successLoading={false}
      instants={[]}
      businessId={faker.getApplicationBusiness().id}
      business={faker.getApplicationBusiness()}
      loadRemoteBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
    />
  );
};

export const BusinessRequestsList2 = (props: any) => {
  return (
    <BusinessRequestsListContainer
      navigation={props.navigation}
      loadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      reloadInstantsByBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
      deleteInstant={function (instantId: string, businessId: string): void {
        console.log(arguments);
      }}
      loading={false}
      errorLoading={''}
      successLoading={false}
      instants={[]}
      businessId={faker.getApplicationBusiness().id}
      business={faker.getApplicationBusiness()}
      loadRemoteBusiness={function (businessId: string): void {
        console.log(arguments);
      }}
    />
  );
};
