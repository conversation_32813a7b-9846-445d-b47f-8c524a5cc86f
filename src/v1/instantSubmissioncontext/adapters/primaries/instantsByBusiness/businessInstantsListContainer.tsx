import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  Flat<PERSON>ist,
  SectionList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Config from 'react-native-config';
import { EventRegister } from 'react-native-event-listeners';
import IconFA from 'react-native-vector-icons/Ionicons';
import { Business } from '../../../../businesscontext/domain/entities/business';
import { ApplicationToast } from '../../../../common/adapters/primaries/components/application.toast';
import { Spinner } from '../../../../common/adapters/primaries/components/spinner.presentational';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../common/adapters/primaries/navigation/navigationHeader';
import { I18n } from '../../../../configuration/i18n/i18n';
import { Theme } from '../../../../configuration/theme/app.theme';
import { CREATION_APP_PREFERENCE_SAVED } from '../../../../helpers/constant.helper';
import { http } from '../../../../helpers/http.helper';
import { InstantInformation } from '../../../domain/entities/instantInformation';
import { InstantInformationDTO } from '../../secondaries/real/dto/instants.dto';
import { InstantSubmissionMapper } from '../../secondaries/real/mapper/instantSubmissionMapper';
import { StaticInstantItem, StaticInstantItemDraft } from '../listing_common/staticInstantItem';
import { util, t, storage } from 'src/_helpers';
import { useGetBusiness } from 'src/_hooks/useGetBusiness';
import { DefaultButton } from 'src/_components';
import {  isValid } from 'date-fns';
import { InstantInformationBuilder } from 'src/v1/instantSubmissioncontext/domain/entities/builder/instantInformation.builder';
import { Constants } from 'src/_constants';

interface Props {
  navigation: any;
  loadInstantsByBusiness: (businessId: string) => void;
  reloadInstantsByBusiness: (businessId: string) => void;
  deleteInstant: (instantId: string, businessId: string) => void;
  loading: boolean;
  errorLoading: string;
  successLoading: boolean;
  instants: InstantInformation[];
  businessId: string;
  business: Business;
  birthDate: string;
  loadRemoteBusiness: (businessId: string) => void;
}

interface State {
  infoInstant: InstantInformation[];
  infoInstant2: InstantInformation[];
  filter: any;
  loading: boolean;
  filtered: boolean;
}

interface ProcessedDraft {
  instantInfo: InstantInformation;
  rawDraft: any; // Type this more specifically if possible
}

export const BusinessInstantsListContainer: React.FC<Props> = (props) => {
  const [infoInstant, setInfoInstant] = useState<InstantInformation[]>([]);
  const [infoInstant2, setInfoInstant2] = useState<InstantInformation[]>([]);
  const [filter, setFilter] = useState<any>(null);
  const [loadingState, setLoadingState] = useState<boolean>(false);
  const [filtered, setFiltered] = useState<boolean>(false);
  const { business, businessLoading, businessError, businessErrorData } = useGetBusiness(props.businessId);
  const [draftsArray, setDraftsArray] = useState<InstantInformation[]>([]);
  const [draftsMap, setDraftsMap] = useState<Record<string, ProcessedDraft>>({});
  
  const staticListenerRef = useRef<any>(null);

  useEffect(() => {
    if (props.instants && infoInstant !== props.instants) {
      setInfoInstant(props.instants);
    }
    if (props.errorLoading) {
      ApplicationToast.error(I18n.getTranslation().instant_submission.server_error);
    }
  }, [props.instants, infoInstant, props.errorLoading]);

  useEffect(() => {
    getDrafts();
    return () => {
      EventRegister.removeEventListener(staticListenerRef.current as string);
    };
  }, []);

  const getDrafts = async () => {
    console.log("Retrieving drafts...");
    try {
      const user = await storage.get(Constants.USER_INFO_KEY);
      console.log("user uuid: ", user?.uuid);
      const storedDrafts = await storage.get(user?.uuid);
      console.log('Stored Drafts: ', storedDrafts);
  
      const processedDraftsArray: InstantInformation[] = [];
      const processedDraftsMap: Record<string, ProcessedDraft> = {};
  
      storedDrafts?.forEach((draft: any) => {
        if (!draft._uniqueId) {
          console.warn('Draft found without _uniqueId, skipping:', draft);
          return; // Skip drafts without _uniqueId
        }
  
        const instantInfo = new InstantInformationBuilder()
          .withCategory(draft._instantInformation._category)
          .withBusinessId(draft._instantInformation._businessId)
          .withTitle(draft._instantInformation._title)
          .withLongDescription(draft._instantInformation._longDescription)
          .withShortDescription(draft._instantInformation._shortDescription)
          .withPicture(draft._instantInformation._picture)
          .withPrice(draft._instantInformation._price)
          .build();
  
        // Add _uniqueId to instantInfo for easy access
        (instantInfo as any)._uniqueId = draft._uniqueId;
  
        processedDraftsArray.push(instantInfo);
  
        processedDraftsMap[draft._uniqueId] = {
          instantInfo,
          rawDraft: draft
        };
      });
  
      console.log("ProcessedDrafts: ", processedDraftsMap);
      console.log("ProcessedDraftsArray: ", processedDraftsArray);
  
      setDraftsArray(processedDraftsArray);
      setDraftsMap(processedDraftsMap);
  
      return { processedDraftsArray, processedDraftsMap };
    } catch (error) {
      console.error('Error in getDrafts:', error);
      throw error;
    }
  };

  useEffect(() => {
    const unsubscribe = props.navigation.addListener('focus', async () => {
      console.log('BusinessInstantsListContainer screen Focused');
      await getDrafts();
      console.log('Drafts: ', draftsArray);
    });

    return unsubscribe;
  }, [props.navigation]);

  //const isAdult = util.isOver18(props.birthDate);

  const isAdult = useMemo(() => {
    return util.isOver18(props.birthDate);
  }, [props.birthDate]);

  const trialEndDate = new Date(business?.subscription?.trialEndDate);
  const now = new Date();
  const isTrialEnded = isValid(trialEndDate) && now >= trialEndDate;
  
  console.log('business?.active:', business?.active);
  console.log('business?.subscription.isSubscriptionActive:', business?.subscription?.isSubscriptionActive);
  console.log('isTrialEnded:', isTrialEnded);

  const spinner = props.loading || loadingState || businessLoading ? <Spinner background={'rgba(0,0,0, 0.2)'} /> : null;

  const renderBody = () => {
    const data = filtered ? infoInstant2 : infoInstant;
    const sections = [
      {
        title: t('Mes activités', 'My Activities'),
        data: data.filter(item => item.businessId === props.businessId),
      },
      {
        title: t('Mes partenariats', 'My Partnerships'),
        data: data.filter(item => item.businessId !== props.businessId),
      },
      {
        title: t('Mes brouillons', 'My Drafts'),
        data: draftsArray,
      },
    ];
    console.log("renderBody Final Drafts: ", draftsArray);
    if (props.loading || loadingState || businessLoading) {
      return <Spinner background={null} />;
    } else if (!business?.active && business?.subscription?.isSubscriptionActive) {
      return (
        <Text style={styles.bannerText}>
          {t("Votre compte est désactivé, veuillez contacter le support.", 'Your account is deactivated, please contact support.')}
        </Text>
      );
    } else if (business?.active && !business?.subscription?.isSubscriptionActive  && isTrialEnded) {
      return (
        <View>
          <Text style={styles.bannerText}>
            {t("Vous n'avez aucun abonnement actif, veuillez souscrire à une de nos formules.", 'You have no active subscription, please subscribe to one of our plans.')}
          </Text>
          <DefaultButton
            onPress={() => props.navigation.navigate('subscription')}
            label={t('Voir nos formules', 'See our plans')}
            style={styles.button}
          />
        </View>
      );
    } else if (props.instants && props.instants.length > 0 && data.length > 0 || draftsArray.length > 0) {
      return (
        <SectionList
          sections={sections}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
          renderSectionHeader={renderSectionHeader}
          stickySectionHeadersEnabled={false}
        />
      );
    } else {
      return (
        <Text style={styles.bannerText}>
          {I18n.getTranslation().instant_submission.no_instants_found}
        </Text>
      );
    }
  };

  const renderSectionHeader = ({ section: { title, data } }) => {
    if (data.length === 0) {
      return null;
    }
    return <Text style={styles.activitiesListTitle}>{title}</Text>;
  };

  const renderItem = ({ item, section }) => {
    if (section.title === t('Mes partenariats', 'My Partnerships')) {
      return renderPartnershipItem(item);
    } else if (section.title === t('Mes brouillons', 'My Drafts')) {
      console.log("Rendering draft...");
      console.log("Item: ", item);
      return renderDraftItem(item as InstantInformation & { _uniqueId: string });
    } else {
      return renderActivityItem(item);
    }
  };

  const renderDraftItem = (item: InstantInformation & { _uniqueId: string }) => {
    console.log("Rendering draft item with uniqueId:", item._uniqueId);
    console.log("DraftsMap : ", draftsMap);
    const fullDraft = draftsMap[item._uniqueId];
    
    if (!fullDraft) {
      console.warn(`No full draft found for uniqueId: ${item._uniqueId}`);
      return null;
    }
  
    const rawDraft = fullDraft.rawDraft;
    console.log("Full Draft:", fullDraft);
    console.log("Raw Draft:", rawDraft);
  
    const handlePress = () => {
      if (rawDraft) {
        console.log("Navigating to DraftEdition...");
        props.navigation.navigate('edit_draft', {
          activityDraft: rawDraft,
          draftId: item._uniqueId // Pass the uniqueId to the edit screen
        });
      } else {
        console.log("Draft not yet loaded");
      }
    };
  
    const handleDelete = () => {
      deleteDraft(fullDraft.instantInfo, item._uniqueId);
    };
  
    return (
      <View style={{ marginTop: 15 }}>
        <StaticInstantItemDraft
          instant={fullDraft.instantInfo}
          displayDetails={handlePress}
          deleteDraft={handleDelete}
        />
      </View>
    );
  };

  const renderActivityItem = (item: InstantInformation) => {
    return (
      <View style={{ marginTop: 15 }}>
        <StaticInstantItem
          instant={item}
          displayDetails={() =>
            props.navigation.navigate('edit_instant', {
              instantId: item.id,
              businessId: props.businessId,
            })
          }
          deleteInstant={deleteInstant}
        />
      </View>
    );
  };

  const renderPartnershipItem = (item: InstantInformation) => {
    return (
      <View style={{ marginTop: 15 }}>
        <StaticInstantItem
          instant={item}
          displayDetails={() => {
            props.navigation.navigate('instant_details', {
              instantId: item.id,
            });
          }}
          deleteInstant={deleteInstant}
        />
      </View>
    );
  };

  const addInstant = (isAdult: boolean) => {
    console.log("Add instants");
    console.log("Queried business: ", business);
    
    if (business && business?.active) {
      const complete = util.isProfileComplete(business);
      console.log("Complete: " ,complete);
      if (complete[1]) {
        console.log("isAdult: ", isAdult);
        if (isAdult) {
          if (props.business.active) {
            props.navigation.navigate('create_instant');
          } else {
            ApplicationToast.warn(I18n.getTranslation().business.business_desactivated);
          }
        } else {
          Alert.alert(
            t('Réservé aux majeurs', 'Reserved for adults'),
            t(
              'La création d\'activité est réservée aux personnes majeures.',
              'The creation of activities is reserved for adults.',
            )
          );
        }
      } else {
        console.log('profile incomplete, missing ', (complete[0] as string[]).join(', '));
        //util.alert(t('Veuillez completer votre profil PRO avec les infos suivantes afin de pouvoir créer des activités : ' +(complete[0] as string[]).join(', ')+'.', 'Please complete your PRO profile with the following to create activities: '+(complete[0] as string[]).join(', ')+'.'));

        Alert.alert(
          t('Profil Pro Incomplet', 'Incomplete Business Profile'),
          t(
            'Veuillez completer votre profil PRO avec les infos suivantes afin de pouvoir créer des activités : ' +(complete[0] as string[]).join(', ')+'.',
            'Please complete your PRO profile with the following to create activities: '+(complete[0] as string[]).join(', ')+'.'
          ),
          [
            {
              text: t('Completer mon profil', 'Update my profile'),
              onPress: () => props.navigation.navigate('business_edition'),
              style: 'default', // Styles the button as a cancel button (iOS only)
            },
            {
              text: t('Plus tard', 'Later'),
              onPress: () => console.log('Later Pressed'),
              style: 'default', // Styles the button as a default button (iOS only)
            },
          ],
        );
      }
    }
  };

  // Function to delete a draft from the array by uniqueId
  const deleteDraftFromArray = async (key, uniqueId) => {
    console.log(`Deleting draft with uniqueId ${uniqueId} from ${key}`);
    try {
      // Get the current array
      let currentArray = await storage.get(key);
      
      // If it's a string, parse it
      if (typeof currentArray === 'string') {
        console.log('Parsing string');
        currentArray = JSON.parse(currentArray);
      }
      console.log('Current array:', currentArray);

      // Filter out the object with the matching _uniqueId
      const updatedDrafts = currentArray.filter(item => item._uniqueId !== uniqueId);

      console.log('Updated array:', updatedDrafts);
      
      // Store the updated array back in AsyncStorage
      await storage.set(key, updatedDrafts);
      
      console.log('Draft deleted successfully');
      return updatedDrafts;
    } catch (e) {
      console.error('Error deleting draft:', e);
      throw e; // Throwing the error allows the caller to handle it
    }
  };

  const deleteDraft = (instantInformation, uniqueId) => {
    Alert.alert(
      '',
      I18n.getTranslation().instant_submission.delete_instant(instantInformation.title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: async () => {
            const user = await storage.get(Constants.USER_INFO_KEY);
            console.log("user uuid: ", user?.uuid);
            const updatedDrafts = await deleteDraftFromArray(user?.uuid, uniqueId);
            if (updatedDrafts) {
              await getDrafts();
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  const deleteInstant = (instantId: string, title: string) => {
    Alert.alert(
      '',
      I18n.getTranslation().instant_submission.delete_instant(title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: () => props.deleteInstant(instantId, props.businessId),
        },
      ],
      { cancelable: false }
    );
  };

  return (
    <View style={styles.container}>
      {spinner}
      {business?.active && (business?.subscription?.isSubscriptionActive || !isTrialEnded) && (<TouchableOpacity
        style={styles.viewButton}
        onPress={() => addInstant(isAdult)}
      >
        <IconFA name={'ios-add-circle'} color={Theme.sunglow} size={20} />
        <Text style={styles.textButton}>
          {I18n.getTranslation().business.add_instants}
        </Text>
      </TouchableOpacity>)}
      {!business?.active && (
        <View>
          <Text style={styles.bannerText}>
            {I18n.getTranslation().business.business_desactivated}
          </Text>
        </View>
      )}
      {!business?.active && !business?.subscription?.isSubscriptionActive && isTrialEnded && (
        <DefaultButton
          label={t('Souscrire', 'Subscribe')}
          onPress={() => props.navigation.navigate('subscription')}
          style={styles.button}
        />
      )}
      {renderBody()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  viewButton: {
    borderColor: Theme.logan,
    backgroundColor: 'white',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderRadius: 5,
    height: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    width: '90%',
    zIndex: 3,
    marginVertical: 20,
  },
  textButton: {
    paddingLeft: 10,
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  bannerText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    paddingHorizontal: 15,
    paddingTop: 45,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
  },
  button: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    marginBottom: 10,
  },
  activitiesListTitle: {
    color: '#666',
    fontFamily: 'U8 Bold',
    fontSize: 18,
    lineHeight: 18,
    paddingHorizontal: 15,
    paddingTop: 45,
    textAlign: 'center',
  },
});
