import React, {PureComponent, useCallback, useEffect, useMemo, useState} from 'react';
import {
  Alert,
  Dimensions,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  Platform,
  PermissionsAndroid,
  Linking,
  ActivityIndicator,
} from 'react-native';
import {EventRegister} from 'react-native-event-listeners';
import IconFA from 'react-native-vector-icons/Ionicons';
import {Business} from '../../../../businesscontext/domain/entities/business';
import {ApplicationToast} from '../../../../common/adapters/primaries/components/application.toast';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';

import {I18n} from '../../../../configuration/i18n/i18n';
import {Theme} from '../../../../configuration/theme/app.theme';
import {InstantInformation} from '../../../domain/entities/instantInformation';
import {
  StaticInstantItem3,
  StaticInstantItem4,
} from '../listing_common/staticInstantItem';
import {http, storage, t, util} from 'src/_helpers';
import {HeaderButtonIcon} from 'src/_components';
import {useLocalUser} from 'src/_hooks';
import {firebase} from '@react-native-firebase/database';
import {Bubble, GiftedChat, IMessage, Send} from 'react-native-gifted-chat';
import DocumentPicker from 'react-native-document-picker';
import ImagePicker, { launchImageLibrary } from 'react-native-image-picker';
import {useRoute} from '@react-navigation/native';
import FlexibleImage from 'src/_components/shared/FlexibleImage';
import Icon from 'react-native-vector-icons/FontAwesome';
import fStorage from '@react-native-firebase/storage';
import RNFS from 'react-native-fs';

import InChatFileTransfer from 'src/_components/shared/InChatFileTransfer';
import InChatViewFile from 'src/_components/shared/InChatViewFile';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FlashMessage, { showMessage } from 'react-native-flash-message';


const detailContainerWidth = Dimensions.get('window').width - 94;
const titleContainerWidth = Dimensions.get('window').width - 94 - 26;

interface Props {
  navigation: any;
  loadInstantsByBusiness: (businessId: string) => void;
  reloadInstantsByBusiness: (businessId: string) => void;
  deleteInstant: (instantId: string, businessId: string) => void;
  loading: boolean;
  errorLoading: string;
  successLoading: boolean;
  instants: InstantInformation[];
  businessId: string;
  business: Business;
  support?: boolean;
  loadRemoteBusiness: (businessId: string) => void;
}

interface State {
  infoInstant: InstantInformation[];
  infoInstant2: InstantInformation[];
  filter: any;
  loading: boolean;
  filtered: boolean;
  request: any;
  step: number;

  link: string;
}

export class BusinessRequestsListContainer extends PureComponent<Props, State> {
  static listener: any = null;

  constructor(props) {
    console.log('BusinessRequestsListContainer props: ', props);
    super(props);
    this.state = {
      infoInstant: [],
      infoInstant2: [],
      filter: null,
      loading: false,
      filtered: false,
      request: undefined,
      step: 1,
      link: '',
    };
    this.back = this.back.bind(this);
  }

  componentDidMount(): void {
    this.props.navigation.setOptions({
      headerLeft: () => (
        <HeaderButtonIcon
          paddingLeft={0}
          CTA={this.back}
          iconName={'angle-left'}
          color={'black'}
        />
      ),
    });
  }
  async closeRequest() {
    this.setState({loading: true});
    const request = this.state.request;
    const res = await http.patch(`/chat/support/requests/${request.id}`, {
      closed: 'true',
    });
    if (res.data) {
      this.setState({request: {...request, closed: true}, loading: false});
      return;
    }
    this.setState({loading: false});
  }
  back() {
    this.props.navigation.setOptions({
      title: this.props.support
        ? t('Support Client', 'Customer Service')
        : I18n.getTranslation().business.my_requests,
    });
    if (this.state.step > 1) {
      this.setState({step: this.state.step - 1});
      return;
    } else if (this.state.request) {
      this.setState({request: undefined});
      return;
    }
    this.props.navigation.goBack();
  }

  onRequestDetails(item: any, link = '') {
    if (!link) {
      this.setState({step: 2, request: item}, () => {
        this.props.navigation.setOptions({
          title: util.truncate(item.titre, 50),
        });
      });
    } else {
      this.setState({step: 3, link});
    }
  }

  static getDerivedStateFromProps(props: Props, state: State) {
    if (props.instants && state.infoInstant !== props.instants)
      return {...state, infoInstant: props.instants};
    if (props.errorLoading)
      ApplicationToast.error(
        I18n.getTranslation().instant_submission.server_error,
      );
    return null;
  }

  render() {
    const spinner = this.state.loading ? (
      <Spinner background={'rgba(0,0,0, 0.2)'} />
    ) : null;
    return (
      <View style={styles.container}>
        {spinner}
        {this.state.step === 1 && (
          <>
            {!this.props.support && (
              <TouchableOpacity
                style={styles.viewButton}
                onPress={() => this.addRequest()}>
                <IconFA
                  name={'ios-add-circle'}
                  color={Theme.sunglow}
                  size={20}
                />
                <Text style={styles.textButton}>
                  {I18n.getTranslation().business.add_requests}
                </Text>
              </TouchableOpacity>
            )}
            <ItemList
              {...this.props}
              {...this.state}
              onRequestDetails={t => this.onRequestDetails(t)}
            />
          </>
        )}
        {this.state.step === 2 && (
          <>
            {Number(this.state.request.closed) < 1 && false && (
              <TouchableOpacity
                style={styles.viewButton}
                onPress={() => this.addResponse()}>
                <IconFA
                  name={'ios-add-circle'}
                  color={Theme.sunglow}
                  size={20}
                />
                <Text style={styles.textButton}>
                  {I18n.getTranslation().business.add_response}
                </Text>
              </TouchableOpacity>
            )}
            {this.state.request && (
              <RequestFiles
                {...this.state}
                {...this.props}
                onRequestDetails={t => this.onRequestDetails({}, t)}
                closeRequest={() => this.closeRequest()}
              />
            )}
            <ItemList3 {...this.props} {...this.state} />
            {/*<ItemList2
              {...this.props}
              {...this.state}
              onRequestDetails={t => this.onRequestDetails({}, t)}
            />*/}
          </>
        )}
        {this.state.step === 3 && this.state.link && (
          <FlexibleImage
            style={{width: '100%', height: 512}}
            source={{uri: this.state.link}}
          />
        )}
      </View>
    );
  }

  addRequest = () => {
    this.props.navigation.navigate('create_request');
  };

  addResponse = () => {
    this.props.navigation.navigate('create_request', {
      request: this.state.request,
    });
  };

  deleteInstant = (instantId: string, title: string) => {
    Alert.alert(
      '',
      I18n.getTranslation().instant_submission.delete_instant(title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: () =>
            this.props.deleteInstant(instantId, this.props.businessId),
        },
      ],
      {cancelable: false},
    );
  };
}

const ResponsesFiles = (props: any) => {
  const [items, setItems] = useState<any[]>([]);

  useEffect(() => {
    const url = `/chat/support/responses/${props.item.id}/attachments?admin=false`;
    http.get(url).then(res => {
      setItems(res.data || []);
    });
  }, [props.item]);
  return (
    <>
      {items.map((a, i) => {
        return (
          <View key={i}>
            <TouchableOpacity
              onPress={() => {
                props.onRequestDetails(a.link);
              }}>
              <Text style={{color: 'skyblue'}}>{t("Pièce jointe", "Attachment")} {i + 1}</Text>
            </TouchableOpacity>
          </View>
        );
      })}
    </>
  );
};

const RequestFiles = (props: any) => {
  const [items, setItems] = useState<any[]>([]);
  const [userName, setUserName] = useState<string>('');
  console.log("props.request: ", props.request);

  useEffect(() => {
    const url = `/chat/support/requests/${props.request.id}/attachments?admin=false`;
    const nameUrl = `/users/info/${props.request.creator_id}`;
    http.get(nameUrl).then(res => {
      setUserName(res.data.displayName || '');
    });
    http.get(url).then(res => {
      setItems(res.data || []);
    });
  }, [props.request]);

  return (
    <View
      style={[
        {
          backgroundColor: '#fff',
        },
      ]}>
      <View style={[{width: Dimensions.get('window').width - 44, padding: 20}]}>
        <View style={styles.itemInformation}>
          <Text
            style={[
              {
                width: Dimensions.get('window').width - 44,
                fontSize: 16,
                fontFamily: 'U8 Bold',
                marginBottom: 10,
              },
            ]}>
            {props.request.titre}
          </Text>
        </View>
        <View>
          <Text style={[styles.description, {paddingBottom: 10}]}>
            {props.request.description}
          </Text>
        </View>

        {items.map((a, i) => {
          return (
            <View key={i}>
              <TouchableOpacity
                onPress={() => {
                  props.onRequestDetails(a.link);
                }}>
                <Text style={{color: 'skyblue'}}>{t("Pièce jointe", "Attachment")} {i + 1}</Text>
              </TouchableOpacity>
            </View>
          );
        })}
        {Number(props.request.closed) < 1 && (
          <TouchableOpacity onPress={() => props.closeRequest()}>
            <Text style={{color: 'red', fontFamily: 'U8 Bold'}}>
              {I18n.getTranslation().instant_submission.closed_this_requete}
            </Text>
          </TouchableOpacity>
        )}
        {Number(props.request.closed) > 0 && (
          <Text style={{color: 'red', fontFamily: 'U8 Bold'}}>
            {I18n.getTranslation().instant_submission.closed_requete}
          </Text>
        )}
        {props.support && (
          <Text
            style={{
              color: 'black',
              fontFamily: 'U8 Bold',
              fontSize: 9,
              marginTop: 10,
            }}>
            {t('Ouvert par ', 'Opened by ') + userName + ' (' + props.request.user + ')'}
          </Text>
        )}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  viewButton: {
    borderColor: Theme.logan,
    backgroundColor: 'white',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderRadius: 5,
    height: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    width: '90%',
    zIndex: 3,
    marginVertical: 20,
  },
  textButton: {
    paddingLeft: 10,
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  bannerText: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    paddingHorizontal: 15,
    paddingTop: 45,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
  },

  item: {
    borderBottomWidth: 1,
    borderBottomColor: 'lightgrey',
    backgroundColor: 'white',
    padding: 10,
    borderLeftWidth: 4,
    marginBottom: 5,
  },
  itemHeader: {
    flexDirection: 'row',
    height: 62,
  },
  imageContainer: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  image: {
    backgroundColor: '#CCC',
    height: 62,
    width: 62,
    borderColor: '#FFF',
    overflow: 'hidden',
  },
  detailContainer: {
    flexDirection: 'column',
    marginLeft: 8,
    width: detailContainerWidth,
  },
  itemInformation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontFamily: 'U8 Bold',
    width: titleContainerWidth,
  },
  description: {
    fontSize: 12,
    fontFamily: 'U8 Regular',
    color: '#666',
    flexWrap: 'wrap',
  },
  icon: {
    alignSelf: 'flex-start',
  },
  deleteButton: {
    backgroundColor: Theme.flamingo,
    width: 30,
    height: 30,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    alignSelf: 'flex-end',
    marginTop: -25,
    marginBottom: 10,
    zIndex: 99999,
  },
});

const ItemList = (props: any) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const location = useRoute();
  const deleteInstant = (instantId: string, title: string) => {
    Alert.alert(
      '',
      I18n.getTranslation().instant_submission.delete_instant(title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: () => {},
        },
      ],
      {cancelable: false},
    );
  };

  const renderItem = (item: any) => {
    return (
      <View style={{marginTop: 15}}>
        <StaticInstantItem3
          instant={item}
          displayDetails={() => props.onRequestDetails(item)}
          deleteInstant={deleteInstant}
        />
      </View>
    );
  };

  const loadData = () => {
    const url = props.response
      ? `/chat/support/requests/${props.request.id}/responses?admin=${
          props.support ? 'true' : 'false'
        }`
      : `/chat/support/requests?admin=${props.support ? 'true' : 'false'}`;
    http.get(url).then(res => {
      if ((location.params as any)?.initialMessage) {
        const message = (location.params as any)?.initialMessage;
        props.navigation.setParams({...location.params, initialMessage: null});
        if (message.data?.requests_id) {
          const i = res.data.findIndex(
            d => String(d.id) === String(message.data?.requests_id),
          );
          if (i > -1) {
            props.onRequestDetails(res.data[i]);
            return;
          }
        }
      }
      setLoading(false);
      setData(res.data || []);
    });
  };
  useEffect(() => {
    setLoading(true);
    if (props.response && !props.request) return;
    const listener = EventRegister.addEventListener(
      'refresh_request',
      async data => loadData(),
    );

    loadData();
    return () => {
      EventRegister.removeEventListener(listener as string);
    };
  }, [props.navigation.state?.params]);
  if (loading) {
    return <Spinner background={null} />;
  }
  if (!data.length) {
    return (
      <Text style={styles.bannerText}>
        {I18n.getTranslation().instant_submission.no_requests_found}
      </Text>
    );
  }
  return (
    <FlatList
      data={data}
      renderItem={data => renderItem(data.item)}
      keyExtractor={(item, index) => index.toString()}
    />
  );
};

const ItemList2 = (props: any) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const renderItem = (item: any) => {
    return (
      <View style={{marginTop: 15}}>
        <StaticInstantItem4
          instant={item}
          displayDetails={() => {}}
          deleteInstant={() => {}}
          getAttachment={() => (
            <ResponsesFiles
              item={item}
              onRequestDetails={t => props.onRequestDetails(t)}
            />
          )}
        />
      </View>
    );
  };

  useEffect(() => {
    setLoading(false);
    const url = `/chat/support/requests/${props.request.id}/responses?admin=${
      props.support ? 'true' : 'false'
    }`;
    http.get(url).then(res => {
      setLoading(false);
      setData(res.data || []);
    });
  }, [props.request, props.navigation.state?.params]);
  if (loading) return <Spinner background={null} />;

  if (!data.length) {
    return (
      <Text style={styles.bannerText}>
        {I18n.getTranslation().instant_submission.no_response_found}
      </Text>
    );
  }
  return (
    <FlatList
      data={data}
      renderItem={data => renderItem(data.item)}
      keyExtractor={(item, index) => index.toString()}
    />
  );
};

const CustomMessage = (props) => {
  //console.log('CustomMessage props:', props);
  const { currentMessage } = props;

  // Render image attachments based on your structure
  const renderAttachment = () => {
    if (currentMessage.attachments && currentMessage.attachments.length > 0) {
      return currentMessage.attachments.map((attachment, index) => {
        //console.log('Message: ', currentMessage);
        //console.log(`${currentMessage.attachments.length} Attachments`);
        //console.log('Attachment:', attachment.link);
        return (
          <Image
          key={attachment.id}
          source={{ uri: attachment.link }}
          style={{ 
            width: 200, 
            height: 200, 
            borderRadius: 10, 
            marginTop: 10,
            marginLeft: 10
          }}
          resizeMode="cover"
        />
      )});
    }
    return null;
  };

  return (
    <View>
      <Bubble
        {...props}
        renderMessageImage={() => null} // Prevent default image rendering
        wrapperStyle={{
          right: {
            backgroundColor: 'teal',
            borderBottomRightRadius: 0,
            borderBottomLeftRadius: 15,
            borderTopRightRadius: 15,
            borderTopLeftRadius: 15,
            marginBottom: 10,
            marginRight: 10

          },
          left: {
            backgroundColor: 'white',
            borderBottomRightRadius: 15,
            borderBottomLeftRadius: 15,
            borderTopRightRadius: 15,
            borderTopLeftRadius: 0,
            marginLeft: 10,
            marginBottom: 10
          }
        }}
      />
      {renderAttachment()}
    </View>
  );
}

export const ItemList3 = (props: any) => {
  const [user] = useLocalUser();
  const [attachments, setAttachments] = useState<any[]>([]);

  const [isAttachImage, setIsAttachImage] = useState(false);
  const [isAttachFile, setIsAttachFile] = useState(false);
  const [fileVisible, setFileVisible] = useState(false);
  const [imagePath, setImagePath] = useState('');
  const [filePath, setFilePath] = useState('');
  const [messages, setMessages] = useState<IMessage[]>([]);

  const [state, setState] = useState<{
    data: any[];
    loading: boolean;
    messages: IMessage[];
  }>({
    data: [],
    loading: true,
    messages: [],
  });

  const postMessage = (m) => {
    http.post(`/chat/support/responses`, {
      message: m.text,
      requests_id: props.request.id,
      user_timestamp: m.createdAt,
      message_uuid: m._id,
      user_name: m.user.name,
      user_avatar: m.user.avatar,
    }).then(async res => {
      console.log('Response:', res);
      
      if (isAttachImage) {
        await uploadFile(m.image, res.data);
      } else if (isAttachFile) {
        await uploadFile(m.file.url, res.data);
      }
    })
  }

  const onSend = useCallback(async (messages = []) => {
      const [messageToSend] = messages;
      console.log("Message to send: ", messageToSend);
      console.log("Messages: ", messages);
      if (isAttachImage) {
        console.log('Image: ', imagePath);
        const newMessage = {
          _id: messages[0]._id + 1,
          text: messageToSend.text,
          createdAt: new Date(),
          user: {
            _id: user?.uuid,
            name: user?.displayName,
            avatar:
              user?.avatar ??
              'https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name=' +
                user?.displayName,
          },
          image: imagePath,
          pending: true,
          file: {
            url: ''
          }
        };
        setMessages(previousMessages =>
          GiftedChat.append(previousMessages, newMessage),
        );
        await postMessage(newMessage);
        console.log('Message posted');
        setImagePath('');
        setIsAttachImage(false);
      } else if (isAttachFile) {
        const newMessage = {
          _id: messages[0]._id + 1,
          text: messageToSend.text,
          createdAt: new Date(),
          user: {
            _id: 1,
            avatar: '',
          },
          image: '',
          pending: true,
          file: {
            url: filePath
          }
        };
        // setMessages(previousMessages =>
        //   GiftedChat.append(previousMessages, newMessage),
        // );
        await postMessage(newMessage);
        console.log('Message posted');
        setFilePath('');
        setIsAttachFile(false);
      } else {
        // setMessages(previousMessages =>
        //   GiftedChat.append(previousMessages, messageToSend),
        // );
        await postMessage(messageToSend);
        console.log('Message posted');
      }
    },
    [filePath, imagePath, isAttachFile, isAttachImage],
  );
  
/*
  // Attachment handling functions
  const pickDocument = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: true,
      });
      
      const newAttachments = res.map(file => ({
        name: file.name,
        type: file.type,
        uri: file.uri,
        size: file.size,
      }));
      
      setAttachments(prev => [...prev, ...newAttachments]);
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker
      } else {
        console.error('Document picking error:', err);
      }
    }
  };

  const pickImage = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        selectionLimit: 0, // Allow multiple selections
        quality: 1,
      });

      if (result.didCancel) {
        return;
      } 

      const selectedImages = result.assets || [];
      const newAttachments = selectedImages.map(image => ({
        name: image.fileName,
        type: image.type,
        uri: image.uri,
        size: image.fileSize,
      }));

      setAttachments(prev => [...prev, ...newAttachments]);
    } catch (err) {
      console.error('Image picking error:', err);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };
*/

  // add a function attach file using DocumentPicker.pick
  const _pickDocument = async () => {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.images],
        copyTo: 'documentDirectory',
        mode: 'import',
      });
      const fileUri = result[0].fileCopyUri;
      console.log(fileUri);
      if (!fileUri) {
        console.log('File URI is undefined or null');
        return;
      }
      if (fileUri.indexOf('.png') !== -1 || fileUri.indexOf('.jpg') !== -1) {
        setImagePath(fileUri);
        setIsAttachImage(true);
      } else {
        setFilePath(fileUri);
        setIsAttachFile(true);
      }
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        console.log('User cancelled file picker');
      } else {
        console.log('DocumentPicker err => ', err);
        throw err;
      }
    }
  };

  const downloadImage = async (imageUrl) => {
    // Show the flash message when the screen loads
    showMessage({
      message: t("Téléchargement en cours...", "Downloading..."),
      description: t('L\'image est en cours de téléchargement.', 'Image is being downloaded.'),
      type: 'info',
      icon: 'info',
      duration: 1500, // Message duration in milliseconds
    });
  try {
    // Request permissions for Android
    if (Platform.OS === 'android') {
      try {
        // Check Android version
        const androidVersion = Platform.Version;

        // For Android 13 and above (API level 33+)
        if (androidVersion >= 33) {
          const permissions = [
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
          ];

          const results = await PermissionsAndroid.requestMultiple(permissions);

          // Check if any permission was denied
          const denied = Object.values(results).some(
            result => result === PermissionsAndroid.RESULTS.DENIED
          );

          // Check if any permission is set to never ask again
          const neverAskAgain = Object.values(results).some(
            result => result === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN
          );

          if (neverAskAgain) {
            Alert.alert(
              t("Permission requise", "Permission Required"),
              t("L'accès aux images est nécessaire pour télécharger des images. Veuillez l'activer dans les paramètres de votre appareil.", 
                "Image access permission is required to download images. Please enable it in your device settings."),
              [
                { text: t("Annuler", "Cancel"), style: "cancel" },
                { 
                  text: t("Ouvrir les paramètres", "Open Settings"), 
                  onPress: () => Linking.openSettings()
                }
              ]
            );
            return;
          }

          if (denied) {
            Alert.alert(
              t("Permission refusée", "Permission Denied"),
              t("Impossible de télécharger des images sans les autorisations requises. Vous pourrez accorder la permission la prochaine fois.", 
                "Cannot download images without required permissions. You can grant permission next time.")
            );
            return;
          }
        } 
        // For Android 12 and below
        else {
          const permission = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;
          const hasPermission = await PermissionsAndroid.check(permission);
          
          if (!hasPermission) {
            const granted = await PermissionsAndroid.request(
              permission,
              {
                title: t("Autorisation de stockage", "Storage Permission"),
                message: t("L'application a besoin d'accéder à votre stockage pour télécharger des images.", 
                          "App needs access to your storage to download images."),
                buttonNeutral: t("Demander plus tard", "Ask Me Later"),
                buttonNegative: t("Annuler", "Cancel"),
                buttonPositive: t("OK", "OK")
              }
            );

            if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
              Alert.alert(
                t("Permission requise", "Permission Required"),
                t("L'accès au stockage est nécessaire pour télécharger des images. Veuillez l'activer dans les paramètres.", 
                  "Storage access is required to download images. Please enable it in your device settings."),
                [
                  { text: t("Annuler", "Cancel"), style: "cancel" },
                  { 
                    text: t("Ouvrir les paramètres", "Open Settings"), 
                    onPress: () => Linking.openSettings()
                  }
                ]
              );
              return;
            }

            if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
              Alert.alert(
                t("Permission refusée", "Permission Denied"),
                t("Impossible de télécharger des images sans autorisation de stockage.", 
                  "Cannot download images without storage permission.")
              );
              return;
            }
          }
        }
      } catch (error) {
        console.error('Permission request error:', error);
        Alert.alert(
          t("Erreur", "Error"),
          t("Une erreur s'est produite lors de la demande d'autorisation.", 
            "Something went wrong while requesting permission.")
        );
        return;
      }
    }

    // Generate a unique filename
    const filename = `image_${Date.now()}.jpg`;
    
    // Determine the download directory based on platform
    const downloadDest = Platform.select({
      ios: `${RNFS.DocumentDirectoryPath}/${filename}`,
      android: `${RNFS.DownloadDirectoryPath}/${filename}` // Changed to Downloads folder
    });

    // Download the file
    const downloadResult = await RNFS.downloadFile({
      fromUrl: imageUrl,
      toFile: downloadDest,
    }).promise;

    console.log("Download Result: ", downloadResult);

    // Show success alert
    /*
    Alert.alert(
      t("Téléchargement réussi", "Download Successful"),
      Platform.OS === 'ios' 
        ? t("L'image a été enregistrée dans vos Photos", "Image has been saved to your Photos")
        : t("L'image a été enregistrée dans votre dossier Téléchargements", "Image has been saved to your Downloads folder")
    );
    */

    showMessage({
      message: t("Téléchargement réussi", "Download Successful"),
      description:
        Platform.OS === 'ios'
          ? t("L'image a été enregistrée dans vos Photos", "Image has been saved to your Photos")
          : t("L'image a été enregistrée dans votre dossier Téléchargements", "Image has been saved to your Downloads folder"),
      type: 'success', // 'info', 'danger', 'warning', or 'success'
      icon: 'success', // Show a success icon
      duration: 3000, // Duration in milliseconds
    });
    

  } catch (error) {
    console.error('Download Error:', error);
    Alert.alert(
      t("Échec du téléchargement", "Download Failed"),
      t("Impossible de télécharger l'image. Veuillez réessayer.", 
        "Unable to download the image. Please try again.")
    );
  }
};

const renderBubble = (props) => {
  const { currentMessage } = props;

  if (currentMessage.file && currentMessage.file.url) {
    return (
      <TouchableOpacity
        style={{
          ...chatstyles.fileContainer,
          backgroundColor: currentMessage.user._id === 2 ? '#2e64e5' : '#efefef',
          borderBottomLeftRadius: currentMessage.user._id === 2 ? 15 : 5,
          borderBottomRightRadius: currentMessage.user._id === 2 ? 5 : 15,
          padding: 10,
        }}
        onPress={() => setFileVisible(true)} // Preview on normal press
      >
        <InChatFileTransfer
          style={{ marginBottom: 10 }}
          filePath={currentMessage.file.url}
        />
        <InChatViewFile
          props={props}
          visible={fileVisible}
          onClose={() => setFileVisible(false)}
        />
        <Text
          style={{
            ...chatstyles.fileText,
            color: currentMessage.user._id === 2 ? 'white' : 'black',
          }}
        >
          {currentMessage.text}
        </Text>
      </TouchableOpacity>
    );
  }

  if (!currentMessage.image || typeof currentMessage.image !== 'string') {
    console.log('Missing or Invalid image URL:', currentMessage.image);
    return (
      <Bubble
        {...props}
        wrapperStyle={{
          right: {
            backgroundColor: '#2e64e5',
            marginBottom: 10,
            opacity: currentMessage.pending ? 0.7 : 1,
          },
        }}
        textStyle={{
          right: {
            color: '#efefef',
          },
        }}
      />
    );
  } else {
    return (
      <Bubble
        {...props}
        onLongPress={() => downloadImage(currentMessage.image)}
        wrapperStyle={{
          right: {
            backgroundColor: '#2e64e5',
            marginBottom: 10,
          },
        }}
        textStyle={{
          right: {
            color: '#efefef',
          },
        }}
      />
    );
  }

  
};

  const renderMessageImage = (props) => {
    return (
      <Image
        source={{ uri: props.currentMessage.image }}
        style={{
          maxWidth: '100%',
          maxHeight: 200,
          resizeMode: 'contain',
        }}
      />
    );
  };

  const renderChatFooter = useCallback(() => {
    if (imagePath) {
      return (
        <View style={chatstyles.chatFooter}>
          <Image source={{uri: imagePath}} style={{height: 75, width: 75}} />
          <TouchableOpacity
            onPress={() => setImagePath('')}
            style={chatstyles.buttonFooterChatImg}
          >
            <Text style={chatstyles.textFooterChat}>X</Text>
          </TouchableOpacity>
        </View>
      );
    }
    if (filePath) {
      return (
        <View style={chatstyles.chatFooter}>
          <InChatFileTransfer
            filePath={filePath}
          />
          <TouchableOpacity
            onPress={() => setFilePath('')}
            style={chatstyles.buttonFooterChat}
          >
            <Text style={chatstyles.textFooterChat}>X</Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  }, [filePath, imagePath]);

  const scrollToBottomComponent = () => {
    return <FontAwesome name="angle-double-down" size={22} color="#333" />;
  };


  const renderSend = (props) => {
    return (
      <View style={{flexDirection: 'row'}}>
        <TouchableOpacity onPress={_pickDocument}>
          <Icon
            type="font-awesome"
            name="paperclip"
            style={chatstyles.paperClip}
            size={28}
            color='blue'
          />
        </TouchableOpacity>
        <Send {...props}>
          <View style={chatstyles.sendContainer}>
            <Icon
              type="font-awesome"
              name="send"
              style={chatstyles.sendButton}
              size={25}
              color='orange'
            />
          </View>
        </Send>
      </View>
    );
  };


  // Separate function to fetch attachments for a single item
  const fetchAttachments = async (item) => {
    try {
      const attachmentUrl = `/chat/support/responses/${item.id}/attachments?admin=${props.support ? 'true' : 'false'}`;
      const response = await http.get(attachmentUrl);
      //console.log('Attachments for item', item.request_id, ':', response);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching attachments for item', item.id, ':', error);
      return [];
    }
  };

  // Function to transform a single item with its attachments
  const transformItem = async (item) => {
    console.log("Item: ", item);
    const attachments = await fetchAttachments(item);
    const hasAttachment = attachments.length > 0;
    console.log(hasAttachment ? 'attachments: ' : null);
    console.log(hasAttachment ? attachments : null);

    const isImageFile = (url: string): boolean => {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
      return imageExtensions.some(ext => url.toLowerCase().includes(ext));
    };
    
    const isPdfFile = (url: string): boolean => {
      return url.toLowerCase().includes('.pdf');
    };

    return {
      _id: item.message_uuid,
      text: item.message,
      createdAt: new Date(item.user_timestamp),
      user: {
        _id: item.user_uuid,
        name: item.user_name,
        avatar: util.isUrl(item.user_avatar)
          ? item.user_avatar
          : `https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name=${item.user_name}`,
      },
      image: hasAttachment ? (isImageFile(attachments[0]?.link) ? attachments[0]?.link : null) : null,
      //file: {
      //  url: hasAttachment ? (isPdfFile(attachments[0].link) ? attachments[0].link : null) : null,
      //}
    };
  };

  const userPhoneNumber = useMemo(() => user?.phoneNumber, [user]);
  const userEmail = useMemo(() => user?.email, [user]);

  // Main data fetching effect
  useEffect(() => {
    let isMounted = true;
    let first_load = true;

    const fetchData = async () => {
      if (!user) return;

      try {
        storage.set('lock_requests_id', String(props.request.id));
        
        const url = `/chat/support/requests/${props.request.id}/responses?admin=${
          props.support ? 'true' : 'false'
        }`;

        const response = await http.get(url);
        console.log('Response:', response);

        if (!isMounted) return;

        if (Array.isArray(response?.data)) {
          // Filter valid messages
          const validMessages = response.data.filter(
            f =>
              f.message_uuid &&
              f.user_timestamp &&
              f.user_uuid &&
              f.user_name &&
              f.message
          );

          // Transform all items with their attachments in parallel
          const transformedMessages = await Promise.all(
            validMessages.map(transformItem)
          );
          setMessages(transformedMessages);
          setState({
            loading: false,
            data: response.data,
            messages: transformedMessages,
          });
        } else {
          setState(prev => ({
            ...prev,
            loading: false,
          }));
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        if (isMounted) {
          setState(prev => ({
            ...prev,
            loading: false,
          }));
        }
      }
    };

    fetchData();

    const reference = firebase
      .app()
      .database(
        'https://sportaabe-db345-default-rtdb.europe-west1.firebasedatabase.app',
      )
      .ref(`${'dev'}/requests/UID-${props.request.id}`);

      const onChildChanged = reference.on('value', async snapshot => {
        const val: any = snapshot.toJSON();
        if (!val) return;
      
        const checkAttachment = async (attempts = 0, maxAttempts = 10) => {
          console.log('Attemptimg attachment retrieval: #', attempts, 'Max attempts: ', maxAttempts);
          try {
            const attachmentUrl = `/chat/support/responses/${val.response_id}/attachments?admin=${props.support ? 'true' : 'false'}`;
            const response = await http.get(attachmentUrl);
            const hasAttachment = response.data.length > 0;
      
            if (hasAttachment) {
              // Update the message with the attachment
              const isImageFile = (url: string): boolean => {
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
                return imageExtensions.some(ext => url.toLowerCase().includes(ext));
              };
      
              setMessages(prevState => {
                const messageIndex = prevState.findIndex(msg => String(msg._id) === String(val.id));
                if (messageIndex === -1) return prevState;
      
                const updatedMessages = [...prevState];
                updatedMessages[messageIndex] = {
                  ...updatedMessages[messageIndex],
                  image: isImageFile(response.data[0]?.link) ? response.data[0]?.link : null,
                  pending: false
                };
                return updatedMessages;
              });
              return true;
            } else if (attempts < maxAttempts) {
              // Wait and try again
              await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
              return checkAttachment(attempts + 1, maxAttempts);
            }
            return false;
          } catch (error) {
            console.error('Error checking attachment:', error);
            return false;
          }
        };
      
        // Create message immediately without attachment
        const newMessage = {
          _id: val.id,
          createdAt: new Date(val.date),
          text: val.message,
          user: {
            _id: val.user,
            name: val?.user_name,
            avatar: val?.user_avatar,
          },
          image: null, // Initially no image
        };
      
        // Add message to chat
        setMessages(prevState => {
          if (!prevState.some(message => String(message._id) === String(newMessage._id))) {
            return GiftedChat.append(prevState, [newMessage]);
          }
          return prevState;
        });
      
        // Start checking for attachment
        checkAttachment();
      
        // Handle notifications and first_load
        if (!first_load && user?.uuid != val.user) {
          util.notify(t("Nouveau message", "New message"), val.message);
        }
        first_load = false;
      });

    // Stop listening for updates when no longer required
    return () => {
      // Set mounted to false to prevent state updates after unmount
      isMounted = false;
      storage.remove('lock_requests_id');
      reference.off('value', onChildChanged);
    };
  }, [userPhoneNumber, userEmail]);

  useEffect(() => {
    // Show the flash message when the screen loads
    showMessage({
      message: t("Astuce", "Tip"),
      description: t('Un appui long sur un message permet de télécharger l\'image.', 'Long-press a message to download it\'s image.'),
      type: 'info',
      icon: 'info',
      duration: 4000, // Message duration in milliseconds
    });
  }, [state.loading]);

  const uploadFile = async (file: any, id: number) => {
    console.log('Uploading File ' + file + ' to response ' + id.toString());
    if (!file) return;
    try {
      const url = `/chat/support/responses/${id}/attachments`;

      if (file?.indexOf('file:/') > -1 || Platform.OS==="ios") {
        const reference = fStorage().ref(
          `requestsResponseAttachments/${file.split('/').pop()}`,
        );
        await reference.putFile(file);
        const link = await reference.getDownloadURL();
        const res = await http.post(url, {link});
        console.log('++++++++ ', res);
      }
    } catch (error) {
      console.error(error);
    }
  }

  if (state.loading) return <Spinner background={'rgba(0,0,0,0.3)'} />;

  return (
      <View style={{
        flex: 1,
      }}>
        <GiftedChat
          renderUsernameOnMessage={true}
          //renderMessage={CustomMessage}
          renderBubble={renderBubble}
          //renderMessageImage={renderMessageImage}
          renderCustomView={props => {
            const { pending, image } = props.currentMessage;
            console.log('Pending: ' + pending + ' Image: ' + image);
            if (image && pending) {
              return (
                <View style={{ padding: 5 }}>
                  <ActivityIndicator size="small" color="#e100ff" />
                  <Text style={{ fontSize: 12, fontFamily: 'U8 Regular', color: '#ffffff', textAlign: 'center' }}>
                    {t('Envoi en cours...', 'Uploading...')}
                  </Text>
                </View>
              );
            }
            return null;
          }}
          renderSend={renderSend}
          alwaysShowSend
          scrollToBottom={true}
          scrollToBottomComponent={scrollToBottomComponent}
          renderChatFooter={renderChatFooter}
          placeholder={t('Saisir un message ...', 'Type a message ...')}
          messages={messages}
          onSend={messages => {
            if (props.request.closed) {
              util.alert(t('Cette requête est fermée', 'This request is closed'));
              return;
            }
            onSend(messages);
          }}
          user={{
            _id: user?.uuid,
            name: user?.displayName,
            avatar:
              user?.avatar ??
              'https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name=' +
                user?.displayName,
          }}
        />
        <FlashMessage position="top" />
      </View>
  );
};

const chatstyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  paperClip: {
    marginTop: 8,
    marginHorizontal: 5,
    transform: [{rotateY: '180deg'}],
  },
  sendButton: {marginBottom: 10, marginRight: 10},
  sendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  chatFooter: {
    shadowColor: '#1F2687',
    shadowOpacity: 0.37,
    shadowRadius: 8,
    shadowOffset: {width: 0, height: 8},
    elevation: 8,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    flexDirection: 'row',
    padding: 5,
    backgroundColor: 'blue'
  },
  fileContainer: {
    flex: 1,
    maxWidth: 300,
    marginVertical: 2,
    borderRadius: 15,
  },
  fileText: {
    marginVertical: 5,
    fontSize: 16,
    lineHeight: 20,
    marginLeft: 10,
    marginRight: 5,
  },
  textTime: {
    fontSize: 10,
    color: 'gray',
    marginLeft: 2,
  },
  buttonFooterChat: {
    width: 35,
    height: 35,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    borderColor: 'black',
    right: 3,
    top: -2,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  buttonFooterChatImg: {
    width: 35,
    height: 35,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    borderColor: 'black',
    left: 66,
    top: -4,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  },
  textFooterChat: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'gray',
  },
});