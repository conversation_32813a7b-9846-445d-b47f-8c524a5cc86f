import React, {PureComponent} from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import {ApplicationIcons} from '../../../../common/adapters/primaries/applicationIcons';
import {ApplicationIconsColor} from '../../../../common/adapters/primaries/applicationIconsColor';
import {I18n} from '../../../../configuration/i18n/i18n';
import {Theme} from '../../../../configuration/theme/app.theme';
import {InstantInformation} from '../../../domain/entities/instantInformation';
import {InstantSubmissionMapper} from '../../secondaries/real/mapper/instantSubmissionMapper';
import {http, t, util} from 'src/_helpers';
import FlexibleImage from 'src/_components/shared/FlexibleImage';
import { InstantHeader } from 'src/v1/instantscontext/domain/entities/instantHeader';
import { InstantMapper } from 'src/v1/instantscontext/adapters/secondaries/real/mappers/instant.mapper';
import { InstantHeaderBuilder } from 'src/v1/instantscontext/usecases/instantslisting/instantHeader.builder';
import { InstantItemContainer } from 'src/v1/instantscontext/adapters/primaries/instantItem/instantItem.container';

interface Props {
  instant: InstantInformation;
  show?: boolean;
  displayDetails: () => void;
  deleteInstant: (instantId: string, title: string) => void;
}

const detailContainerWidth = Dimensions.get('window').width - 94;
const titleContainerWidth = Dimensions.get('window').width - 94 - 36;

export class StaticInstantItem extends PureComponent<Props> {
  render() {
    const show = this.props.show !== false;
    const img = util.isUrl(this.props.instant.picture)
      ? {uri: this.props.instant.picture}
      : require('../../../../../assets/img/defaultBgActivity.png');
    //console.log( this.props.instant);
    
    return (
      <TouchableOpacity
        style={[
          styles.itemCreate,
          {
            borderLeftColor: 'white',
          },
        ]}
        onPress={() => this.props.displayDetails()}>
        {show && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() =>
              this.props.deleteInstant(
                this.props.instant.id,
                this.props.instant.title,
              )
            }>
            <EvilIcons name="trash" size={30} color={'white'} />
          </TouchableOpacity>
        )}

        <View style={styles.itemHeader}>
          <FlexibleImage
            style={styles.imageContainer}
            source={img}
            imageStyle={[styles.imageCreateScreen, {alignSelf: 'center'}]}
          />
          <View style={styles.detailContainer}>
            <View style={styles.itemInformation}>
              <Text style={styles.title} numberOfLines={1}>
                {this.props.instant.title}
              </Text>
              <View style={styles.icon}>
                {ApplicationIcons(36, this.props.instant.category)}
              </View>
            </View>
            <View>
              <Text numberOfLines={2} style={styles.description}>
                {this.props.instant.shortDescription}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

export class StaticInstantItemDraft extends PureComponent<any> {
  render() {
    console.log("Rendering StaticInstantItemDraft with props: ", this.props);
    const show = this.props.show !== false;
    const img = this.props.instant.picture
      ? {uri: this.props.instant.picture}
      : require('../../../../../assets/img/defaultBgActivity.png');
    //console.log("Rendering draft: ", this.props.instant);
    
    return (
      <TouchableOpacity
        style={[
          styles.itemCreate,
          {
            borderLeftColor: 'white',
          },
        ]}
        onPress={() => this.props.displayDetails()}>
        {show && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() =>
              this.props.deleteDraft(
                this.props.instant
              )
            }>
            <EvilIcons name="trash" size={30} color={'white'} />
          </TouchableOpacity>
        )}

        <View style={styles.itemHeader}>
          <FlexibleImage
            style={styles.imageContainer}
            source={img}
            imageStyle={[styles.imageCreateScreen, {alignSelf: 'center'}]}
          />
          <View style={styles.detailContainer}>
            <View style={styles.itemInformation}>
              <Text style={styles.title} numberOfLines={1}>
                {this.props.instant.title}
              </Text>
              <View style={styles.icon}>
                {ApplicationIcons(36, this.props.instant.category)}
              </View>
            </View>
            <View>
              <Text numberOfLines={2} style={styles.description}>
                {this.props.instant.shortDescription}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

export class StaticInstantItem2 extends PureComponent<any> {

  render() {
    console.log("This.instant: ", this.props.instant);

    const getStatusColor = (status) => {
      switch (status.toUpperCase()) {
        case 'INITIATED':
          return '#3498db'; // Light Blue
        case 'PENDING':
          return '#f1c40f'; // Yellow
        case 'FAILED':
          return '#e74c3c'; // Red
        case 'SUCCESSFUL':
          return '#2ecc71'; // Green
        case 'FREE':
          return '#2ecc71'; // Green as well
        default:
          return '#95a5a6'; // Default color (e.g., Grey) for unknown status
      }
    };

    return (
      <View style={{marginTop: 15}}>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() =>
            this.props.deleteInstant(
              this.props.instant.id,
              this.props.instant.title,
            )
          }>
          <EvilIcons name="trash" size={30} color={'white'} />
        </TouchableOpacity>
        <InstantItemContainer
          {...this.props}
          key={this.props.instant?.id}
          instant={this.props.instant}
          displayDetails={() => {
            this.props.navigation.navigate('instant_details', {
              instantId: this.props.instant?.id,
              dateStart: this.props.instant?.dateStart,
              dateEnd: this.props.instant?.dateEnd,
            });
          }}
        />
      </View>
    );
  }
}

/*
export class StaticInstantItem3 extends PureComponent<any> {
  getData() {
    const status = Number(this.props.instant.status);
    const closed = Number(this.props.instant.closed);
    const data: any = {
      message: '',
      color: '',
    };
    if (status === 1) {
      data.color = 'orange';
      data.message = I18n.getTranslation().instant_submission.awaiting_response;
    } else if (status === 3 || closed === 1) {
      data.color = 'gray';
      data.message = I18n.getTranslation().instant_submission.request_closed;
    } else {
      data.color = 'green';
      data.message = I18n.getTranslation().instant_submission.request_open;
    }
    return data;
  }
  render() {
    const data = this.getData();
    console.log("Data: ", data);
    console.log("Props: ", this.props);
    return (
      <TouchableOpacity
        style={[
          styles.item,
          {
            borderLeftColor: data.color,
          },
        ]}
        onPress={() => this.props.displayDetails()}>
        <View
          style={[
            styles.itemHeader,
            {height: this.props.instant?.last_response?.message ? 72 : 62},
          ]}>
          <View style={styles.imageContainer}>
            <Image
              style={styles.image}
              source={{
                uri: `https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name=${this.props.instant.titre}`,
              }}
              borderRadius={10}
            />
          </View>
          <View style={[styles.detailContainer]}>
            <View style={styles.itemInformation}>
              <Text style={[styles.title]} numberOfLines={1}>
                {'('}
                <Text
                  style={[{color: data.color, fontSize: 9}]}
                  numberOfLines={1}>
                  {data.message}
                </Text>
                {') '}
                {this.props.instant.titre}
              </Text>
            </View>
            <View>
              <Text numberOfLines={2} style={styles.description}>
                {this.props.instant.description}
              </Text>
            </View>
            {this.props.instant?.last_response?.message && (
              <View style={{paddingBottom: 10}}>
                <Text
                  numberOfLines={1}
                  style={[
                    styles.description,
                    {
                      color: '#333',
                      fontFamily: 'U8 Bold',
                      marginTop: 5,
                      paddingBottom: 5,
                    },
                  ]}>
                  {t('Dernier message : ', 'Last message : ') +
                    this.props.instant?.last_response?.message}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}
  */

const StatusBadge = ({ status, closed }) => {
  let color, message;
  if (status === 1) {
    color = 'orange';
    message = I18n.getTranslation().instant_submission.awaiting_response;
  } else if (status === 3 || closed === 1) {
    color = 'gray';
    message = I18n.getTranslation().instant_submission.request_closed;
  } else {
    color = 'green';
    message = I18n.getTranslation().instant_submission.request_open;
  }

  return (
    <View style={[styles.statusBadge, { backgroundColor: color }]}>
      <Text style={styles.statusText}>{message}</Text>
    </View>
  );
};

export const StaticInstantItem3 = ({ instant, displayDetails }) => {
  return (
    <TouchableOpacity style={styles.container} onPress={displayDetails}>
      <View style={styles.header}>
        <Image
          style={styles.avatar}
          source={{
            uri: `https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name=${instant.titre}`,
          }}
        />
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {instant.titre}
          </Text>
          <StatusBadge status={instant.status} closed={instant.closed} />
        </View>
      </View>
      <Text style={styles.description} numberOfLines={2}>
        {instant.description}
      </Text>
      {instant?.last_response?.message && (
        <Text style={styles.lastMessage} numberOfLines={1}>
          {t('Dernier message : ', 'Last message : ') +
            instant?.last_response?.message}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export class StaticInstantItem4 extends PureComponent<any> {
  getTitle() {
    const t = Number(this.props.instant.admin);
    if (t > 0) {
      return (
        I18n.getTranslation().instant_submission.admin_msg +
        ' - ' +
        this.props.instant.created_at
      );
    } else {
      return (
        I18n.getTranslation().instant_submission.user_msg +
        ' - ' +
        this.props.instant.created_at
      );
    }
  }
  render() {
    return (
      <TouchableOpacity
        style={[
          styles.item,
          {
            borderLeftColor: 'green',
          },
        ]}
        onPress={() => this.props.displayDetails()}>
        <View style={styles.itemHeader}>
          <View
            style={[
              styles.detailContainer,
              {width: Dimensions.get('window').width - 44},
            ]}>
            <View style={styles.itemInformation}>
              <Text
                style={[
                  styles.title,
                  {width: Dimensions.get('window').width - 44, fontSize: 14},
                ]}
                numberOfLines={1}>
                {this.getTitle()}
              </Text>
            </View>
            <View>
              <Text numberOfLines={2} style={styles.description}>
                {this.props.instant.message}
              </Text>
            </View>
            <View>{this.props.getAttachment()}</View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    borderBottomColor: 'lightgrey',
    borderLeftColor: 'transparent',
    backgroundColor: 'white',
    padding: 10,
    borderLeftWidth: 4,
    marginBottom: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#CCC',
  },
  titleContainer: {
    flex: 1,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontFamily: 'U8 Bold',
    flex: 1,
  },
  statusBadge: {
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
  },
  description: {
    fontSize: 14,
    fontFamily: 'U8 Regular',
    color: '#666',
  },
  lastMessage: {
    fontSize: 13,
    fontFamily: 'U8 Bold',
    color: '#333',
    marginTop: 5,
  },
  item: {
    borderBottomWidth: 1,
    borderBottomColor: 'lightgrey',
    backgroundColor: 'white',
    padding: 10,
    borderLeftWidth: 4,
    marginBottom: 5,
  },
  itemCreate: {
    borderBottomWidth: 1,
    borderBottomColor: 'lightgrey',
    backgroundColor: 'white',
    padding: 10,
    borderLeftWidth: 4,
    marginBottom: 5,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 62,
    backgroundColor: 'white',
  },
  imageContainer: {
    borderRadius: 10,
    overflow: 'hidden',
    alignSelf: 'center',
  },
  image: {
    backgroundColor: '#CCC',
    height: 40,
    width: 40,
    borderColor: '#FFF',
    overflow: 'hidden',
  },
  imageCreateScreen: {
    backgroundColor: '#CCC',
    height: 70,
    width: 70,
    borderColor: '#FFF',
    overflow: 'hidden',
  },
  detailContainer: {
    flexDirection: 'column',
    marginLeft: 8,
    width: detailContainerWidth,
    justifyContent: 'space-around',
  },
  itemInformation: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  icon: {
    alignSelf: 'flex-start',
  },
  deleteButton: {
    position: 'absolute',
    backgroundColor: Theme.flamingo,
    width: 30,
    height: 30,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    alignSelf: 'flex-end',
    marginTop: -25,
    marginBottom: 0,
    zIndex: 99999,
  },
});
