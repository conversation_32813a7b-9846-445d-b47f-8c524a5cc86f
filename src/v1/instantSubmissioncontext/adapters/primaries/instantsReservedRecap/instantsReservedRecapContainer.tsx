import React, { PureComponent, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ie<PERSON>,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { Business } from "../../../../businesscontext/domain/entities/business";
import { ApplicationToast } from "../../../../common/adapters/primaries/components/application.toast";
import { Spinner } from "../../../../common/adapters/primaries/components/spinner.presentational";
import {
  HeaderButtonIcon,
  HeaderTitle,
} from "../../../../common/adapters/primaries/navigation/navigationHeader";
import { I18n } from "../../../../configuration/i18n/i18n";
import { Theme } from "../../../../configuration/theme/app.theme";
import { ApiInstantsLoader } from "../../../../instantscontext/adapters/secondaries/real/apiInstants.loader";
import { InstantInformation } from "../../../domain/entities/instantInformation";

import { StaticInstantItem2 } from "../listing_common/staticInstantItem";

interface Props {
  navigation: any;
  loadInstantsByBusiness: (businessId: string) => void;
  reloadInstantsByBusiness: (businessId: string) => void;
  deleteInstant: (instantId: string, businessId: string) => void;
  loading: boolean;
  errorLoading: string;
  successLoading: boolean;
  instants: InstantInformation[];
  businessId: string;
  business: Business;
  loadRemoteBusiness: (businessId: string) => void;
}

interface State {
  infoInstant: InstantInformation[];
  delLoading: boolean;
}

export class ReservedInstantsRecapContainer extends PureComponent<
  Props,
  State
> {
  constructor(props) {
    super(props);
    this.state = {
      infoInstant: [],
      delLoading: false,
    };
  }

  static navigationOptions = ({ navigation }) => {
    return {
      headerTitle: (
        <HeaderTitle
          title={I18n.getTranslation().business.my_reserved_instants}
        />
      ),
      headerLeft: (
        <HeaderButtonIcon
          CTA={() => navigation.navigate("more")}
          iconName={"angle-left"}
          size={20}
          color={"black"}
        />
      ),
      headerStyle: {
        backgroundColor: "white",
        elevation: 0,
        shadowOpacity: 0,
        height: 40,
      },
    };
  };

  static getDerivedStateFromProps(props: Props, state: State) {
    if (props.instants && state.infoInstant !== props.instants)
      return { ...state, infoInstant: props.instants };
    if (props.errorLoading)
      ApplicationToast.error(
        I18n.getTranslation().instant_submission.server_error
      );
    return null;
  }

  componentDidMount() {
    if (!this.props.business)
      this.props.loadRemoteBusiness(this.props.businessId);
  }

  render() {
    return (
      <ScrollView contentContainerStyle={styles.container}>
        {!this.state.delLoading && (
          <InstantList
            {...this.props}
            loading={this.state.delLoading}
            renderItem={(data) => this.renderItem(data)}
          />
        )}
        {this.state.delLoading && <Spinner background={null} />}
      </ScrollView>
    );
  }

  renderItem(item: any) {
    return (
      <View style={{ marginTop: 15 }}>
        <StaticInstantItem2
          instant={item}
          displayDetails={() =>
            this.props.navigation.navigate("instant_details2", {
              instantId: item.idActivity,
              date: new Date(),
            })
          }
          deleteInstant={(instantId: string, title: string) =>
            this.deleteInstant(instantId, title)
          }
        />
      </View>
    );
  }

  deleteInstant = (instantId: string, title: string) => {
    Alert.alert(
      "",
      I18n.getTranslation().instant_submission.cancel_instant(title),
      [
        {
          text: I18n.getTranslation().instant_submission.cancel,
        },
        {
          text: I18n.getTranslation().instant_submission.delete,
          onPress: () => {
            this.setState({ delLoading: true });
            ApiInstantsLoader.removeReservedInstants(instantId).then((res) => {
              if (!res) {
                ApplicationToast.error("An error occured whiile cancelling !");
              }
              this.setState({ delLoading: false });
            });
          },
        },
      ],
      { cancelable: false }
    );
  };
}

const InstantList = (props: any) => {
  const [loading, setLoading] = useState(true);
  const [instants, setInstants] = useState<any[]>([]);

  useEffect(() => {
    ApiInstantsLoader.loadReservedInstantsByBusiness(
      props.businessId,
      props.profile.email,
      false
    ).then((res) => {
      setInstants(res);
      setLoading(false);
      setTimeout(() => {
        ApplicationToast.show(
          I18n.getTranslation().instant.payment_succeeded,
          "white",
          "#3ca971"
        );
      }, 400);
    });
  }, []);

  if (loading) return <Spinner background={null} />;
  else if (instants.length > 0)
    return (
      <FlatList
        data={instants}
        renderItem={(data) => props.renderItem(data.item)}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  else
    return (
      <Text style={styles.bannerText}>
        {I18n.getTranslation().instant_submission.no_instants_found}
      </Text>
    );
};
const styles = StyleSheet.create({
  container: {
    height: "100%",
  },
  viewButton: {
    borderColor: Theme.logan,
    backgroundColor: "white",
    borderStyle: "dashed",
    borderWidth: 1,
    borderRadius: 5,
    height: 100,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    width: "90%",
    zIndex: 3,
    marginVertical: 20,
  },
  textButton: {
    paddingLeft: 10,
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  bannerText: {
    color: "#666",
    fontSize: 14,
    lineHeight: 18,
    paddingHorizontal: 15,
    paddingTop: 45,
    fontFamily: 'U8 Bold',
    textAlign: "center",
  },
});
