import React, {PureComponent} from 'react';
import {StyleSheet} from 'react-native';
import {EventRegister} from 'react-native-event-listeners';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ApplicationToast} from '../../../../common/adapters/primaries/components/application.toast';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';
import {Coordinates} from '../../../../common/domain/entities/Coordinates';
import {I18n} from '../../../../configuration/i18n/i18n';
import {
  ACTIVITY_APP_PREFERENCE_SAVED,
  CREATION_APP_PREFERENCE_SAVED,
  FOR_YOU_APP_PREFERENCE_SAVED,
  IS_FOR_YOU,
  SHEDULE_FILTER,
} from '../../../../helpers/constant.helper';
import {storage} from '../../../../helpers/storage.helper';
import {InstantPartner} from '../../../domain/entities/instantPartner';
import {SubmissionInstant} from '../../../domain/entities/submissionInstant';
import {PlaningSection} from '../form_common/form/planing/planing.section';
import {InstantSubmissionScheduleType} from '../form_common/formTypes';
import {HeaderButtonIcon, HeaderButtonText} from 'src/_components';

interface Props {
  publishFilter: (
    submissionInstant: SubmissionInstant,
    businessId: string,
  ) => void;
  navigation: any;
  loading: boolean;
  successSubmission: boolean;
  errorSubmission: string;
  businessId: string;
  creation?: boolean;
}

interface State {
  id: string;
  businessId: string;
  title: string;
  description: string;
  price: string;
  type: string;
  picture: string;
  longDescription: string;
  address: string;
  city: string;
  country: string;
  zip: string;
  coordinates: Coordinates;
  schedules: InstantSubmissionScheduleType;
  activeSection: number[];
  errorSection: number;
  partners: InstantPartner[];
  loading?: boolean;
}

export class InstantSubmissionContainer2 extends PureComponent<Props, State> {
  private defaultState: State;

  constructor(props) {
    super(props);
    this.defaultState = {
      id: '',
      businessId: this.props.businessId,
      description:
        I18n.getTranslation().instant_submission.default_short_description,
      picture: undefined,
      title: I18n.getTranslation().instant_submission.default_title,
      type: 'default',
      price: 'free',
      longDescription:
        I18n.getTranslation().instant_submission.long_description_text,
      address: '',
      city: '',
      country: '',
      zip: '',
      coordinates: {latitude: undefined, longitude: undefined},
      schedules: null,
      activeSection: [0],
      partners: [],
      errorSection: undefined,
      loading: true,
    };
    this.state = this.defaultState;
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.successSubmission &&
      this.props.successSubmission !== prevProps.successSubmission
    )
      this.props.navigation.goBack();
    else if (
      this.props.errorSubmission &&
      this.props.errorSubmission !== prevProps.errorSubmission
    )
      ApplicationToast.error(
        I18n.getTranslation().instant_submission.server_error,
      );
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderButtonText
          paddingRight={0}
          CTA={this.publishFilter}
          title={'Ok'}
          iconName={''}
        />
      ),
      headerLeft: () => (
        <HeaderButtonIcon
          paddingLeft={0}
          CTA={this.back}
          iconName={'angle-left'}
          color={'black'}
        />
      ),
    });
    storage.get(IS_FOR_YOU).then(res => {
      storage
        .get(
          res == '2' || res == 2
            ? ACTIVITY_APP_PREFERENCE_SAVED
            : res == 'true' || res == true
            ? FOR_YOU_APP_PREFERENCE_SAVED
            : CREATION_APP_PREFERENCE_SAVED,
        )
        .then(saved => {
          if (!saved) {
            this.setState({loading: false});
          } else {
            try {
              saved = typeof saved === 'string' ? JSON.parse(saved) : saved;

              let days = saved.find(
                d =>
                  d.key ===
                  (res == '2' || res == 2
                    ? 'map_days'
                    : res == 'true' || res == true
                    ? 'foryou_days'
                    : 'foryou_days2'),
              );
              days = days ? days.value : [];
              let k: 'fixed' | 'recurrent' = 'fixed';
              const items = Array.isArray(days)
                ? days.map(d => {
                    let p = d.split('|');
                    k = p[0] == 'fixed' ? 'fixed' : 'recurrent';
                    return {
                      id: '',
                      dateRange: {
                        key: p[0],
                        label: p[1].split(',')[0],
                        startTime: p[2],
                        endTime: p[3],
                      },
                      isPaused: false,
                      prePoned: false,
                    };
                  })
                : [];

              this.setState({
                loading: false,
                schedules: {key: k, schedule: items} as any,
              });
            } catch (error) {
              console.error(error);
              this.setState({loading: false});
            }
          }
        });
    });
  }

  render() {
    const spinner = this.props.loading ? (
      <Spinner background={'rgba(0,0,0,0.3)'} />
    ) : null;

    return (
      <KeyboardAwareScrollView style={styles.container}>
        {!this.state.loading && (
          <PlaningSection
            creation={true}
            hasError={false}
            instant={this.state}
            raiseUpdates={(key, schedule) =>
              this.setState({
                ...this.state,
                schedules: {key, schedule: schedule as any[]},
              })
            }
          />
        )}
        {this.state.loading && spinner}
      </KeyboardAwareScrollView>
    );
  }

  publishFilter = () => {
    if (this.state.schedules && this.state.schedules.schedule) {
      EventRegister.emit(
        SHEDULE_FILTER,
        this.state.schedules.schedule.map(d => {
          return {
            label: `${d.dateRange.label}`,
            value: `${d.dateRange.key}|${
              d.dateRange.label
            }|${'00:00:00'}|${'24:00:00'}|${d.dateRange.startTime}`,
          };
        }),
      );
    } else {
      EventRegister.emit(SHEDULE_FILTER, []);
    }
    this.props.navigation.goBack();
  };

  back = () => this.props.navigation.goBack();
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
  },
});
