import React, {PureComponent} from 'react';
import {<PERSON><PERSON>View, StyleSheet} from 'react-native';
import Accordion from 'react-native-collapsible/Accordion';
import {AccordionHeader} from '../../../../common/adapters/primaries/components/accordion/accordionHeader.presentational';
import {AccordionType} from '../../../../common/domain/entities/types/AppTypes';
import {I18n} from '../../../../configuration/i18n/i18n';
import {BasicInformationSection} from '../form_common/form/basicInformation/basicInformation.section';
import {LocationSection, LocationSection2} from '../form_common/form/location';
import {PartnersSection} from '../form_common/form/partner';
import {PlaningSection} from '../form_common/form/planing/planing.section';
import {
  InstantSubmissionFormKey,
  InstantSubmissionType,
} from '../form_common/formTypes';

interface Props {
  instant: InstantSubmissionType;
  onChange: (key: InstantSubmissionFormKey, value: string) => void;
  activeSection: number[];
  errorSection: number;
}

interface State {
  activeSection: number[];
  sections: AccordionType[];
}

export class InstantSubmissionAccordion extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      activeSection: this.props.activeSection,
      sections: [],
    };
  }

  static getDerivedStateFromProps(props) {
    const virtual = Number(props.instant.virtualLink) === 1;

    return {
      sections: [
        {
          rank: 0,
          title: I18n.getTranslation().instant_submission.basic_information,
          subTitle:
            I18n.getTranslation().instant_submission
              .basic_information_sub_title,
          content: (
            <BasicInformationSection
              hasError={props.errorSection === 0}
              raiseUpdates={(key, value) => props.onChange(key, value)}
            />
          ),
        },
        {
          rank: 1,
          title: I18n.getTranslation().instant_submission.location,
          subTitle: virtual
            ? I18n.getTranslation().instant_submission.location_sub_title2
            : I18n.getTranslation().instant_submission.location_sub_title,
          content: virtual ? (
            <LocationSection2
              hasError={props.errorSection === 1}
              raiseUpdates={(key, value) => props.onChange(key, value)}
              iconType={''}
              instant={props.instant}
            />
          ) : (
            <LocationSection
              hasError={props.errorSection === 1}
              raiseUpdates={(key, value) => props.onChange(key, value)}
              virtual={false}
              iconType={''}
              instant={props.instant}
            />
          ),
        },
        {
          rank: 2,
          title: I18n.getTranslation().instant_submission.active_days,
          subTitle:
            I18n.getTranslation().instant_submission.active_days_sub_title,
          content: (
            <PlaningSection
              hasError={props.errorSection === 2}
              instant={props.instant}
              raiseUpdates={(key, schedule) =>
                props.onChange('schedules', {
                  key,
                  schedule,
                })
              }
            />
          ),
        },
        {
          rank: 3,
          title: I18n.getTranslation().instant_submission.partners,
          subTitle: I18n.getTranslation().instant_submission.partners_subtitle,
          content: (
            <PartnersSection
              hasError={props.errorSection === 3}
              instant={props.instant}
              raiseUpdates={(key, partners) =>
                props.onChange('partners', partners)
              }
            />
          ),
        },
      ],
    };
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.activeSection !== this.props.activeSection)
      this.setState({activeSection: this.props.activeSection});
  }

  render() {
    return (
      <ScrollView contentContainerStyle={styles.container}>
        <Accordion
          activeSections={this.state.activeSection}
          sections={this.state.sections}
          containerStyle={{width: '93%'}}
          renderHeader={this.renderHeader}
          renderContent={this.renderContent}
          onChange={this.updateSections}
        />
      </ScrollView>
    );
  }

  private renderContent = section => {
    return section.content;
  };

  private renderHeader = section => {
    return (
      <AccordionHeader
        isOpened={this.state.activeSection.includes(section.rank)}
        rank={section.rank}
        title={section.title}
        subTitle={section.subTitle}
      />
    );
  };

  private updateSections = activeSection => {
    this.setState({activeSection});
  };
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 5,
    paddingBottom: 15,
    height: '100%',
    minWidth: '100%',
  },
});
