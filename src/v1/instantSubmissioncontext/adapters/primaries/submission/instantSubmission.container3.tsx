import React, {PureComponent} from 'react';
import {Alert, Platform, ScrollView, StyleSheet} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {ApplicationToast} from '../../../../common/adapters/primaries/components/application.toast';
import {DefaultTextInput} from '../../../../common/adapters/primaries/components/form/inputs/defaultText.input';
import {CustomizedCameraContainer} from '../../../../common/adapters/primaries/components/form/pickers/customizedCamera.container';
import {Spinner} from '../../../../common/adapters/primaries/components/spinner.presentational';

import {I18n} from '../../../../configuration/i18n/i18n';
import {SubmissionInstant} from '../../../domain/entities/submissionInstant';
import {HeaderButtonIcon, HeaderButtonText} from 'src/_components';
import {http, t, util} from 'src/_helpers';
import fStorage from '@react-native-firebase/storage';
import {EventRegister} from 'react-native-event-listeners';

interface Props {
  publishInstant: (
    submissionInstant: SubmissionInstant,
    businessId: string,
  ) => void;
  navigation: any;
  loading: boolean;
  successSubmission: boolean;
  errorSubmission: string;
  businessId: string;
  response?: boolean;
}

interface State {
  title: string;
  description: string;
  picture1: string;
  picture2: string;
  picture3: string;
  loading: boolean;
}

export class InstantSubmissionContainer3 extends PureComponent<Props, State> {
  private defaultState: State;

  constructor(props) {
    super(props);
    this.defaultState = {
      title: '',
      description: '',
      picture1: undefined,
      picture2: undefined,
      picture3: undefined,
      loading: false,
    };
    this.state = this.defaultState;
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.successSubmission &&
      this.props.successSubmission !== prevProps.successSubmission
    )
      this.props.navigation.goBack();
    else if (
      this.props.errorSubmission &&
      this.props.errorSubmission !== prevProps.errorSubmission
    )
      ApplicationToast.error(
        I18n.getTranslation().instant_submission.server_error,
      );
  }

  componentDidMount() {
    this.props.navigation.setOptions({
      title: this.props.navigation.state?.params
        ? I18n.getTranslation().business.add_response
        : I18n.getTranslation().instant_submission.add_request,
      headerRight: () => (
        <HeaderButtonText
          paddingRight={0}
          CTA={this.publishInstant}
          title={I18n.getTranslation().instant_submission.publish}
          iconName={''}
        />
      ),
      headerLeft: () => (
        <HeaderButtonIcon
          paddingLeft={0}
          CTA={this.back}
          iconName={'angle-left'}
          color={'black'}
        />
      ),
    });
  }

  render() {
    const spinner = this.state.loading ? (
      <Spinner background={'rgba(0,0,0,0.3)'} />
    ) : null;

    return (
      <KeyboardAwareScrollView style={styles.container}>
        {spinner}
        <RequestFrom
          {...this.state}
          {...this.props}
          raiseUpdates={(key, value) =>
            this.setState({...this.state, [key]: value})
          }
        />
      </KeyboardAwareScrollView>
    );
  }

  async uploadPic(picture: any, id: number) {
    if (!picture) return;
    try {
      const p =
        this.props.navigation.state?.params &&
        this.props.navigation.state?.params.request
          ? this.props.navigation.state?.params.request
          : null;
      const pp = p
        ? `/chat/support/responses/${id}/attachments`
        : `/chat/support/requests/${id}/attachments`;

      if (picture?.indexOf('file:/') > -1 || Platform.OS==="ios") {
        const reference = fStorage().ref(
          `requestsResponseAttachments/${picture.split('/').pop()}`,
        );
        await reference.putFile(picture);
        const link = await reference.getDownloadURL();
        const res = await http.post(pp, {link});
        console.log('++++++++ ', res);
      }
    } catch (error) {
      console.error(error);
    }
  }
  publishInstant = () => {
    if (
      !this.state.title &&
      !this.props.navigation.state?.params &&
      !this.props.navigation.state?.params.request
    ) {
      return util.alert(
        t(
          'Veuillez renseigner un titre pour votre requête',
          'Please fill a title for your request',
        ),
      );
    }
    if (!this.state.description) {
      return util.alert(
        t(
          'Veuillez renseigner une description pour votre requête',
          'Please give a description to your request',
        ),
      );
    }
    this.setState({loading: true});
    const p =
      this.props.navigation.state?.params &&
      this.props.navigation.state?.params.request
        ? this.props.navigation.state?.params.request
        : null;

    const path = p ? `/chat/support/responses` : '/chat/support/requests';
    const pp = p
      ? {
          message: this.state.description,
          requests_id: p.id,
        }
      : {
          titre: this.state.title,
          description: this.state.description,
        };

    http.post(path, pp).then(async res => {
      let id = Number(res.data);
      if (id > 0) {
        await this.uploadPic(this.state.picture1, id);
        await this.uploadPic(this.state.picture2, id);
        await this.uploadPic(this.state.picture3, id);
      }
      this.setState({loading: false, title: '', description: ''}, this.back);
    });
  };

  back = () => {
    if (this.props.loading === false)
      if (this.hasTheStateBeenChanged())
        Alert.alert(
          '',
          I18n.getTranslation().instant_submission.discard_changes,
          [
            {
              text: I18n.getTranslation().instant_submission.cancel,
              onPress: () => this.props.navigation.goBack(null),
              style: 'cancel',
            },
            {text: I18n.getTranslation().instant_submission.stay},
          ],
          {cancelable: false},
        );
      else {
        this.props.publishInstant(null, null);
        EventRegister.emit('refresh_request', true);
        this.props.navigation.navigate('business_request', {reload: true});
      }
  };

  hasTheStateBeenChanged(): boolean {
    if (this.state.title !== '' || this.state.description !== '') {
      return true;
    }
    return false;
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
  },
});

const RequestFrom = (props: any) => {
  const isResponse =
    props.navigation.state?.params && props.navigation.state?.params.request;
  return (
    <ScrollView
      style={{
        paddingHorizontal: 15,
      }}>
      {!isResponse && (
        <DefaultTextInput
          value={props.title}
          onChange={textValue => {
            props.raiseUpdates('title', textValue);
          }}
          error={props.titleError}
          style={{marginTop: 10}}
          numberOfLines={3}
          placeholder={I18n.getTranslation().instant_submission.title}
        />
      )}
      <DefaultTextInput
        value={props.description}
        style={{marginTop: 10}}
        numberOfLines={15}
        error={props.descriptionError}
        returnKeyType={'next'}
        blurOnSubmit={false}
        placeholder={'Message'}
        onChange={textValue => {
          props.raiseUpdates('description', textValue);
        }}
      />
      <CustomizedCameraContainer
        name={I18n.getTranslation().instant_submission.request_pic1}
        onPictureTaken={imageURI => props.raiseUpdates('picture1', imageURI)}
        transparent={false}
        imagePlaceholder={props.picture1}
        style={{marginTop: 10}}
      />
      <CustomizedCameraContainer
        name={I18n.getTranslation().instant_submission.request_pic2}
        onPictureTaken={imageURI => props.raiseUpdates('picture2', imageURI)}
        transparent={false}
        imagePlaceholder={props.picture2}
        style={{marginTop: 10}}
      />
      <CustomizedCameraContainer
        name={I18n.getTranslation().instant_submission.request_pic3}
        onPictureTaken={imageURI => props.raiseUpdates('picture3', imageURI)}
        transparent={false}
        imagePlaceholder={props.picture3}
        style={{marginTop: 10}}
      />
    </ScrollView>
  );
};
