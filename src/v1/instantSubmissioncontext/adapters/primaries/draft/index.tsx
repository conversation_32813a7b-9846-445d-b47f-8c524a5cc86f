import {connect, useDispatch} from 'react-redux';
import {AppState} from '../../../../configuration/AppState';
import {SubmissionInstant} from '../../../domain/entities/submissionInstant';
import {editInstant} from '../../../usecases/edition/editinstant.actions';
import {
  errorEditInstantSelector,
  isEditInstantLoadingSelector,
  successEditInstantSelector,
} from '../../../usecases/edition/editinstant.selectors';
import {loadInstantByIdForEdition} from '../../../usecases/loadById/instantLoadingById.actions';
import {
  errorLoadInstantForEditingSelector,
  isLoadInstantForEditingLoadingSelector,
  loadedInstantForEditionSelector,
} from '../../../usecases/loadById/instantLoadingById.selectors';
import {useEffect, useState} from 'react';
import {http, storage, t, util} from 'src/_helpers';
import {SubmissionInstantBuilder} from 'src/v1/instantSubmissioncontext/domain/entities/builder/submissionInstant.builder';
import {Spinner} from 'src/_components';
import {Constants} from 'src/_constants';
import {BusinessBuilder} from 'src/v1/businesscontext/domain/builder/business.builder';
import {updateAuthBusiness} from 'src/_reducers';
import fStorage from '@react-native-firebase/storage';
import { Alert, Platform } from 'react-native';
import { InstantDraftContainer } from './instantDraft.container';
import { InstantInformationBuilder } from 'src/v1/instantSubmissioncontext/domain/entities/builder/instantInformation.builder';

const mapStateToProps = (state: AppState) => ({
  loadByIdLoading: isLoadInstantForEditingLoadingSelector(state),
  editionLoading: isEditInstantLoadingSelector(state),
  retrievedInstant: loadedInstantForEditionSelector(state),
  successEdition: successEditInstantSelector(state),
  errorEdition: errorEditInstantSelector(state),
  errorLoadById: errorLoadInstantForEditingSelector(state),
});
/*
const mapDispatchToProps = dispatch => ({
  editInstant: (submissionInstant: SubmissionInstant) =>
    dispatch(editInstant(submissionInstant)),
  loadById: (id: string) => dispatch(loadInstantByIdForEdition(id)),
});
export const InstantEdition = connect(
  mapStateToProps,
  mapDispatchToProps,
)(InstantEditionContainer);*/

export const DraftEdition = (props: any) => {
  console.log("DraftEdition props: ", props)
  const dispatch = useDispatch();
  const [state, setState] = useState({
    retrievedInstant: undefined,
    loadByIdLoading: true,
    editionLoading: false,
    errorLoadById: '',
    errorEdition: '',
    successEdition: false,
    business: undefined,
  });
  const [uniqueId, setUniqueId] = useState(null);

  const loadBusiness = async () => {
    const user = await storage.get(Constants.USER_INFO_KEY);
    const company = user?.company;

    setState(prevState => ({
      ...prevState,
      loading: false,
      business: company?.uuid
        ? new BusinessBuilder()
            .withId(company.uuid)
            .withName(company.name)
            .withType(company.type)
            .withActivity(company.activity)
            .withAddress(company.location.address)
            .withZipCode(company.location.zipCode)
            .withCity(company.location.city)
            .withCountry(company.location.country)
            .withActive(company.active)
            .withLongitude(Number(company.location.longitude))
            .withLatitude(Number(company.location.latitude))
            .withSiren(company.metas.siren)
            .withCoverImage(company.profile.coverImage)
            .withProfileImage(company.profile.profileImage)
            .withOpeningHours(company.activeDays ?? [])
            .withDescription(company.description)
            .withWebsite(company.contact.website)
            .withEmail(company.contact.email)
            .withPhoneNumber(company.contact.phoneNumber)
            .build()
        : undefined,
    }));
  };

  const loadInstant = async () => {

    const drafts = props.route.params.activityDraft;
    setUniqueId(props.route.params.activityDraft._uniqueId);

    let retrievedInstant = undefined;
    if (drafts._instantInformation._businessId) {
      retrievedInstant = new SubmissionInstantBuilder()
        .withBusinessId(drafts._instantInformation._businessId)
        .withCategory(drafts._instantInformation._category)
        .withTitle(drafts._instantInformation._title)
        .withShortDescription(drafts._instantInformation._shortDescription)
        .withLongDescription(drafts._instantInformation._longDescription)
        .withPicture(drafts._instantInformation._picture)
        .withAddress(drafts._address._address)
        .withCity(drafts._address._city)
        .withCountry(drafts._address._country)
        .withPrice(drafts._instantInformation._price)
        .withZipcode(drafts._address._zipCode)
        .withCoords(drafts._address._coords)
        .withActivationTimes(drafts._instantActivationTimes)
        .withPartners(drafts._instantPartner)
        .withProOnly(drafts._pro_only)
        .build();
      console.log("Retrieved activity: ", retrievedInstant);
    }
    setState(prevState => ({
      ...prevState,
      retrievedInstant,
      loadByIdLoading: false,
    }));
  };

  const isAddressIncomplete = (instantSubmission: SubmissionInstant) => {
    console.log("Address: ", instantSubmission.address);
    return (
      instantSubmission.address.address === '' ||
      instantSubmission.address.country === '' ||
      instantSubmission.address.city === '' ||
      instantSubmission.address.zipCode === '' ||
      instantSubmission.address.coords.latitude === undefined ||
      instantSubmission.address.coords.longitude === undefined   
    );
  };

  // Helper function to compare two _instantInformation objects
  const isEqual = (obj1, obj2) => {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (let key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  };

  // Function to update an existing draft
  const updateDraftInArray = async (key, uniqueId, updatedDraftData) => {
    try {
      let currentArray = await storage.get(key);
      
      if (!currentArray) {
        throw new Error(`Array with key ${key} does not exist`);
      }
      
      if (typeof currentArray === 'string') {
        currentArray = JSON.parse(currentArray);
      }
      
      const index = currentArray.findIndex(draft => draft._uniqueId === uniqueId);
      
      if (index === -1) {
        throw new Error(`Draft with uniqueId ${uniqueId} not found in the array`);
      }
      
      // Update the draft, preserving the _uniqueId
      currentArray[index] = {
        ...currentArray[index],
        ...updatedDraftData,
        _uniqueId: uniqueId // Ensure the uniqueId is not overwritten
      };
      
      console.log(`Updating draft in ${key} with uniqueId ${uniqueId}:`, currentArray[index]);
      
      await storage.set(key, currentArray);
      
      const updatedArray = await storage.get(key);
      if (updatedArray) {
        console.log('Updated array:', updatedArray);
      }
      
      console.log('Draft updated successfully');
      return currentArray[index];
    } catch (e) {
      console.error('Error updating draft:', e);
      throw e;
    }
  };

  useEffect(() => {
    loadInstant();
    loadBusiness();
  }, [props.route.params.activityDraft]);

  return (
    <InstantDraftContainer
      loadById={function (id: string): void {
        console.log(id);
      }}
      editDraft={async function (submissionInstant: SubmissionInstant) {
        setState(prev => ({...prev, loading: true}));
        console.log("ActivationTimes: ", submissionInstant.instantActivationTimes);
        if (submissionInstant.instantActivationTimes.length === 0 || isAddressIncomplete(submissionInstant)) {
          Alert.alert(t("Mettre à jour le brouillon?", "Update this draft?"), t("Les informations de l'activité ne sont pas toutes renseignées. Les horaires et la localisation sont nécessaires pour publier votre activité. Voulez-vous mettre à jour ce brouillon et le finaliser plus tard?", "The activity information is incomplete. The schedules and location are auxiliary for publishing your activity. Do you want to update this draft and complete it later?"), [
            {
              text: t('Completer les informations', 'Fill in the information'),
              onPress: () => {
                console.log('Cancel Pressed');
              },
              style: 'cancel',
            },
            {
              text: t('Mettre à jour le brouillon', 'Update draft'),
              onPress: async() => {
                console.log('Save as draft Pressed');
                const user = await storage.get(Constants.USER_INFO_KEY);
                console.log("user uuid: ", user?.uuid);
                await updateDraftInArray(user?.uuid, uniqueId, submissionInstant);
                props.navigation.goBack();
                return;
              }
            },
          ]);
        } else {
          let payload: any = {...submissionInstant};
          if (
            submissionInstant?.instantInformation?.picture?.startsWith('file:/') || (Platform.OS==="ios")
          ) {
            setState(prev => ({...prev, editionLoading: true}));
            if(submissionInstant?.instantInformation?.picture)
            {
              try {
                const reference = fStorage().ref(
                  `activityPics/${submissionInstant.instantInformation.picture
                    .split('/')
                    .pop()}`,
                );
                await reference.putFile(
                  submissionInstant.instantInformation.picture,
                );
                const url = await reference.getDownloadURL();
    
                payload = {
                  ...payload,
                  _instantInformation: {
                    ...payload._instantInformation,
                    _picture: url,
                  },
                };
              } catch (error) {}
            }
          }

          setState(prev => ({...prev, editionLoading: true}));

          //payload = await util.tryGeocodeInstantPayloadAddress(payload);
          console.log("Payload: ", payload);
          const res = await http.post('/activities/create', payload);

          if (res.data?.id) {
            dispatch(updateAuthBusiness(state.business));
            props.navigation.goBack();
          } else {
            setState(prev => ({...prev, editionLoading: true}));
          }
        }
      }}
      retrievedInstant={state.retrievedInstant}
      loadByIdLoading={state.loadByIdLoading}
      editionLoading={state.editionLoading}
      errorLoadById={state.errorLoadById}
      navigation={props.navigation}
      errorEdition={state.errorEdition}
      successEdition={state.successEdition}
    />
  );
};
