import Config from "react-native-config";
import { applyMiddleware, createStore } from "redux";
import { createLogger } from "redux-logger";
import { AppState } from "./AppState";
import { epicsMiddleware, rootEpics } from "./epicsMiddleware.redux";
import { reduxReducer } from "./reducers.redux";

const logger = createLogger({
  collapsed: true,
  level: "info",
});

export const reduxStore = () => {
  let store;
  if (Config.ENV === "dev")
    /*store = createStore<AppState, any, any, any>(
            reduxReducer,
                applyMiddleware(epicsMiddleware, logger)
        )*/
    store = createStore<AppState, any, any, any>(
      reduxReducer,
      applyMiddleware(epicsMiddleware)
    );
  else
    store = createStore<AppState, any, any, any>(
      reduxReducer,
      applyMiddleware(epicsMiddleware)
    );
  epicsMiddleware.run(rootEpics);

  return store;
};
