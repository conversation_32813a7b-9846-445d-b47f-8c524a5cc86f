import { AccountState } from '../accountcontext/configuration/account.state';
import { BusinessState } from '../businesscontext/configuration/business.state';
import { ApplicationState } from '../common/configuration/application.states'
import { InstantsState } from '../instantscontext/configuration/instant.states'
import {
    InstantManagementState
} from '../instantSubmissioncontext/configuration/instantSubmission.state';
import { SignupState } from '../signupcontext/configuration/signupState';

export interface AppState {
    instants: InstantsState
    application: ApplicationState
    signup: SignupState
    account: AccountState
    business: BusinessState
    instantManagement: InstantManagementState
}
