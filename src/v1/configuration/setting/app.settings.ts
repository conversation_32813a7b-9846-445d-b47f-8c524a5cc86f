import {I18n} from '../i18n/i18n';

export const GlobalSettings = {
  min_display_radius: 2000,
  //min_retrieval_radius: 450,
  min_retrieval_radius: 5137,
  centerClichy: {latitude: 48.903617, longitude: 2.304809},
  initialZoomMap: 180,
  radiusClichy: 1400,
  avatarWidth: 1000,
  avatarHeight: 1000,
  quality: 0.8,
};

export const Categories = {
  aikido: I18n.getTranslation().common.settings.categories.aikido,
  american_football:
    I18n.getTranslation().common.settings.categories.american_football,
  fishing: I18n.getTranslation().common.settings.categories.fishing,
  badminton: I18n.getTranslation().common.settings.categories.badminton,
  baseball: I18n.getTranslation().common.settings.categories.baseball,
  basketball: I18n.getTranslation().common.settings.categories.basketball,
  biathlon: I18n.getTranslation().common.settings.categories.biathlon,
  billiards: I18n.getTranslation().common.settings.categories.billiards,
  boxing: I18n.getTranslation().common.settings.categories.boxing,
  canoeing: I18n.getTranslation().common.settings.categories.canoeing,
  climbing: I18n.getTranslation().common.settings.categories.climbing,
  cricket: I18n.getTranslation().common.settings.categories.cricket,
  crossfit: I18n.getTranslation().common.settings.categories.crossfit,
  cycling: I18n.getTranslation().common.settings.categories.cycling,
  dance: I18n.getTranslation().common.settings.categories.dance,
  fitness: I18n.getTranslation().common.settings.categories.fitness,
  fencing: I18n.getTranslation().common.settings.categories.fencing,
  fives: I18n.getTranslation().common.settings.categories.fives,
  floorball: I18n.getTranslation().common.settings.categories.floorball,
  football: I18n.getTranslation().common.settings.categories.football,
  futsal: I18n.getTranslation().common.settings.categories.futsal,
  geocaching: I18n.getTranslation().common.settings.categories.geocaching,
  golf: I18n.getTranslation().common.settings.categories.golf,
  mini_golf: I18n.getTranslation().common.settings.categories.mini_golf,
  gymnastics: I18n.getTranslation().common.settings.categories.gymnastics,
  handball: I18n.getTranslation().common.settings.categories.handball,
  hockey: I18n.getTranslation().common.settings.categories.hockey,
  hiking: I18n.getTranslation().common.settings.categories.hiking,
  holistic_classes:
    I18n.getTranslation().common.settings.categories.holistic_classes,
  ice_skating: I18n.getTranslation().common.settings.categories.ice_skating,
  ju_jitsu: I18n.getTranslation().common.settings.categories.ju_jitsu,
  judo: I18n.getTranslation().common.settings.categories.judo,
  karate: I18n.getTranslation().common.settings.categories.karate,
  karting: I18n.getTranslation().common.settings.categories.karting,
  kayaking: I18n.getTranslation().common.settings.categories.kayaking,
  kickboxing: I18n.getTranslation().common.settings.categories.kickboxing,
  krav_maga: I18n.getTranslation().common.settings.categories.krav_maga,
  kung_fu: I18n.getTranslation().common.settings.categories.kung_fu,
  lacrosse: I18n.getTranslation().common.settings.categories.lacrosse,
  mixed_martial_arts:
    I18n.getTranslation().common.settings.categories.mixed_martial_arts,
  motor_sport: I18n.getTranslation().common.settings.categories.motor_sport,
  mountain_biking:
    I18n.getTranslation().common.settings.categories.mountain_biking,
  muay_thai: I18n.getTranslation().common.settings.categories.muay_thai,
  orienteering: I18n.getTranslation().common.settings.categories.orienteering,
  petanque: I18n.getTranslation().common.settings.categories.petanque,
  paintball: I18n.getTranslation().common.settings.categories.paintball,
  roller_sports: I18n.getTranslation().common.settings.categories.roller_sports,
  rowing: I18n.getTranslation().common.settings.categories.rowing,
  rugby: I18n.getTranslation().common.settings.categories.rugby,
  running: I18n.getTranslation().common.settings.categories.running,
  shooting: I18n.getTranslation().common.settings.categories.shooting,
  skateboarding: I18n.getTranslation().common.settings.categories.skateboarding,
  skiing: I18n.getTranslation().common.settings.categories.skiing,
  snowboarding: I18n.getTranslation().common.settings.categories.snowboarding,
  squash: I18n.getTranslation().common.settings.categories.squash,
  swimming: I18n.getTranslation().common.settings.categories.swimming,
  table_tennis: I18n.getTranslation().common.settings.categories.table_tennis,
  taekwondo: I18n.getTranslation().common.settings.categories.taekwondo,
  tennis: I18n.getTranslation().common.settings.categories.tennis,
  padel_tennis: I18n.getTranslation().common.settings.categories.padel_tennis,
  triathlon: I18n.getTranslation().common.settings.categories.triathlon,
  frisbee: I18n.getTranslation().common.settings.categories.frisbee,
  volleyball: I18n.getTranslation().common.settings.categories.volleyball,
  beach_volleyball:
    I18n.getTranslation().common.settings.categories.beach_volleyball,
  walking: I18n.getTranslation().common.settings.categories.walking,
  water_sports: I18n.getTranslation().common.settings.categories.water_sports,
  wrestling: I18n.getTranslation().common.settings.categories.wrestling,
  yoga: I18n.getTranslation().common.settings.categories.yoga,
  other: I18n.getTranslation().common.settings.categories.other,
  pro: I18n.getTranslation().common.settings.categories.pro,
  sports_facility:
    I18n.getTranslation().common.settings.categories.sports_facility,
  shop: I18n.getTranslation().common.settings.categories.shop,
};

export const CategoryIds = {
  aikido: 1,
  american_football: 2,
  fishing: 3,
  badminton: 4,
  baseball: 5,
  basketball: 6,
  biathlon: 7,
  billiards: 8,
  boxing: 9,
  canoeing: 10,
  climbing: 11,
  cricket: 12,
  crossfit: 13,
  cycling: 14,
  dance: 15,
  fitness: 16,
  fencing: 17,
  fives: 18,
  floorball: 19,
  football: 20,
  futsal: 21,
  geocaching: 22,
  golf: 23,
  mini_golf: 24,
  gymnastics: 25,
  handball: 26,
  hockey: 27,
  hiking: 28,
  holistic_classes: 29,
  ice_skating: 30,
  ju_jitsu: 31,
  judo: 32,
  karate: 33,
  karting: 34,
  kayaking: 35,
  kickboxing: 36,
  krav_maga: 37,
  kung_fu: 38,
  lacrosse: 39,
  mixed_martial_arts: 40,
  motor_sport: 41,
  mountain_biking: 42,
  muay_thai: 43,
  orienteering: 44,
  petanque: 45,
  paintball: 46,
  roller_sports: 47,
  rowing: 48,
  rugby: 49,
  running: 50,
  shooting: 51,
  skateboarding: 52,
  skiing: 53,
  snowboarding: 54,
  squash: 55,
  swimming: 56,
  table_tennis: 57,
  taekwondo: 58,
  tennis: 59,
  padel_tennis: 60,
  triathlon: 61,
  frisbee: 62,
  volleyball: 63,
  beach_volleyball: 64,
  walking: 65,
  water_sports: 66,
  wrestling: 67,
  yoga: 68,
  other: 69,
  pro: 70,
  sports_facility: 71,
  shop: 72,
};

export const ActiveDaysNames = {
  all_days: I18n.getTranslation().instant_submission.all_days,
  mondays_to_fridays:
    I18n.getTranslation().instant_submission.mondays_to_fridays,
  saturdays_and_sundays:
    I18n.getTranslation().instant_submission.saturdays_and_sundays,
  all_mondays: I18n.getTranslation().instant_submission.all_mondays,
  all_tuesdays: I18n.getTranslation().instant_submission.all_tuesdays,
  all_wednesdays: I18n.getTranslation().instant_submission.all_wednesdays,
  all_thursdays: I18n.getTranslation().instant_submission.all_thursdays,
  all_fridays: I18n.getTranslation().instant_submission.all_fridays,
  all_saturdays: I18n.getTranslation().instant_submission.all_saturdays,
  all_sundays: I18n.getTranslation().instant_submission.all_sundays,
};

export const Countries = () => [
  {name: I18n.getTranslation().common.settings.countries.AF, code: 'AF'},
  {name: I18n.getTranslation().common.settings.countries.AX, code: 'AX'},
  {name: I18n.getTranslation().common.settings.countries.AL, code: 'AL'},
  {name: I18n.getTranslation().common.settings.countries.DZ, code: 'DZ'},
  {name: I18n.getTranslation().common.settings.countries.AS, code: 'AS'},
  {name: I18n.getTranslation().common.settings.countries.AD, code: 'AD'},
  {name: I18n.getTranslation().common.settings.countries.AO, code: 'AO'},
  {name: I18n.getTranslation().common.settings.countries.AI, code: 'AI'},
  {name: I18n.getTranslation().common.settings.countries.AQ, code: 'AQ'},
  {name: I18n.getTranslation().common.settings.countries.AG, code: 'AG'},
  {name: I18n.getTranslation().common.settings.countries.AR, code: 'AR'},
  {name: I18n.getTranslation().common.settings.countries.AM, code: 'AM'},
  {name: I18n.getTranslation().common.settings.countries.AW, code: 'AW'},
  {name: I18n.getTranslation().common.settings.countries.AU, code: 'AU'},
  {name: I18n.getTranslation().common.settings.countries.AT, code: 'AT'},
  {name: I18n.getTranslation().common.settings.countries.AZ, code: 'AZ'},
  {name: I18n.getTranslation().common.settings.countries.BS, code: 'BS'},
  {name: I18n.getTranslation().common.settings.countries.BH, code: 'BH'},
  {name: I18n.getTranslation().common.settings.countries.BD, code: 'BD'},
  {name: I18n.getTranslation().common.settings.countries.BB, code: 'BB'},
  {name: I18n.getTranslation().common.settings.countries.BY, code: 'BY'},
  {name: I18n.getTranslation().common.settings.countries.BE, code: 'BE'},
  {name: I18n.getTranslation().common.settings.countries.BZ, code: 'BZ'},
  {name: I18n.getTranslation().common.settings.countries.BJ, code: 'BJ'},
  {name: I18n.getTranslation().common.settings.countries.BM, code: 'BM'},
  {name: I18n.getTranslation().common.settings.countries.BT, code: 'BT'},
  {name: I18n.getTranslation().common.settings.countries.BO, code: 'BO'},
  {name: I18n.getTranslation().common.settings.countries.BA, code: 'BA'},
  {name: I18n.getTranslation().common.settings.countries.BW, code: 'BW'},
  {name: I18n.getTranslation().common.settings.countries.BV, code: 'BV'},
  {name: I18n.getTranslation().common.settings.countries.BR, code: 'BR'},
  {name: I18n.getTranslation().common.settings.countries.IO, code: 'IO'},
  {name: I18n.getTranslation().common.settings.countries.BN, code: 'BN'},
  {name: I18n.getTranslation().common.settings.countries.BG, code: 'BG'},
  {name: I18n.getTranslation().common.settings.countries.BF, code: 'BF'},
  {name: I18n.getTranslation().common.settings.countries.BI, code: 'BI'},
  {name: I18n.getTranslation().common.settings.countries.KH, code: 'KH'},
  {name: I18n.getTranslation().common.settings.countries.CM, code: 'CM'},
  {name: I18n.getTranslation().common.settings.countries.CA, code: 'CA'},
  {name: I18n.getTranslation().common.settings.countries.CV, code: 'CV'},
  {name: I18n.getTranslation().common.settings.countries.KY, code: 'KY'},
  {name: I18n.getTranslation().common.settings.countries.CF, code: 'CF'},
  {name: I18n.getTranslation().common.settings.countries.TD, code: 'TD'},
  {name: I18n.getTranslation().common.settings.countries.CL, code: 'CL'},
  {name: I18n.getTranslation().common.settings.countries.CN, code: 'CN'},
  {name: I18n.getTranslation().common.settings.countries.CX, code: 'CX'},
  {name: I18n.getTranslation().common.settings.countries.CC, code: 'CC'},
  {name: I18n.getTranslation().common.settings.countries.CO, code: 'CO'},
  {name: I18n.getTranslation().common.settings.countries.KM, code: 'KM'},
  {name: I18n.getTranslation().common.settings.countries.CG, code: 'CG'},
  {name: I18n.getTranslation().common.settings.countries.CD, code: 'CD'},
  {name: I18n.getTranslation().common.settings.countries.CK, code: 'CK'},
  {name: I18n.getTranslation().common.settings.countries.CR, code: 'CR'},
  {name: I18n.getTranslation().common.settings.countries.CI, code: 'CI'},
  {name: I18n.getTranslation().common.settings.countries.HR, code: 'HR'},
  {name: I18n.getTranslation().common.settings.countries.CU, code: 'CU'},
  {name: I18n.getTranslation().common.settings.countries.CY, code: 'CY'},
  {name: I18n.getTranslation().common.settings.countries.CZ, code: 'CZ'},
  {name: I18n.getTranslation().common.settings.countries.DK, code: 'DK'},
  {name: I18n.getTranslation().common.settings.countries.DJ, code: 'DJ'},
  {name: I18n.getTranslation().common.settings.countries.DM, code: 'DM'},
  {name: I18n.getTranslation().common.settings.countries.DO, code: 'DO'},
  {name: I18n.getTranslation().common.settings.countries.EC, code: 'EC'},
  {name: I18n.getTranslation().common.settings.countries.EG, code: 'EG'},
  {name: I18n.getTranslation().common.settings.countries.SV, code: 'SV'},
  {name: I18n.getTranslation().common.settings.countries.GQ, code: 'GQ'},
  {name: I18n.getTranslation().common.settings.countries.ER, code: 'ER'},
  {name: I18n.getTranslation().common.settings.countries.EE, code: 'EE'},
  {name: I18n.getTranslation().common.settings.countries.ET, code: 'ET'},
  {name: I18n.getTranslation().common.settings.countries.FK, code: 'FK'},
  {name: I18n.getTranslation().common.settings.countries.FO, code: 'FO'},
  {name: I18n.getTranslation().common.settings.countries.FJ, code: 'FJ'},
  {name: I18n.getTranslation().common.settings.countries.FI, code: 'FI'},
  {name: I18n.getTranslation().common.settings.countries.FR, code: 'FR'},
  {name: I18n.getTranslation().common.settings.countries.GF, code: 'GF'},
  {name: I18n.getTranslation().common.settings.countries.PF, code: 'PF'},
  {name: I18n.getTranslation().common.settings.countries.TF, code: 'TF'},
  {name: I18n.getTranslation().common.settings.countries.GA, code: 'GA'},
  {name: I18n.getTranslation().common.settings.countries.GM, code: 'GM'},
  {name: I18n.getTranslation().common.settings.countries.GE, code: 'GE'},
  {name: I18n.getTranslation().common.settings.countries.DE, code: 'DE'},
  {name: I18n.getTranslation().common.settings.countries.GH, code: 'GH'},
  {name: I18n.getTranslation().common.settings.countries.GI, code: 'GI'},
  {name: I18n.getTranslation().common.settings.countries.GR, code: 'GR'},
  {name: I18n.getTranslation().common.settings.countries.GL, code: 'GL'},
  {name: I18n.getTranslation().common.settings.countries.GD, code: 'GD'},
  {name: I18n.getTranslation().common.settings.countries.GP, code: 'GP'},
  {name: I18n.getTranslation().common.settings.countries.GU, code: 'GU'},
  {name: I18n.getTranslation().common.settings.countries.GT, code: 'GT'},
  {name: I18n.getTranslation().common.settings.countries.GG, code: 'GG'},
  {name: I18n.getTranslation().common.settings.countries.GN, code: 'GN'},
  {name: I18n.getTranslation().common.settings.countries.GW, code: 'GW'},
  {name: I18n.getTranslation().common.settings.countries.GY, code: 'GY'},
  {name: I18n.getTranslation().common.settings.countries.HT, code: 'HT'},
  {name: I18n.getTranslation().common.settings.countries.HM, code: 'HM'},
  {name: I18n.getTranslation().common.settings.countries.VA, code: 'VA'},
  {name: I18n.getTranslation().common.settings.countries.HN, code: 'HN'},
  {name: I18n.getTranslation().common.settings.countries.HK, code: 'HK'},
  {name: I18n.getTranslation().common.settings.countries.HU, code: 'HU'},
  {name: I18n.getTranslation().common.settings.countries.IS, code: 'IS'},
  {name: I18n.getTranslation().common.settings.countries.IN, code: 'IN'},
  {name: I18n.getTranslation().common.settings.countries.ID, code: 'ID'},
  {name: I18n.getTranslation().common.settings.countries.IR, code: 'IR'},
  {name: I18n.getTranslation().common.settings.countries.IQ, code: 'IQ'},
  {name: I18n.getTranslation().common.settings.countries.IE, code: 'IE'},
  {name: I18n.getTranslation().common.settings.countries.IM, code: 'IM'},
  {name: I18n.getTranslation().common.settings.countries.IL, code: 'IL'},
  {name: I18n.getTranslation().common.settings.countries.IT, code: 'IT'},
  {name: I18n.getTranslation().common.settings.countries.JM, code: 'JM'},
  {name: I18n.getTranslation().common.settings.countries.JP, code: 'JP'},
  {name: I18n.getTranslation().common.settings.countries.JE, code: 'JE'},
  {name: I18n.getTranslation().common.settings.countries.JO, code: 'JO'},
  {name: I18n.getTranslation().common.settings.countries.KZ, code: 'KZ'},
  {name: I18n.getTranslation().common.settings.countries.KE, code: 'KE'},
  {name: I18n.getTranslation().common.settings.countries.KI, code: 'KI'},
  {name: I18n.getTranslation().common.settings.countries.KP, code: 'KP'},
  {name: I18n.getTranslation().common.settings.countries.KR, code: 'KR'},
  {name: I18n.getTranslation().common.settings.countries.KW, code: 'KW'},
  {name: I18n.getTranslation().common.settings.countries.KG, code: 'KG'},
  {name: I18n.getTranslation().common.settings.countries.LA, code: 'LA'},
  {name: I18n.getTranslation().common.settings.countries.LV, code: 'LV'},
  {name: I18n.getTranslation().common.settings.countries.LB, code: 'LB'},
  {name: I18n.getTranslation().common.settings.countries.LS, code: 'LS'},
  {name: I18n.getTranslation().common.settings.countries.LR, code: 'LR'},
  {name: I18n.getTranslation().common.settings.countries.LY, code: 'LY'},
  {name: I18n.getTranslation().common.settings.countries.LI, code: 'LI'},
  {name: I18n.getTranslation().common.settings.countries.LT, code: 'LT'},
  {name: I18n.getTranslation().common.settings.countries.LU, code: 'LU'},
  {name: I18n.getTranslation().common.settings.countries.MO, code: 'MO'},
  {name: I18n.getTranslation().common.settings.countries.MK, code: 'MK'},
  {name: I18n.getTranslation().common.settings.countries.MG, code: 'MG'},
  {name: I18n.getTranslation().common.settings.countries.MW, code: 'MW'},
  {name: I18n.getTranslation().common.settings.countries.MY, code: 'MY'},
  {name: I18n.getTranslation().common.settings.countries.MV, code: 'MV'},
  {name: I18n.getTranslation().common.settings.countries.ML, code: 'ML'},
  {name: I18n.getTranslation().common.settings.countries.MT, code: 'MT'},
  {name: I18n.getTranslation().common.settings.countries.MH, code: 'MH'},
  {name: I18n.getTranslation().common.settings.countries.MQ, code: 'MQ'},
  {name: I18n.getTranslation().common.settings.countries.MR, code: 'MR'},
  {name: I18n.getTranslation().common.settings.countries.MU, code: 'MU'},
  {name: I18n.getTranslation().common.settings.countries.YT, code: 'YT'},
  {name: I18n.getTranslation().common.settings.countries.MX, code: 'MX'},
  {name: I18n.getTranslation().common.settings.countries.FM, code: 'FM'},
  {name: I18n.getTranslation().common.settings.countries.MD, code: 'MD'},
  {name: I18n.getTranslation().common.settings.countries.MC, code: 'MC'},
  {name: I18n.getTranslation().common.settings.countries.MN, code: 'MN'},
  {name: I18n.getTranslation().common.settings.countries.MS, code: 'MS'},
  {name: I18n.getTranslation().common.settings.countries.MA, code: 'MA'},
  {name: I18n.getTranslation().common.settings.countries.MZ, code: 'MZ'},
  {name: I18n.getTranslation().common.settings.countries.MM, code: 'MM'},
  {name: I18n.getTranslation().common.settings.countries.NA, code: 'NA'},
  {name: I18n.getTranslation().common.settings.countries.NR, code: 'NR'},
  {name: I18n.getTranslation().common.settings.countries.NP, code: 'NP'},
  {name: I18n.getTranslation().common.settings.countries.NL, code: 'NL'},
  {name: I18n.getTranslation().common.settings.countries.AN, code: 'AN'},
  {name: I18n.getTranslation().common.settings.countries.NC, code: 'NC'},
  {name: I18n.getTranslation().common.settings.countries.NZ, code: 'NZ'},
  {name: I18n.getTranslation().common.settings.countries.NI, code: 'NI'},
  {name: I18n.getTranslation().common.settings.countries.NE, code: 'NE'},
  {name: I18n.getTranslation().common.settings.countries.NG, code: 'NG'},
  {name: I18n.getTranslation().common.settings.countries.NU, code: 'NU'},
  {name: I18n.getTranslation().common.settings.countries.NF, code: 'NF'},
  {name: I18n.getTranslation().common.settings.countries.MP, code: 'MP'},
  {name: I18n.getTranslation().common.settings.countries.NO, code: 'NO'},
  {name: I18n.getTranslation().common.settings.countries.OM, code: 'OM'},
  {name: I18n.getTranslation().common.settings.countries.PK, code: 'PK'},
  {name: I18n.getTranslation().common.settings.countries.PW, code: 'PW'},
  {name: I18n.getTranslation().common.settings.countries.PS, code: 'PS'},
  {name: I18n.getTranslation().common.settings.countries.PA, code: 'PA'},
  {name: I18n.getTranslation().common.settings.countries.PG, code: 'PG'},
  {name: I18n.getTranslation().common.settings.countries.PY, code: 'PY'},
  {name: I18n.getTranslation().common.settings.countries.PE, code: 'PE'},
  {name: I18n.getTranslation().common.settings.countries.PH, code: 'PH'},
  {name: I18n.getTranslation().common.settings.countries.PN, code: 'PN'},
  {name: I18n.getTranslation().common.settings.countries.PL, code: 'PL'},
  {name: I18n.getTranslation().common.settings.countries.PT, code: 'PT'},
  {name: I18n.getTranslation().common.settings.countries.PR, code: 'PR'},
  {name: I18n.getTranslation().common.settings.countries.QA, code: 'QA'},
  {name: I18n.getTranslation().common.settings.countries.RE, code: 'RE'},
  {name: I18n.getTranslation().common.settings.countries.RO, code: 'RO'},
  {name: I18n.getTranslation().common.settings.countries.RU, code: 'RU'},
  {name: I18n.getTranslation().common.settings.countries.RW, code: 'RW'},
  {name: I18n.getTranslation().common.settings.countries.SH, code: 'SH'},
  {name: I18n.getTranslation().common.settings.countries.KN, code: 'KN'},
  {name: I18n.getTranslation().common.settings.countries.LC, code: 'LC'},
  {name: I18n.getTranslation().common.settings.countries.PM, code: 'PM'},
  {name: I18n.getTranslation().common.settings.countries.VC, code: 'VC'},
  {name: I18n.getTranslation().common.settings.countries.WS, code: 'WS'},
  {name: I18n.getTranslation().common.settings.countries.SM, code: 'SM'},
  {name: I18n.getTranslation().common.settings.countries.ST, code: 'ST'},
  {name: I18n.getTranslation().common.settings.countries.SA, code: 'SA'},
  {name: I18n.getTranslation().common.settings.countries.SN, code: 'SN'},
  {name: I18n.getTranslation().common.settings.countries.CS, code: 'CS'},
  {name: I18n.getTranslation().common.settings.countries.SC, code: 'SC'},
  {name: I18n.getTranslation().common.settings.countries.SL, code: 'SL'},
  {name: I18n.getTranslation().common.settings.countries.SG, code: 'SG'},
  {name: I18n.getTranslation().common.settings.countries.SK, code: 'SK'},
  {name: I18n.getTranslation().common.settings.countries.SI, code: 'SI'},
  {name: I18n.getTranslation().common.settings.countries.SB, code: 'SB'},
  {name: I18n.getTranslation().common.settings.countries.SO, code: 'SO'},
  {name: I18n.getTranslation().common.settings.countries.ZA, code: 'ZA'},
  {name: I18n.getTranslation().common.settings.countries.GS, code: 'GS'},
  {name: I18n.getTranslation().common.settings.countries.ES, code: 'ES'},
  {name: I18n.getTranslation().common.settings.countries.LK, code: 'LK'},
  {name: I18n.getTranslation().common.settings.countries.SD, code: 'SD'},
  {name: I18n.getTranslation().common.settings.countries.SR, code: 'SR'},
  {name: I18n.getTranslation().common.settings.countries.SJ, code: 'SJ'},
  {name: I18n.getTranslation().common.settings.countries.SZ, code: 'SZ'},
  {name: I18n.getTranslation().common.settings.countries.SE, code: 'SE'},
  {name: I18n.getTranslation().common.settings.countries.CH, code: 'CH'},
  {name: I18n.getTranslation().common.settings.countries.SY, code: 'SY'},
  {name: I18n.getTranslation().common.settings.countries.TW, code: 'TW'},
  {name: I18n.getTranslation().common.settings.countries.TJ, code: 'TJ'},
  {name: I18n.getTranslation().common.settings.countries.TZ, code: 'TZ'},
  {name: I18n.getTranslation().common.settings.countries.TH, code: 'TH'},
  {name: I18n.getTranslation().common.settings.countries.TL, code: 'TL'},
  {name: I18n.getTranslation().common.settings.countries.TG, code: 'TG'},
  {name: I18n.getTranslation().common.settings.countries.TK, code: 'TK'},
  {name: I18n.getTranslation().common.settings.countries.TO, code: 'TO'},
  {name: I18n.getTranslation().common.settings.countries.TT, code: 'TT'},
  {name: I18n.getTranslation().common.settings.countries.TN, code: 'TN'},
  {name: I18n.getTranslation().common.settings.countries.TR, code: 'TR'},
  {name: I18n.getTranslation().common.settings.countries.TM, code: 'TM'},
  {name: I18n.getTranslation().common.settings.countries.TC, code: 'TC'},
  {name: I18n.getTranslation().common.settings.countries.TV, code: 'TV'},
  {name: I18n.getTranslation().common.settings.countries.UG, code: 'UG'},
  {name: I18n.getTranslation().common.settings.countries.UA, code: 'UA'},
  {name: I18n.getTranslation().common.settings.countries.AE, code: 'AE'},
  {name: I18n.getTranslation().common.settings.countries.GB, code: 'GB'},
  {name: I18n.getTranslation().common.settings.countries.US, code: 'US'},
  {name: I18n.getTranslation().common.settings.countries.UM, code: 'UM'},
  {name: I18n.getTranslation().common.settings.countries.UY, code: 'UY'},
  {name: I18n.getTranslation().common.settings.countries.UZ, code: 'UZ'},
  {name: I18n.getTranslation().common.settings.countries.VU, code: 'VU'},
  {name: I18n.getTranslation().common.settings.countries.VE, code: 'VE'},
  {name: I18n.getTranslation().common.settings.countries.VN, code: 'VN'},
  {name: I18n.getTranslation().common.settings.countries.VG, code: 'VG'},
  {name: I18n.getTranslation().common.settings.countries.VI, code: 'VI'},
  {name: I18n.getTranslation().common.settings.countries.WF, code: 'WF'},
  {name: I18n.getTranslation().common.settings.countries.EH, code: 'EH'},
  {name: I18n.getTranslation().common.settings.countries.YE, code: 'YE'},
  {name: I18n.getTranslation().common.settings.countries.ZM, code: 'ZM'},
  {name: I18n.getTranslation().common.settings.countries.ZW, code: 'ZW'},
];

export const convertIdToCategory = (id: number) => {
  for (const category in CategoryIds)
    if (id === CategoryIds[category]) return category;
  return id;
};
