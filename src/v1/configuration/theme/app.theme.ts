import { RGBAColor } from '../../common/domain/entities/types/rgba';

export const Theme = {
    blue           : '#324178',
    supernova      : '#FFCB00',
    purpleHeart    : '#7831B9',
    mountainMeadow : '#17BB8E',
    sunglow        : '#ffcc30',
    tanHide        : '#F78E6A',
    elm            : '#1A757B',
    deepKoamaru    : '#1A137D',
    shockingPink   : '#F81BB7',
    scorpion       : '#696467',
    flamingo       : '#EF4C3B',
    cyan           : '#00CEFF',
    logan          : '#9C9FC0',
    jacksonsPurple : '#101335',
    boulder        : '#636363',
    gainsboro      : '#D8D8D8',
    auroMetalSaurus: '#747585',
    cultured       : '#f6f6f7',
    dodgerBlue     : '#4B82FB',
    lightGrey      : '#E1E1E5',
    ghost          : '#c0c0c7',
    lavender       : '#efeff2',
    magenta        : '#ff00ff',
    gray50         : '#7F7F7F',
    dimGray        : '#6F6F6F',
    jaune          : '#fded3f',
    black          : '#000000'

}
export const ThemeRGBA = {
    blue          : new RGBAColor(50, 65, 120, 1),
    supernova     : new RGBAColor(255, 203, 0, 1),
    purpleHeart   : new RGBAColor(120, 49, 185, 1),
    mountainMeadow: new RGBAColor(23, 187, 14, 12),
    sunglow       : new RGBAColor(255, 204, 48, 1),
    tanHide       : new RGBAColor(247, 142, 106, 1),
    elm           : new RGBAColor(26, 117, 123, 1),
    deepKoamaru   : new RGBAColor(26, 19, 125, 1),
    shockingPink  : new RGBAColor(248, 27, 183, 1),
    scorpion      : new RGBAColor(105, 100, 103, 1),
    flamingo      : new RGBAColor(239, 76, 59, 1),
    cyan          : new RGBAColor(0, 206, 255, 1),
    logan         : new RGBAColor(156, 159, 192, 1),
    jacksonsPurple: new RGBAColor(16, 19, 53, 1),
    boulder       : new RGBAColor(99, 99, 99, 1),
    lavender      : new RGBAColor(239, 239, 242, 1),
    magenta       : new RGBAColor(255, 0, 255, 1),
    jaune         : new RGBAColor(253, 237, 63, 1),
    black         : new RGBAColor(0, 0, 0, 1),
    dimGray       : new RGBAColor(111, 111, 111, 1)

}
