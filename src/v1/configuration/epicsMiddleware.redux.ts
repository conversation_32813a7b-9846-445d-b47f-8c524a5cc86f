import { combineEpics, createEpicMiddleware } from 'redux-observable'
import { accountRootEpics } from '../accountcontext/configuration/accountEpics';
import { ProfileContextEpicsDependencies } from '../accountcontext/profilecontext/configuration/profileContextEpicsDependencies.redux';
import { businessRootEpics } from '../businesscontext/configuration/business.root.epics';
import { businessContextEpicsDependencies } from '../businesscontext/configuration/businessContextEpicsDependencies.redux';
import { applicationContextEpicsDependencies } from '../common/configuration/applicationContextEpicsDependencies.redux';
import { ApplicationContextRootEpics } from '../common/configuration/applicationContextRootEpics';
import { instantContextEpicsDependencies } from '../instantscontext/configuration/instantContextEpicsDependencies.redux'
import { instantContextRootEpics } from '../instantscontext/configuration/instantContextRootEpics.redux'
import { instantManagementRootEpics } from '../instantSubmissioncontext/configuration/instantManagementRootEpics';
import { instantSubmissionContextEpicsDependencies } from '../instantSubmissioncontext/configuration/instantSubmissionEpicsDependencies.redux';
import { signupContextEpicsDependencies } from '../signupcontext/configuration/signupContextEpicsDependencies.redux';
import { signupContextRootEpics } from '../signupcontext/configuration/signupRootEpics';

export const rootEpics = combineEpics(
    instantContextRootEpics,
    signupContextRootEpics,
    ApplicationContextRootEpics,
    accountRootEpics,
    businessRootEpics,
    instantManagementRootEpics

)

export const epicsMiddleware = createEpicMiddleware({
    dependencies: {
        ...instantContextEpicsDependencies,
        ...signupContextEpicsDependencies,
        ...applicationContextEpicsDependencies,
        ...ProfileContextEpicsDependencies,
        ...businessContextEpicsDependencies,
        ...instantSubmissionContextEpicsDependencies
    }
})
