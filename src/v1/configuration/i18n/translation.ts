import { preferencesI18n<PERSON> } from "../../accountcontext/preferencescontext/configuration/en.i18n";
import { preferencesI18nFR } from "../../accountcontext/preferencescontext/configuration/fr.i18n";
import { profileI18nEN } from "../../accountcontext/profilecontext/configuration/en.i18n";
import { profileI18nFR } from "../../accountcontext/profilecontext/configuration/fr.i18n";
import { BusinessI18nEN } from "../../businesscontext/configuration/en.i18n";
import { BusinessI18nFR } from "../../businesscontext/configuration/fr.i18n";
import { Commoni18nEN } from "../../common/configuration/commoni18nEN";
import { Commoni18nFR } from "../../common/configuration/commoni18nFR";
import { Instanti18nEN } from "../../instantscontext/configuration/instanti18nEN";
import { Instanti18nFR } from "../../instantscontext/configuration/instanti18nFR";
import { InstantSubmissionI18nEN } from "../../instantSubmissioncontext/configuration/en.i18n";
import { InstantSubmissionI18nFR } from "../../instantSubmissioncontext/configuration/fr.i18n";
import { navigationI18nEN } from "../../navigationcontext/configuration/en.i18n";
import { navigationI18nFR } from "../../navigationcontext/configuration/fr.i18n";
import { singupI18nEN } from "../../signupcontext/configuration/en.i18n";
import { singupI18nFR } from "../../signupcontext/configuration/fr.i18n";

export const translationI18n = (lang) => {
  return lang === "fr"
    ? {
        app_name: "sportaabe",
        common: Commoni18nFR,
        instant: Instanti18nFR,
        account: {
          signup: singupI18nFR,
          profile: profileI18nFR,
          preferences: preferencesI18nFR,
        },
        instant_submission: InstantSubmissionI18nFR,
        business: BusinessI18nFR,
        navigation: navigationI18nFR,
      }
    : {
        app_name: "sportaabe",
        common: Commoni18nEN,
        instant: Instanti18nEN,
        account: {
          signup: singupI18nEN,
          profile: profileI18nEN,
          preferences: preferencesI18nEN,
        },
        instant_submission: InstantSubmissionI18nEN,
        business: BusinessI18nEN,
        navigation: navigationI18nEN,
      };
};
