import { combineReducers } from 'redux'
import { accountRootReducer } from '../accountcontext/configuration/accoutRootReducer';
import { businessRootReducer } from '../businesscontext/configuration/business.root.reducer';
import { application } from '../common/usecases/application.reducers'
import { instantsRootReducer } from '../instantscontext/usecases/instant.reducers'
import { instantsManagementRootReducer } from '../instantSubmissioncontext/usecases/instantManagerRootReducer';
import { signupRootReducer } from '../signupcontext/usecases/signupRoot.reducers';
import { AppState } from './AppState'

export const reduxReducer = combineReducers<AppState>({
    application,
    instants         : instantsRootReducer,
    signup           : signupRootReducer,
    account          : accountRootReducer,
    business         : businessRootReducer,
    instantManagement: instantsManagementRootReducer
})
