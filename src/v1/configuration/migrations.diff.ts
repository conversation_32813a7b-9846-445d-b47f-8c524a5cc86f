import { MigrationQuery } from '../common/domain/entities/types/AppTypes';

export const migrationQueries: MigrationQuery[] = [
    {
        version    : 157979233600,
        description: 'create table business',
        query      : 'CREATE TABLE IF NOT EXISTS business (id TEXT PRIMARY KEY,name TEXT NOT NULL,type TEXT NULL,' +
        'membership TEXT NULL,address TEXT NOT NULL, zipCode TEXT NOT NULL,longitude FlOAT NULL,latitude FlOAT NULL ,' +
        'city TEXT NOT NULL,country TEXT NOT NULL,coverImage TEXT NULL,profileImage TEXT NULL,description TEXT NULL, website TEXT NULL, ' +
        'email TEXT NULL, phoneNumber TEXT NULL)'
    },
    {
        version    : 157979233700,
        description: 'create table user',
        query      : 'CREATE TABLE IF NOT EXISTS user(phoneNumber text PRIMARY KEY, photoURL text, firstName text,lastName text, email text,' +
        ' birthdate date, gender text, weight text, height text, address text, city text, country text)'
    },
    {
        version    : 157979233800,
        description: 'create table config',
        query      : 'CREATE TABLE IF NOT EXISTS config (token text NOT NULL)'
    },
    {
        version    : 157979233900,
        description: 'create table preference',
        query      : 'CREATE TABLE IF NOT EXISTS preference (key TEXT PRIMARY KEY, value TEXT NOT NULL)'
    },
    {
        version    : 1579792339367,
        description: 'insert the [for you page] categories filter',
        query      : 'INSERT INTO preference (key,value) values ( "foryou_categories", "[]")'
    },
    {
        version    : 1579792344980,
        description: 'insert the [for you page] cities filter',
        query      : 'INSERT INTO preference (key,value) values ( "foryou_cities", "[]")'
    },
    {
        version    : 1579792344981,
        description: 'insert the [for you page] business filter',
        query      : 'INSERT INTO preference (key,value) values ( "foryou_business", "[]")'
    },
    {
        version    : 1579792349952,
        description: 'insert the [for you page] price filter',
        query      : 'INSERT INTO preference (key, value) values ( "foryou_price", "all")'
    },
    {
        version    : 1579792354235,
        description: 'insert the [map page] categories filter',
        query      : 'INSERT INTO preference  (key,value) values ( "map_categories", "[]")'
    },
    {
        version    : 1579792364452,
        description: 'insert the [map you page] categories filter',
        query      : 'INSERT INTO preference (key,value) values ( "map_price", "all")'
    },
    {
        version    : 1579792370227,
        description: 'insert the language categories filter',
        query      : 'INSERT INTO preference (key,value) values ( "language", "any")'
    },
    {
        version    : 1586793518321,
        description: 'insert the [map page] business filter',
        query      : 'INSERT INTO preference (key,value) values ( "map_business", "[]")'
    }
]
