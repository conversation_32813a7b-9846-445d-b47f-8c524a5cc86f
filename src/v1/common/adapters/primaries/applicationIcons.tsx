import React from 'react'
import { ApplicationIconsColor } from './applicationIconsColor'
import { AmericanFootballMarker } from './icons/markers/americanFootball.marker';
import { BadmintonMarker } from './icons/markers/badminton.marker';
import { BaseballMarker } from './icons/markers/baseball.marker';
import { BasketballMarker } from './icons/markers/basketball.marker';
import { BiathlonMarker } from './icons/markers/biathlon.marker';
import { BikingMarker } from './icons/markers/biking.marker';
import { BilliardsMarker } from './icons/markers/billiards.marker';
import { BoxingMarker } from './icons/markers/boxing.marker';
import { CanoeingMarker } from './icons/markers/canoeing.marker';
import { ClimbingMarker } from './icons/markers/climbing.marker';
import { CricketMarker } from './icons/markers/cricket.marker';
import { CrossfitMarker } from './icons/markers/crossfit.marker';
import { CyclingMarker } from './icons/markers/cycling.marker';
import { DanceMarker } from './icons/markers/dance.marker';
import { DefaultMarker } from './icons/markers/default.marker';
import { FencingMarker } from './icons/markers/fencing.marker';
import { FishingMarker } from './icons/markers/fishing.marker';
import { FitnessMarker } from './icons/markers/fitness.marker';
import { FloorballMarker } from './icons/markers/floorball.marker';
import { FootballMarker } from './icons/markers/football.marker';
import { FrisbeeMarker } from './icons/markers/frisbee.marker';
import { GeocoachingMarker } from './icons/markers/geocoaching.marker';
import { GolfMarker } from './icons/markers/golf.marker';
import { GymMarker } from './icons/markers/gym.marker';
import { HandballMarker } from './icons/markers/handball.marker';
import { HokeyMarker } from './icons/markers/hokey.marker';
import { JudoMarker } from './icons/markers/judo.marker';
import { KartingMarker } from './icons/markers/karting.marker';
import { KayakingMarker } from './icons/markers/kayaking.marker';
import { LacrousseMarker } from './icons/markers/lacrousse.marker';
import { PaintballMarker } from './icons/markers/paintball.marker';
import { PetanqueMarker } from './icons/markers/petanque.marker';
import { ProMarker } from './icons/markers/pro.marker';
import { RollerSportMarker } from './icons/markers/rollerSport.marker';
import { RowingMarker } from './icons/markers/rowing.marker';
import { RugbyMarker } from './icons/markers/rugby.marker';
import { RunningMarker } from './icons/markers/running.marker';
import { ShootingMarker } from './icons/markers/shooting.marker';
import { ShopMarker } from './icons/markers/shop.marker';
import { SkateboardingMarker } from './icons/markers/skateboarding.marker';
import { SkatingMarker } from './icons/markers/skating.marker';
import { SkiingMarker } from './icons/markers/skiing.marker';
import { SnowboardingMarker } from './icons/markers/snowboarding.marker';
import { SportFacilityMarker } from './icons/markers/sportFacility.marker';
import { SquashMarker } from './icons/markers/squash.marker';
import { SwimmingMarker } from './icons/markers/swimming.marker';
import { TableTennisMarker } from './icons/markers/tableTennis.marker';
import { TennisMarker } from './icons/markers/tennis.marker';
import { TrekkingMarker } from './icons/markers/trekking.marker';
import { VolleyballMarker } from './icons/markers/volleyball.marker';
import { VolleyballBeachMarker } from './icons/markers/volleyballBeach.marker';
import { WalkingMarker } from './icons/markers/walking.marker';
import { WaterSportMarker } from './icons/markers/waterSport.marker';
import { WrestlingMarker } from './icons/markers/wrestling.marker';
import { YogaMarker } from './icons/markers/yoga.marker';

export const ApplicationIcons = (width: number, iconType: string) => ({
    aikido            : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    american_football : <AmericanFootballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    fishing           : <FishingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    badminton         : <BadmintonMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    baseball          : <BaseballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    basketball        : <BasketballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    biathlon          : <BiathlonMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    billiards         : <BilliardsMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    boxing            : <BoxingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    canoeing          : <CanoeingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    climbing          : <ClimbingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    cricket           : <CricketMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    crossfit          : <CrossfitMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    cycling           : <CyclingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    dance             : <DanceMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    fitness           : <FitnessMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    fencing           : <FencingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    fives             : <FootballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    floorball         : <FloorballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    football          : <FootballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    futsal            : <FootballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    geocaching        : <GeocoachingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    golf              : <GolfMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    mini_golf         : <GolfMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    gymnastics        : <GymMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    handball          : <HandballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    hockey            : <HokeyMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    hiking            : <HokeyMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    holistic_classes  : <YogaMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    ice_skating       : <SkatingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    ju_jitsu          : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    judo              : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    karate            : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    karting           : <KartingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    kayaking          : <KayakingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    kickboxing        : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    krav_maga         : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    kung_fu           : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    lacrosse          : <LacrousseMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    mixed_martial_arts: <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    motor_sport       : <KartingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    mountain_biking   : <BikingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    muay_thai         : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    orienteering      : <TrekkingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    petanque          : <PetanqueMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    paintball         : <PaintballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    roller_sports     : <RollerSportMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    rowing            : <RowingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    rugby             : <RugbyMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    running           : <RunningMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    shooting          : <ShootingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    skateboarding     : <SkateboardingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    skiing            : <SkiingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    snowboarding      : <SnowboardingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    squash            : <SquashMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    swimming          : <SwimmingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    table_tennis      : <TableTennisMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    taekwondo         : <JudoMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    tennis            : <TennisMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    padel_tennis      : <TennisMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    triathlon         : <RunningMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    frisbee           : <FrisbeeMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    volleyball        : <VolleyballMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    beach_volleyball  : <VolleyballBeachMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    walking           : <WalkingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    water_sports      : <WaterSportMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    wrestling         : <WrestlingMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    yoga              : <YogaMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    other             : <DefaultMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    pro               : <ProMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    sports_facility   : <SportFacilityMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    shop              : <ShopMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>,
    default           : <DefaultMarker width={width} fillColor={ApplicationIconsColor()[iconType]}/>
}[iconType])
