import React from 'react';
import {Dimensions, StyleSheet, Text, View} from 'react-native';
import {I18n} from '../../../../configuration/i18n/i18n';
import {Theme} from '../../../../configuration/theme/app.theme';
import {DefaultButton} from '../components/form/fields/defaultButton.field';
import {NoGpsLocationIcon} from '../icons/noGpsLocation.icon';
import {t} from 'src/_helpers';

export const NoPermissionPage = ({openPhoneSettings}) => (
  <View style={styles.containerGlobal}>
    <View style={styles.container}>
      <View style={styles.middle}>
        <NoGpsLocationIcon width={120} />
      </View>
      <View style={styles.bottomText}>
        <Text style={[styles.text, {fontFamily: 'U8 Bold'}]}>
          {t('Attribution des permissions', 'Allow permissions')}
        </Text>
        <Text style={styles.text}>
          {t(
            "Afin de profiter pleinemment de l'application sportaabe, vous devez nous attribuer certaines permission",
            'In other to take full advantage of the sportaabe application, you should allow us some permissions',
          )}
        </Text>
      </View>
      <View style={styles.viewButton}>
        <DefaultButton
          label={t('Attibuer les permissions', 'Give permissions')}
          onPress={openPhoneSettings}
        />
      </View>
    </View>
  </View>
);

const styles = StyleSheet.create({
  containerGlobal: {
    flex: 1,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  middle: {
    height: 181,
    width: 181,
    borderRadius: 181,
    backgroundColor: Theme.lavender,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomText: {
    position: 'relative',
    marginTop: 50,
  },
  text: {
    width: Dimensions.get('screen').width,
    color: 'black',
    textAlign: 'center',
    fontSize: 15,
    fontFamily: 'U8 Regular',
    paddingHorizontal: 15,
    marginVertical: 10,
  },
  viewButton: {
    top: 20,
    bottom: 20,
    width: Dimensions.get('screen').width * 0.8,
  },
  button: {
    width: Dimensions.get('screen').width * 0.8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    paddingVertical: 10,
    backgroundColor: Theme.supernova,
  },
  textBtn: {
    color: Theme.blue,
    fontFamily: 'U8 Regular',
  },
});
