import React from 'react';
import {Dimensions, StyleSheet, Text, View} from 'react-native';
import {I18n} from '../../../../configuration/i18n/i18n';
import {InstantHeader} from '../../../../instantscontext/adapters/primaries/common/instantHeader.presentational';
import {NoNetworkIcon} from '../icons/noNetwork.icon';

export const NoNetworkPage = () => (
  <View style={styles.containerGlobal}>
    <InstantHeader navigation={null} />
    <View style={styles.container}>
      <View style={styles.middle}>
        <NoNetworkIcon width={120} />
      </View>
      <View style={styles.bottomText}>
        <Text style={[styles.text, {fontFamily: 'U8 Bold'}]}>
          {I18n.getTranslation().common.title_no_network}
        </Text>
        <Text style={styles.text}>
          {I18n.getTranslation().common.description_no_network}
        </Text>
      </View>
    </View>
  </View>
);

const styles = StyleSheet.create({
  containerGlobal: {
    flex: 1,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  middle: {
    height: 181,
    width: 181,
    borderRadius: 181,
    backgroundColor: '#EEE',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomText: {
    position: 'relative',
    marginTop: 50,
  },
  text: {
    width: Dimensions.get('screen').width,
    color: 'black',
    textAlign: 'center',
    fontSize: 15,
    fontFamily: 'U8 Regular',
    paddingHorizontal: 15,
    marginVertical: 10,
  },
});
