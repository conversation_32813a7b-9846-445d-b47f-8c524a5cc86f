import React, {PureComponent} from 'react';
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  StyleSheet,
  View,
} from 'react-native';

interface State {
  progressBar: Animated.Value;
}

const screenWidth = Dimensions.get('window').width;

export class LaunchScreen extends PureComponent<any, State> {
  state = {
    progressBar: new Animated.Value(0),
  };

  componentDidMount() {
    Animated.timing(this.state.progressBar, {
      toValue: screenWidth,
      duration: 3500,
      useNativeDriver: false,
      easing: Easing.out(Easing.cubic),
    }).start();
  }

  render() {
    return (
      <View style={styles.container}>
        <Image
          resizeMode="contain"
          fadeDuration={0}
          style={styles.logoImage}
          source={require('../../../../assets/img/sporataabe_baseline_rose-jaune.jpg')}
        />
        <Image
          fadeDuration={0}
          style={styles.mapImage}
          source={require('../../../../assets/img/entrance_sportaabe.png')}
        />
        <Animated.View
          style={[styles.progressBar, {width: this.state.progressBar}]}
        />
      </View>
    );
  }
}

const ratio = 872 / 1029;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    width: screenWidth,
  },
  progressBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: 5,
    backgroundColor: 'white',
  },
  mapImage: {
    width: screenWidth,
    height: screenWidth * ratio,
  },
  welcomeText: {
    color: 'black',
    fontSize: 18,
    fontFamily: 'U8 Bold',
    textAlign: 'center',
    marginTop: 20,
  },
  logoImage: {
    height: 70,
    width: '100%',
    maxWidth: 190,
    marginBottom: 10,
  },
});
