import React from 'react';
import {
  Dimensions,
  Image,
  Linking,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {ifIphoneX} from 'react-native-iphone-x-helper';
import Icon from 'react-native-vector-icons/FontAwesome';
import {I18n} from '../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../configuration/theme/app.theme';
import {PanelView} from '../../components/modal/panelView.modal';

interface Props {
  navigation: any;
}

const screen = Dimensions.get('window');

export class AboutContainer extends React.PureComponent<Props, any> {
  render() {
    return (
      <View style={styles.containerGlobal}>
        <PanelView
          isVisible={true}
          style={styles.topOfScreen}
          onClose={() => this.props.navigation.navigate('more')}>
          <View style={styles.whiteContainer}>
            <View style={styles.header}>
              <View style={styles.iconView} />
              <View style={styles.titleView}>
                <Text style={styles.title}>
                  {I18n.getTranslation().common.about}
                </Text>
              </View>
              <View style={styles.iconView}>
                <TouchableOpacity
                  hitSlop={{top: 15, right: 15, bottom: 15, left: 15}}
                  style={styles.icon}
                  onPress={() => this.props.navigation.navigate('more')}>
                  <Icon name="close" size={30} color="#BBB" />
                </TouchableOpacity>
              </View>
            </View>
            <ScrollView style={styles.content}>
              <View style={styles.contentView}>
                <View style={styles.imageView}>
                  <Image
                    style={styles.image}
                    resizeMode="contain"
                    source={require('../../../../../assets/img/Sportaabe_Com_3.jpg')}
                  />
                </View>
                <Text style={styles.text}>
                  {I18n.getTranslation().common.aboutContent}
                </Text>
                <Text style={styles.text}>
                  {I18n.getTranslation().common.links}
                </Text>
                <TouchableWithoutFeedback
                  onPress={() =>
                    Linking.openURL(
                      'https://sportaabe.com/condition-general-utilisation/',
                    )
                  }>
                  <View>
                    <Text style={[styles.text, styles.link]}>
                      {I18n.getTranslation().common.link_cgu}
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback
                  onPress={() =>
                    Linking.openURL(
                      'https://sportaabe.com/politique-de-confidentialite/',
                    )
                  }>
                  <View>
                    <Text style={[styles.text, styles.link]}>
                      {I18n.getTranslation().common.link_privacy}
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </ScrollView>
          </View>
        </PanelView>
      </View>
    );
  }
}

const ratio = 611 / 1180;
const styles = StyleSheet.create({
  topOfScreen: {
    marginTop: Platform.OS === 'android' ? 0 : ifIphoneX(30, 20),
  },
  containerGlobal: {
    flex: 1,
    backgroundColor: 'white',
  },
  whiteContainer: {
    width: screen.width,
    height: screen.height,
    backgroundColor: 'white',
  },
  header: {
    justifyContent: 'center',
    flexDirection: 'row',
    width: '100%',
  },
  iconView: {
    width: 30,
  },
  icon: {
    width: 30,
  },
  titleView: {
    textAlign: 'center',
    width: screen.width - 90,
  },
  title: {
    fontSize: 18,
    fontFamily: 'U8 Bold',
    color: '#111',
    textAlign: 'center',
    paddingVertical: 10,
  },
  contentView: {
    display: 'flex',
    justifyContent: 'flex-start',
    width: screen.width,
    paddingBottom: 60,
    paddingHorizontal: 20,
  },
  content: {
    width: '100%',
  },
  imageView: {
    borderRadius: 5,
    overflow: 'hidden',
    marginVertical: 10,
  },
  image: {
    width: screen.width - 40,
    height: (screen.width - 40) * ratio,
    borderRadius: 5,
    overflow: 'hidden',
  },
  text: {
    color: '#444',
    textAlign: 'justify',
    fontSize: 16,
    fontFamily: 'U8 Regular',
    marginVertical: 10,
    lineHeight: 23,
    width: '100%',
  },
  textBold: {
    fontFamily: 'U8 Bold',
  },
  link: {
    color: Theme.magenta,
  },
});
