import React, { PureComponent } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome'
import { Theme } from '../../../../configuration/theme/app.theme';

interface TitleProps {
    title: string
    color?: string
    backgroundColor?: string
}

export class HeaderTitle extends PureComponent<TitleProps, any> {
    static defaultProps = {
        color          : 'black',
        backgroundColor: 'white'
    }

    render() {
        return (
            <View style={{ backgroundColor: this.props.backgroundColor, flex: 1, margin: 0 }}>
                <View style={[styles.container, { backgroundColor: this.props.backgroundColor }]}>
                    <View style={styles.titleView}>
                        <Text style={[styles.title, { color: this.props.color }]}>{this.props.title}</Text>
                    </View>
                </View>
            </View>
        )
    }
}

export const HeaderWithLogo = () => (
    <View style={{ backgroundColor: 'white', flex: 1, height: 40, justifyContent: 'center' }}>
        <Image source={require('../../../../assets/logo/sportaabe-logo.png')} style={styles.logo}/>
    </View>
)

interface HeaderLeftProps {
    CTA: () => void
    iconName: string,
    size?: number
    color?: string
    backgroundColor?: string
}

export class HeaderButtonIcon extends PureComponent<HeaderLeftProps, any> {
    static defaultProps = {
        size           : 30,
        color          : 'black',
        backgroundColor: 'white'
    }

    render() {
        return (
            <View style={{ backgroundColor: 'white' }}>
                <View style={[styles.container, { backgroundColor: this.props.backgroundColor }]}>
                    <View
                        style={[styles.control]}>
                        <TouchableOpacity
                            hitSlop={{ top: 10, right: 15, bottom: 10, left: 15 }} activeOpacity={1}
                            onPress={() => this.props.CTA()}>
                            <IconFA name={this.props.iconName} color={this.props.color} size={this.props.size}/>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        )
    }
}

export const HeaderButtonText = ({ CTA, title, color = 'black', backgroundColor = 'white' }) =>
    (
        <View style={{ backgroundColor }}>
            <View style={[styles.container, { backgroundColor }]}>
                <View style={styles.control}>
                    <TouchableOpacity
                        hitSlop={{ top: 10, right: 15, bottom: 10, left: 15 }} activeOpacity={1}
                        onPress={() => CTA()}>
                        <Text style={[styles.publish, { color }]}>{title}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    )

const styles = StyleSheet.create({
    container    : {
        backgroundColor: 'white',
        flex           : 1,
        alignItems     : 'center',
        justifyContent : 'center',
        alignSelf      : 'center',
        height         : 40,
        paddingRight   : 20,
        paddingLeft    : 20
    },
    control      : {
        flex          : 2,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    titleView    : {
        flex          : 6,
        alignItems    : 'center',
        justifyContent: 'center'
    },
    title        : {
        fontSize  : 14,
        fontFamily: 'U8 Bold',
        color     : '#FFFFFF',
        textAlign : 'center'
    },
    logo         : {
        width    : 120,
        height   : 35,
        alignSelf: 'center'
    },
    publish      : {
        color     : Theme.sunglow,
        fontSize  : 15,
        fontFamily: 'U8 Bold'
    }
})
