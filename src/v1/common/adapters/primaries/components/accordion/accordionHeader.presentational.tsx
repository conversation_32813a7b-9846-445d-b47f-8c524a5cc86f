import React, { PureComponent } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient'
import IconIon from 'react-native-vector-icons/Ionicons'
import { Theme } from '../../../../../configuration/theme/app.theme';

interface Props {
    title: string
    subTitle: string
    rank: number
    iconName?: string
    isOpened: boolean
}

export class AccordionHeader extends PureComponent<Props, any> {

    render() {
        const arrowIcon = this.props.isOpened ? <IconIon name={'ios-arrow-up'} color={'#CCC'} size={20}/> :
            <IconIon name={'ios-arrow-forward'} color={'#CCC'} size={20}/>
        const iconLeft = this.props.iconName ?
            <View style={styles.rankView}>
                <IconIon name={this.props.iconName} size={20} color={Theme.logan}/></View> :
            <LinearGradient colors={['#FF00FF', '#FCEE21']} style={styles.yellowRoundView}
                            start={{ x: 0.0, y: 0.1 }} end={{ x: 0.9, y: 1.0 }}>
            <View style={styles.yellowRoundView}>
                <Text style={styles.rank}>{this.props.rank + 1}</Text>
            </View>
            </LinearGradient>

        return (
            <View style={styles.container}>
                <View style={styles.rankView}>
                    {iconLeft}
                </View>
                <View style={styles.titleView}>
                    <Text style={styles.title}>{this.props.title}</Text>
                    <Text style={styles.subTitle}>{this.props.subTitle}</Text>
                </View>
                <View style={styles.controlView}>
                    {arrowIcon}
                </View>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    container      : {
        height           : 60,
        flexDirection    : 'row',
        width            : '100%',
        backgroundColor  : '#FFF',
        borderBottomWidth: 1,
        borderBottomColor: '#CCC'
    },
    rankView       : {
        flex          : 1,
        justifyContent: 'center',
        alignItems    : 'center'
    },
    yellowRoundView: {
        justifyContent : 'center',
        alignItems     : 'center',
        borderRadius   : 13,
        width          : 25,
        height         : 25
    },
    rank           : {
        color: '#FFF'
    },
    titleView      : {
        flex          : 6,
        justifyContent: 'center',
        alignItems    : 'flex-start'
    },
    title          : {
        fontSize  : 16,
        fontFamily: 'U8 Bold'

    },
    subTitle       : {
        fontSize: 14,
        fontFamily: 'U8 Regular',
        color   : '#666'
    },
    controlView    : {
        flex          : 1,
        justifyContent: 'center',
        alignItems    : 'center'
    }

})
