import { Theme } from '../../../../configuration/theme/app.theme';
import { Toast } from './toast/Toast'

export class ApplicationToast {
    static show(msg: string, textColor = 'black', backgroundColor = 'white') {
        Toast.show(msg, { backgroundColor, textColor });
    }

    static warn(msg: string) {
        Toast.show(msg, { backgroundColor: Theme.sunglow, textColor: 'black' });
    }

    static error(msg: string, textColor = 'white', backgroundColor = '#DD3A41') {
        Toast.show(msg, { backgroundColor, textColor });
    }
}
