import React from 'react'
import { Image, Platform, StyleSheet, TouchableOpacity, View } from 'react-native'
import { isIphoneX } from 'react-native-iphone-x-helper';
import IconFA from 'react-native-vector-icons/FontAwesome';
import { Theme } from '../../../../configuration/theme/app.theme'

export const CommonHeader = ({ iconName = 'close', onIconPress = () => false }) =>
    <View style={styles.container}>
        <View style={styles.control}></View>
        <View style={styles.titleView}>
            <Image resizeMode="contain"
                   style={styles.logo}
                   source={require('../../../../assets/logo/sportaabe-logo.png')}/>
        </View>
        <View style={styles.control}>
            <TouchableOpacity hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }} activeOpacity={1}
                              onPress={onIconPress}>
                <IconFA name={iconName} color="black" size={20}/>
            </TouchableOpacity>
        </View>
        <View/>
    </View>

const styles = StyleSheet.create({
    container: {
        paddingTop     : Platform.OS === 'android' ? 0 : isIphoneX() ? 33 : 18,
        paddingBottom  : Platform.OS === 'android' ? 0 : 2,
        flexDirection  : 'row',
        backgroundColor: 'white',
        alignItems     : 'center',
        borderBottomWidth: 1,
        borderStyle: 'solid',
        borderBottomColor: Theme.gainsboro
    },
    control  : {
        flex          : 2,
        maxWidth      : 80,
        justifyContent: 'center',
        alignItems    : 'center',
        flexDirection : 'row',
        alignSelf     : 'center',
        height        : 40
    },
    titleView: {
        flex          : 8,
        alignItems    : 'center',
        alignSelf     : 'center',
        justifyContent: 'center'
    },
    logo     : {
        width: 120,
        height: 35,
        alignSelf: 'center'
    }
})
