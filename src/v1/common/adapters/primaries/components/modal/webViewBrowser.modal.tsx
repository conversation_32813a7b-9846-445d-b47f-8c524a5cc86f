import React from 'react';
import {Modal, StyleSheet, View} from 'react-native';
import {CommonHeader} from '../commonHeader.presentational';
import WebView from 'react-native-webview';

export const WebViewBrowser = ({onCloseModal, visible, urlBrowser}) => (
  <Modal
    animationType="slide"
    visible={visible}
    onRequestClose={() => onCloseModal()}>
    <View style={styles.containerBrowser}>
      <CommonHeader
        iconName={'times-circle'}
        onIconPress={() => onCloseModal()}
      />
      <WebView source={{uri: urlBrowser}} 
      javaScriptCanOpenWindowsAutomatically = {true}
      />
      
    </View>
  </Modal>
);

const styles = StyleSheet.create({
  containerBrowser: {
    flex: 1,
    top: 0,
  },
});
