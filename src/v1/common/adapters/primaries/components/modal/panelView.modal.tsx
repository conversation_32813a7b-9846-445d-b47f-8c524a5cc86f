import React, { PureComponent } from 'react'
import {
    Animated,
    Modal,
    StyleSheet,
    TouchableWithoutFeedback,
    View
} from 'react-native'

interface Props {
    onClose: () => void
    style: any,
    children: any,
    isVisible: boolean
}

export class PanelView extends PureComponent<Props, any> {
    render() {
        return (
            <Modal animationType="slide"
                   transparent={true}
                   visible={this.props.isVisible}
                   onRequestClose={() => this.props.onClose()}>
                <Animated.View style={[styles.modal, this.props.style]}>
                    <View style={styles.handleContainer}>
                        <TouchableWithoutFeedback onPress={() => this.props.onClose()}>
                            <View style={styles.panelHandle}/>
                        </TouchableWithoutFeedback>
                        {this.props.children}
                    </View>
                </Animated.View>
            </Modal>
        )
    }
}

const styles = StyleSheet.create({
    modal          : {
        backgroundColor     : 'transparent',
        borderTopLeftRadius : 20,
        borderTopRightRadius: 20,
        shadowColor         : '#000000',
        shadowOffset        : { width: 0, height: 0 },
        shadowRadius        : 5,
        shadowOpacity       : 0.4
    },
    handleContainer: {
        backgroundColor     : 'white',
        borderTopLeftRadius : 20,
        borderTopRightRadius: 20,
        zIndex              : 999999
    },
    panelHandle    : {
        borderRadius   : 4,
        backgroundColor: 'white',
        marginVertical : 10,
        alignSelf      : 'center'
    }
})
