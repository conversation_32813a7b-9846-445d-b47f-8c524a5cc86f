import React, { Component } from 'react';
import { TextStyle } from 'react-native';
import RootSiblings from 'react-native-root-siblings';
import { ToastContainer } from './ToastContainer';

export class Toast extends Component {

    static show(message: string, options: { backgroundColor: string, textColor: string, textStyle?: TextStyle }) {
        return new RootSiblings(<ToastContainer {...{ ...options, message }}/>);
    };

    render() {
        return null;
    }
}
