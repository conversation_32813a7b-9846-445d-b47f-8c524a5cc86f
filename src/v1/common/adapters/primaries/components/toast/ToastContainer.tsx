import React, { Component } from 'react';
import {
    Dimensions,
    StyleSheet,
    Text,
    TextStyle, TouchableOpacity,
    View
} from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';

interface Props {
    message: string,
    backgroundColor: string,
    textColor: string,
    textStyle?: TextStyle,
}

interface State {
    visible: boolean
}

const width = Dimensions.get('window').width

export class ToastContainer extends Component<Props, State> {

    constructor(props) {
        super(props);
        this.state = {
            visible: true
        }
    }

    render() {
        if (this.state.visible)
            return (
                <View style={[ styles.container, { backgroundColor: this.props.backgroundColor } ]}
                      pointerEvents="box-none">
                    <View style={[ styles.viewableArea ]}>
                        <Text style={[ styles.textStyle, { color: this.props.textColor }, this.props.textStyle ]}>
                            {this.props.message}
                        </Text>
                    </View>
                    <TouchableOpacity hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                                      onPress={() => this.setState({ visible: false })}>
                        <IconFA name={'close'} style={styles.icon} color={this.props.textColor} size={15}/>
                    </TouchableOpacity>
                </View>
            )
        return null
    }
}

const styles = StyleSheet.create({
    container   : {
        position     : 'absolute',
        top          : 75,
        left         : 0,
        right        : 0,
        flexDirection: 'row',
        marginLeft   : width * 0.1,
        marginRight  : width * 0.1
    },
    viewableArea: {
        flex           : 9,
        paddingBottom  : 5,
        paddingTop     : 5,
        justifyContent : 'center',
        alignItems     : 'center'
    },
    icon        : {
        width          : 20,
        paddingTop     : 5,
        paddingRight   : 5
    },
    textStyle   : {
        fontSize : 16,
        fontFamily: 'U8 Regular',
        textAlign: 'center'
    }
})
