import React, { PureComponent } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons'
import IconFA from 'react-native-vector-icons/FontAwesome'
import { I18n } from '../../../../../../configuration/i18n/i18n';
import { Theme, ThemeRGBA } from '../../../../../../configuration/theme/app.theme';
import { RGBAColor } from '../../../../../domain/entities/types/rgba';
import { PicturePickerModal } from './modals/picturePicker.modal';

interface Props {
    onPictureTaken: (imageURI: string) => void
    name?: string
    color?: RGBAColor
    error?: boolean
    style?: ViewStyle
    transparent?: boolean
    imageUri?: string

}

interface State {
    isVisible: boolean
    imageUri: string
}

export class PicturePickerContainer extends PureComponent<Props, State> {
    static defaultProps = {
        name : I18n.getTranslation().common.picture,
        color: ThemeRGBA.boulder,
        error: null
    }

    constructor(props) {
        super(props)
        this.state = {
            isVisible: false,
            imageUri : undefined
        }
    }
    componentDidMount() {
        if (this.props.imageUri)
            this.setState({ imageUri: this.props.imageUri })
    }

    render() {
        const imageSnapShot = this.state.imageUri ?
            <Image style={{ width: '100%', height: 58, borderBottomLeftRadius: 5, borderTopLeftRadius: 5 }}
                   source={{ uri: this.state.imageUri }}/> :
            <EvilIcons name="image" size={40} color={this.props.color.generateLowOpacityColor()}/>

        return (
            <View style={[
                styles.container,
                this.getBorderColor(),
                { ...this.props.style }]}>
                <TouchableOpacity
                    style={[styles.subContainer,  this.getBorderColor()]}
                    onPress={() => this.setState({ isVisible: true })}>
                    <View style={[styles.imageIconView, this.getBackgroundColor()]}>
                        {imageSnapShot}
                    </View>
                    <View style={[styles.textView]}>
                        <Text style={[styles.text, { color: this.props.color.generateLowOpacityColor() }]}>
                            {this.props.name}
                        </Text>
                        <IconFA name={'angle-right'} size={30} color={this.props.color.generateLowOpacityColor()}
                                style={styles.icon}/>
                    </View>
                </TouchableOpacity>
                <PicturePickerModal visible={this.state.isVisible}
                                    onCloseRequest={() => this.setState({ isVisible: false })}
                                    onPicturing={imageUri => {
                                        this.setState({ isVisible: false, imageUri })
                                        this.props.onPictureTaken(imageUri)
                                    }}
                />
            </View>
        )
    }

    getBackgroundColor() {
        return this.props.transparent ? { backgroundColor: 'transparent' } : { backgroundColor: '#CCC' }
    }
    getBorderColor() {
        return this.props.error === null ?
            { borderColor: Theme.dimGray } : this.props.error === true ?
                { borderColor: Theme.flamingo } : { borderColor: Theme.mountainMeadow }
    }

}

const styles = StyleSheet.create({
    container    : {
        height: 60,
        width : '100%'
    },
    subContainer : {
        flex         : 1,
        flexDirection: 'row',
        borderRadius : 5,
        borderWidth  : 1
    },
    imageIconView: {
        flex                  : 2,
        justifyContent        : 'center',
        alignItems            : 'center',
        borderTopLeftRadius   : 5,
        borderBottomLeftRadius: 5
    },
    textView     : {
        flex                   : 5,
        justifyContent         : 'space-between',
        alignItems             : 'center',
        flexDirection          : 'row',
        borderTopRightRadius   : 5,
        borderBottomRightRadius: 5,
        paddingHorizontal      : 10
    },
    text         : {
        fontSize: 14,
        fontFamily: 'U8 Regular',
    },
    icon         : {
        padding: 10
    }
})
