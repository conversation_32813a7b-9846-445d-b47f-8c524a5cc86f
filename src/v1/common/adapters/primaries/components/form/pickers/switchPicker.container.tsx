import React, { PureComponent } from 'react'
import { StyleSheet, Switch, Text, View } from 'react-native';
import { Theme } from '../../../../../../configuration/theme/app.theme';

interface Props {
    value: boolean
    title: string
    subTitle?: string
    onChange: (value: boolean) => void
}

export class SwitchPicker extends PureComponent<Props, any> {
    render() {
        const subTitle = this.props.subTitle ? <Text style={styles.subTitle}>{this.props.subTitle}</Text> : null
        return (
            <View style={styles.container}>
                <View style={styles.info}>
                    <Text
                        style={[styles.title, this.props.subTitle ? { fontFamily: 'U8 Bold' } : null]}>{this.props.title}</Text>
                    {subTitle}
                </View>
                <Switch style={styles.switch} value={this.props.value}
                        onValueChange={value => this.props.onChange(value)}/>
            </View>
        )
    }
}

const styles = StyleSheet.create({
    containerGlobal: {
        backgroundColor: '#FFF'
    },
    container      : {
        flexDirection    : 'row',
        justifyContent   : 'center',
        alignItems       : 'center',
        backgroundColor  : '#FFF',
        paddingVertical  : 15,
        borderBottomColor: Theme.gainsboro,
        borderBottomWidth: 1,
        width            : '90%',
        alignSelf        : 'center'
    },
    title          : {
        fontSize: 15,
        fontFamily: 'U8 Regular',
        color   : Theme.auroMetalSaurus
    },
    subTitle       : {
        fontSize: 12,
        fontFamily: 'U8 Regular',
        color   : Theme.auroMetalSaurus,
        overflow: 'hidden'
    },
    info           : {
        flex          : 6,
        justifyContent: 'center'
    },
    switch         : {
        flex: 1
    }
})
