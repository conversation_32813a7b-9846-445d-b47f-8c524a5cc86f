import React, { PureComponent } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';
import { Theme, ThemeRGBA } from '../../../../../../configuration/theme/app.theme';
import { ChoiceType } from '../../../../../domain/entities/types/AppTypes';
import { RGBAColor } from '../../../../../domain/entities/types/rgba';
import { ApplicationSpecification } from '../../../../../usecases/specifications/application.specifications';
import { ApplicationIcons } from '../../../applicationIcons';
import { ChoicePickerModal } from './modals/choiceSelector.modal';

interface Props {
    onChange: (item: ChoiceType) => void
    placeholder: string
    value?: string
    color?: RGBAColor
    validation?: boolean
    options: ChoiceType[]
    animationType?: 'none' | 'fade' | 'slide'
    style?: ViewStyle
    icon?: string
    error?: boolean
}

interface State {
    isComponentVisible: boolean
    selectedItem: ChoiceType
}

export class ChoicePickerContainer extends PureComponent<Props, State> {

    static defaultProps = {
        animationType: 'slide',
        color        : new RGBAColor(0, 0, 0, 1),
        error        : null,
        value        : ''
    }

    constructor(props) {
        super(props)
        this.state = {
            isComponentVisible: false,
            selectedItem      : {
                value   : this.props.value,
                label   : this.getSelectedChoiceItem(this.props.value, this.props.options) ?
                    this.getSelectedChoiceItem(this.props.value, this.props.options).label : this.props.placeholder,
                iconName: this.getSelectedChoiceItem(this.props.value, this.props.options) ?
                    this.getSelectedChoiceItem(this.props.value, this.props.options).iconName : 'default'
            }
        }
    }
    getSelectedChoiceItem(value, options: ChoiceType[]): ChoiceType {
        if (this.hasNestedList(options)) {
            let selectedItem = null
            for (const choice of options)
                if (this.isInTheList(value, choice.items) !== undefined)
                    selectedItem = this.getSelectedItem(value, choice.items)
            return selectedItem
        }
        else if (this.isInTheList(value, options))
            return this.getSelectedItem(value, options)

        return null
    }

    render() {
        const iconView = this.props.icon ? (
            <View style={styles.iconView}>
                {ApplicationIcons(30, this.state.selectedItem.iconName)}
            </View>) : null

        return (
            <View style={[styles.container, this.defineStyle(), this.props.style]}>
                {iconView}
                <TouchableOpacity style={styles.titleView}
                                  onPress={() => this.setState({ isComponentVisible: true })}>
                    <View style={styles.option}>
                        <Text style={[styles.valueText, this.isNotPlaceholderColor()]}>
                            {this.state.selectedItem.label}
                        </Text>
                        <IconFA name={'angle-right'} size={28} color={this.props.color.generateOpacity(0.5)}
                                style={styles.icon}/>
                    </View>
                </TouchableOpacity>
                <ChoicePickerModal
                    isVisible={this.state.isComponentVisible}
                    title={this.props.placeholder}
                    onChange={(item: ChoiceType) => {
                        this.setState({ selectedItem: item, isComponentVisible: false });
                        this.props.onChange(item)
                    }}
                    onClose={() => this.setState({ isComponentVisible: false })}
                    options={ApplicationSpecification.sortByAlphabetize(this.props.options)}/>
            </View>
        )
    }

    isNotPlaceholderColor() {
        return !(this.props.value === null || this.props.value === '') ?
            { color: Theme.black } : { color: this.props.color.generateOpacity(0.5) }
    }

    defineStyle() {
        switch (this.props.error) {
            case null:
                return { borderColor: Theme.gray50 }
            case true:
                return { borderColor: Theme.flamingo }
            case false:
                return { borderColor: Theme.mountainMeadow }
        }
    }
    hasNestedList(options: ChoiceType[]) {
        return options[0].items
    }

    isInTheList(value: string, options: ChoiceType[]) {
        return value && options.find(item => item.value === value)
    }

    getSelectedItem(value: string, options: ChoiceType[]): ChoiceType {
        return options.find(item => item.value === value)
    }
}

const styles = StyleSheet.create({
    container: {
        width        : '100%',
        borderRadius : 5,
        flexDirection: 'row',
        borderWidth  : 1,
        height       : 50,
        borderColor  : Theme.gray50

    },
    iconView : {
        flex          : 1,
        justifyContent: 'center',
        alignItems    : 'flex-start',
        paddingLeft   : 7
    },
    titleView: {
        flex          : 6,
        flexDirection : 'row',
        justifyContent: 'center',
        alignItems    : 'center',
        paddingLeft   : 10
    },
    valueText: {
        textAlign: 'left',
        flex     : 1,
        fontSize : 14,
        fontFamily: 'U8 Regular',
        color: ThemeRGBA.black.generateColor()
    },
    option   : {
        flexDirection : 'row',
        justifyContent: 'center',
        alignItems    : 'center'
    },
    icon     : {
        paddingLeft: 10,
        paddingRight: 10
    }
})
