import React, {PureComponent, ReactElement} from 'react';
import {Image, Modal, Platform, StyleSheet} from 'react-native';
import ImagePicker, {Image as ImageCrop} from 'react-native-image-crop-picker';
import Permissions, {PERMISSIONS} from 'react-native-permissions';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {GlobalSettings} from '../../../../../../../configuration/setting/app.settings';
import {CameraComponent} from './components/camera.component';
import {ToolBoxComponent} from './components/toolBox.component';

interface Props {
  visible: boolean;
  onCloseRequest: () => void;
  onPicturing: (imageURI: string) => void;
}

interface State {
  picture: ReactElement<Image>;
  isToolsList: boolean;
  isCamera: boolean;
  URI: string;
}

export class CustomizedCameraModal extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      picture: null,
      URI: '',
      isToolsList: false,
      isCamera: true,
    };
  }

  render() {
    return (
      <Modal
        visible={this.props.visible}
        transparent={false}
        onRequestClose={() => this.props.onCloseRequest()}>
        {this.state.isCamera ? (
          <CameraComponent
            onTakePicture={imageURI => this.takePicture(imageURI)}
            onChooseGalery={() => this.handleGallery()}
            onClose={() => this.props.onCloseRequest()}
          />
        ) : (
          <ToolBoxComponent
            picture={this.state.picture}
            onClose={() => this.setState({isToolsList: false})}
            onPressCrop={() => this.handleCrop()}
            onPressTools={key =>
              this.setState({
                ...this.state,
                [key]: true,
              })
            }
            onCloseModal={() => {
              this.setState({isToolsList: false, isCamera: true});
              this.props.onCloseRequest();
            }}
            onSavePicture={() => {
              this.setState({isToolsList: false, isCamera: true});
              this.props.onPicturing(this.state.URI);
            }}
          />
        )}
      </Modal>
    );
  }

  takePicture(imageURI) {
    this.setState({
      URI: imageURI,
      picture: (
        <Image
          source={{uri: imageURI}}
          style={styles.image}
          resizeMode={'contain'}
        />
      ),
      isCamera: false,
      isToolsList: true,
    });
  }

  handleGallery() {
    const pickerOptions = {
      cropping: true,
      freeStyleCropEnabled: true,
      // Remove width and height constraints to handle all image ratios
      // width: GlobalSettings.avatarWidth,
      // height: GlobalSettings.avatarHeight,
    };

    const openPicker = () => {
      ImagePicker.openPicker(pickerOptions).then((data: ImageCrop) => {
        this.setState({
          URI: data.path,
          picture: (
            <Image
              source={{uri: data.path}}
              style={styles.image}
              resizeMode={'contain'}
            />
          ),
          isCamera: false,
          isToolsList: true,
        });
      });
    };

    if (Platform.OS === "android") {
      if (Platform.Version >= 33) {
        Permissions.request(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES).then(
          success => {
            if (success === 'granted') openPicker();
            else Permissions.openSettings();
          },
        );
      } else {
        Permissions.request(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE).then(
          success => {
            if (success === 'granted') openPicker();
            else Permissions.openSettings();
          },
        );
      }
    } else {
      Permissions.request(PERMISSIONS.IOS.PHOTO_LIBRARY).then(
        success => {
          if (success === 'granted') openPicker();
          else Permissions.openSettings();
        },
      );
    }
  }

  cropImage() {
    ImagePicker.openCropper({
      path: this.state.URI,
      cropping: true,
      freeStyleCropEnabled: true,
      cropperStatusBarColor: 'black',
      cropperToolbarTitle: I18n.getTranslation().common.crop_rotate,
      mediaType: 'photo',
      // Remove width and height constraints
      // width: GlobalSettings.avatarWidth,
      // height: GlobalSettings.avatarHeight,
    }).then(image => {
      this.setState({
        URI: image.path,
        picture: (
          <Image
            source={{uri: image.path}}
            style={styles.image}
            resizeMode={'contain'}
          />
        ),
      });
    });
  }

  handleCrop = () => {
    if (Platform.OS === 'android')
      Permissions.request(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES).then(
        success => {
          if (success === 'granted') this.cropImage();
          else Permissions.openSettings();
        },
      );
      
    else this.cropImage();
  };
}

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
});
