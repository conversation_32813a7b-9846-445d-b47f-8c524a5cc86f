import React, {PureComponent, ReactElement} from 'react';
import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import IconF from 'react-native-vector-icons/Foundation';
import {I18n} from '../../../../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../../../../configuration/theme/app.theme';
import {
  HeaderButtonIcon,
  HeaderButtonText,
  HeaderTitle,
} from '../../../../../navigation/navigationHeader';

interface Props {
  onClose: () => void;
  picture: ReactElement<Image>;
  onPressTools: (key: string) => void;
  onPressCrop: () => void;
  onCloseModal: () => void;
  onSavePicture: () => void;
}

interface State {
  URI: string;
}

export class ToolBoxComponent extends PureComponent<Props, State> {
  render() {
    return (
      <SafeAreaView style={{flex: 1}}>
        <View style={{flex: 1}}>
          <View style={styles.header}>
            <HeaderButtonIcon
              CTA={() => this.props.onCloseModal()}
              iconName={'angle-left'}
              color={'black'}
            />
            <HeaderTitle title={I18n.getTranslation().common.adjust_photo} />
            <HeaderButtonText
              title={I18n.getTranslation().common.done}
              CTA={() => this.props.onSavePicture()}
              color={Theme.magenta}
              backgroundColor={'white'}
            />
          </View>
          <View style={styles.content}>
            <View style={styles.viewImage}>{this.props.picture}</View>
            <View style={styles.toolsZone}>
              <View style={styles.tools}>
                <TouchableOpacity
                  style={styles.itemFilter}
                  onPress={() => this.props.onPressCrop()}>
                  <View style={styles.imageFilter}>
                    <IconF
                      name={'crop'}
                      color={Theme.auroMetalSaurus}
                      size={40}
                    />
                  </View>
                  <Text style={styles.textFilter}>
                    {I18n.getTranslation().common.crop_rotate}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  header: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'center',
    top: 0,
    right: 0,
    width: '100%',
    zIndex: 2,
    height: 50,
  },
  content: {
    flex: 1,
    marginTop: 50,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  viewImage: {
    flex: 2,
    width: '100%',
    height: '100%',
    backgroundColor: 'gray',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  toolsZone: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  tools: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '90%',
    flex: 4,
  },
  bottomZone: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  button: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textBtn: {
    fontSize: 14,
    fontFamily: 'U8 Regular',
    color: Theme.auroMetalSaurus,
  },
  imageFilter: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 1,
    alignSelf: 'center',
    borderColor: Theme.auroMetalSaurus,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
  },
  itemFilter: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  textFilter: {
    fontSize: 12,
    fontFamily: 'U8 Regular',
    textAlign: 'center',
  },
});
