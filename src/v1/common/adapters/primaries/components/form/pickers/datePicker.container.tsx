import React, {PureComponent} from 'react';
import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import DatePicker from 'react-native-datepicker';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {
  Theme,
  ThemeRGBA,
} from '../../../../../../configuration/theme/app.theme';
import {RGBAColor} from '../../../../../domain/entities/types/rgba';

interface Props {
  value: string | Date;
  mode: 'date' | 'datetime' | 'time';
  placeholder: string;
  onChange: (date: any) => void;
  onBlur?: () => void;
  format?: string;
  label?: string;
  errorMsg?: string;
  error?: boolean;
  color?: RGBAColor;
  style?: ViewStyle;
}

export class DatePickerContainer extends PureComponent<Props, any> {
  static defaultProps = {
    color: ThemeRGBA.black,
    format: 'll',
    error: null,
  };

  render() {
    const label = this.props.label ? (
      <Text style={styles.label}>{this.props.label}</Text>
    ) : null;
    const errorText =
      this.props.error === true && this.props.errorMsg ? (
        <View style={styles.errorView}>
          <Text style={styles.textError}>{this.props.errorMsg}</Text>
        </View>
      ) : null;
    return (
      <View style={[{...this.props.style}, this.getBorderColor()]}>
        <View
          style={[
            this.props.label ? styles.container : styles.containerHeight,
            this.getBorderColor(),
          ]}>
          {label}
          <DatePicker
            style={[this.getInputHeight()]}
            date={this.props.value}
            mode={this.props.mode}
            androidMode={'spinner'}
            placeholder={this.props.placeholder}
            placeholderTextColor={this.props.color.generateLowOpacityColor()}
            format={this.props.format}
            confirmBtnText={I18n.getTranslation().common.validate}
            cancelBtnText={I18n.getTranslation().common.cancel}
            customStyles={this.datePickerStyle()}
            onDateChange={this.props.onChange}
            onCloseModal={this.props.onBlur}
          />
        </View>
        {errorText}
      </View>
    );
  }

  getBorderColor() {
    if (this.props.error === null) return {borderColor: Theme.gray50};
    else if (this.props.error) return {borderColor: Theme.flamingo};
    else return {borderColor: Theme.mountainMeadow};
  }

  getInputHeight() {
    if (this.props.label)
      return {
        width: '100%',
        height: 30,
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
      };
    return {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    };
  }

  datePickerStyle() {
    const dateText = {
      color: this.props.color.generateColor(),
      fontSize: 14,
      fontFamily: 'U8 Regular',
    };
    const dateTextLabel = {
      color: this.props.color.generateColor(),
      fontSize: 14,
      fontFamily: 'U8 Regular',
      paddingTop: 0,
      paddingLeft: 15,
      paddingRight: 15,
      paddingBottom: 15,
    };
    const dateInput = {
      borderWidth: 0,
      paddingLeft: 10,
      justifyContent: 'center',
      alignItems: 'flex-start',
    };
    const dateInputLabel = {
      borderWidth: 0,
      justifyContent: 'center',
      alignItems: 'flex-start',
      fontFamily: 'U8 Regular',
    };

    return {
      dateIcon: {
        opacity: 0,
      },
      dateInput: this.props.label ? dateInputLabel : dateInput,
      dateText: this.props.label ? dateTextLabel : dateText,
      btnTextConfirm: {
        color: Theme.magenta,
      },
      btnTextCancel: {
        color: 'black',
      },
    };
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Theme.gray50,
  },
  containerHeight: {
    width: '100%',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Theme.gray50,
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textError: {
    color: 'red',
    textAlign: 'center',
    fontFamily: 'U8 Regular',
    width: '100%',
  },
  errorView: {
    width: '100%',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  label: {
    fontSize: 13,
    fontFamily: 'U8 Regular',
    color: Theme.dimGray,
    paddingTop: 11,
    paddingLeft: 15,
    paddingRight: 15,
  },
});
