import React, {PureComponent} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {RNCamera} from 'react-native-camera';
import IconFA from 'react-native-vector-icons/Entypo';
import {I18n} from '../../../../../../../../configuration/i18n/i18n';
import {GlobalSettings} from '../../../../../../../../configuration/setting/app.settings';
import {Theme} from '../../../../../../../../configuration/theme/app.theme';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../../navigation/navigationHeader';

interface Props {
  onTakePicture: (imageURI: string) => void;
  onChooseGalery: () => void;
  onClose: () => void;
}

interface State {
  URI: string;
  visibleModal: boolean;
}

export class CameraComponent extends PureComponent<Props, State> {
  camera: any;

  constructor(props) {
    super(props);
    this.state = {
      URI: '',
      visibleModal: false,
    };
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.container}>
          <View style={styles.header}>
            <HeaderButtonIcon
              CTA={() => this.props.onClose()}
              iconName={'angle-left'}
              color={'black'}
            />
            <HeaderTitle title={I18n.getTranslation().common.photo} />
            <View />
          </View>
          <RNCamera
            ref={ref => {
              this.camera = ref;
            }}
            style={styles.preview}
            type={RNCamera.Constants.Type.back}
            captureAudio={false}
            flashMode={RNCamera.Constants.FlashMode.auto}
          />
          <View style={styles.content}>
            <TouchableOpacity
              style={styles.take}
              onPress={() => this.handleTakePhoto()}>
              <IconFA name={'circle'} size={60} color={'gray'} />
            </TouchableOpacity>
            <View style={styles.zoneButton}>
              <TouchableOpacity style={styles.button}>
                <IconFA name={'camera'} size={25} color={Theme.gainsboro} />
                <Text style={styles.txtButton}>
                  {I18n.getTranslation().common.photo}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, {opacity: 0.6}]}
                onPress={() => this.props.onChooseGalery()}>
                <IconFA name={'image'} size={25} color={Theme.gainsboro} />
                <Text style={styles.txtButton}>
                  {I18n.getTranslation().common.library}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  handleTakePhoto = async () => {
    if (this.camera !== null)
      try {
        const options = {
          quality: GlobalSettings.quality,
          width: GlobalSettings.avatarWidth,
          height: GlobalSettings.avatarHeight,
          forceUpOrientation: true,
          orientation: 'portrait',
          fixOrientation: true,
          skipProcessing: true,
        };
        const data = await this.camera.takePictureAsync(options);
        this.props.onTakePicture(data.uri);
      } catch (e) {
        this.props.onClose();
      }
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 3,
    flexDirection: 'column',
    backgroundColor: 'white',
  },
  preview: {
    flex: 3,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  header: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'center',
    top: 0,
    right: 0,
    width: '100%',
    zIndex: 2,
    height: 50,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  take: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 4,
  },
  zoneButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    flex: 1,
  },
  button: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 10,
  },
  txtButton: {
    fontSize: 14,
    paddingLeft: 10,
    fontFamily: 'U8 Bold',
  },
  text: {
    fontSize: 14,
    fontFamily: 'U8 Regular',
    textAlign: 'center',
    color: 'white',
  },
  desciption: {
    width: '96%',
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
});
