import React, {PureComponent} from 'react';
import {
  FlatList,
  Image,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {isIphoneX} from 'react-native-iphone-x-helper';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../../../configuration/theme/app.theme';
import {ChoiceType} from '../../../../../../domain/entities/types/AppTypes';
import {ApplicationIcons} from '../../../../applicationIcons';
import {
  HeaderButtonIcon,
  HeaderButtonText,
  HeaderTitle,
} from '../../../../navigation/navigationHeader';
import {SearchInput} from '../../inputs/search.input';

interface PropsModal {
  title: string;
  onChange: (item: ChoiceType) => void;
  onClose: () => void;
  isVisible: boolean;
  options: ChoiceType[];
  animationType?: 'none' | 'fade' | 'slide';
  headerColor?: string;
}

interface ModalState {
  selectedItem: ChoiceType;
  data: ChoiceType[];
  filterValue: string;
}

export class ChoicePickerModal extends PureComponent<PropsModal, ModalState> {
  static defaultProps = {
    animationType: 'slide',
    headerColor: 'white',
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedItem: null,
      data: this.props.options,
      filterValue: '',
    };
  }

  componentDidUpdate(prevProps) {
    if (this.props.options && prevProps.options !== this.props.options)
      this.setState({data: this.props.options});
  }

  render() {
    return (
      <Modal
        animationType={this.props.animationType}
        visible={this.props.isVisible}
        transparent={false}
        onRequestClose={() => this.props.onClose()}>
        <View style={{flex: 1, backgroundColor: 'white'}}>
          <View
            style={[
              ModalStyles.header,
              {backgroundColor: this.props.headerColor},
            ]}>
            <HeaderButtonIcon
              CTA={() => this.props.onClose()}
              backgroundColor={this.props.headerColor}
              iconName={'angle-left'}
              size={20}
            />
            <HeaderTitle
              title={this.props.title}
              backgroundColor={this.props.headerColor}
              color={'black'}
            />
            <HeaderButtonText
              title={''}
              CTA={() => false}
              backgroundColor={this.props.headerColor}
            />
          </View>
          <View style={ModalStyles.containerModal}>
            <View style={ModalStyles.backdrop}>
              <View style={ModalStyles.scrollViewOption}>
                <FlatList
                  data={this.state.data}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={data => this.renderItem(data.item)}
                  ListHeaderComponent={this.renderHeader()}
                />
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  }

  renderHeader() {
    return (
      <View style={ModalStyles.headerList}>
        <SearchInput
          value={this.state.filterValue}
          placeholder={I18n.getTranslation().common.search}
          onChange={value => {
            this.setState({filterValue: value});
            this.searchValueInList(value);
          }}
        />
      </View>
    );
  }

  renderItem(item: ChoiceType) {
    const itemIcon = !item.iconName ? (
      ApplicationIcons(26, 'other')
    ) : this.isWebsite(item.iconName) ? (
      <Image source={{uri: item.iconName}} style={ModalStyles.icon} />
    ) : (
      ApplicationIcons(26, item.iconName)
    );
    if (item.title)
      return (
        <View style={ModalStyles.viewItem}>
          <Text style={ModalStyles.titleItem}>{item.title}</Text>
          <FlatList
            data={item.items}
            renderItem={data => this.renderItem(data.item)}
          />
        </View>
      );
    else
      return (
        <TouchableOpacity
          style={[ModalStyles.optionItem, this.isSelected(item)]}
          onPress={() => this.props.onChange(item)}>
          {itemIcon}
          <Text style={ModalStyles.optionText}>{item.label}</Text>
        </TouchableOpacity>
      );
  }

  searchValueInList(value: string) {
    if (!this.hasNestedList(this.props.options))
      this.setState({data: this.FiltredList(value.toLowerCase(), this.props.options)});
    else {
      const optionsFiltred: ChoiceType[] = [];
      for (const choice of this.props.options)
        if (this.FiltredList(value.toLowerCase(), choice.items).length > 0)
          optionsFiltred.push({
            title: choice.title,
            items: this.FiltredList(value, choice.items),
            value: choice.value,
            label: choice.label,
          });
      this.setState({data: optionsFiltred});
    }
  }

  FiltredList(value: string, options: ChoiceType[]) {
    return options.filter((item) => item.label.toLowerCase().indexOf(value) !== -1);
  }

  isSelected(item: ChoiceType) {
    return item === this.state.selectedItem ? {backgroundColor: '#CCC'} : null;
  }

  hasNestedList(options: ChoiceType[]) {
    return options[0].items;
  }

  private isWebsite(website: string): boolean {
    const regex =
      /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    return regex.test(website);
  }
}

const ModalStyles = StyleSheet.create({
  header: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'center',
    top: 0,
    right: 0,
    width: '100%',
    zIndex: 2,
    paddingTop: isIphoneX() ? 40 : 0,
    backgroundColor: 'white',
    height: Platform.OS === 'android' ? 40 : isIphoneX() ? 80 : 50,
  },
  backdrop: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerList: {
    width: '96%',
    marginHorizontal: '2%',
    alignSelf: 'center',
  },
  optionText: {
    textAlign: 'left',
    fontFamily: 'U8 Regular',
    flex: 1,
    color: 'black',
    padding: 20,
  },
  scrollViewOption: {
    width: '100%',
    flex: 1,
  },
  optionItem: {
    height: 60,
    flex: 1,
    width: '96%',
    marginHorizontal: '2%',
    borderBottomWidth: 1,
    borderBottomColor: Theme.dimGray,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  containerModal: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Platform.OS === 'android' ? 40 : isIphoneX() ? 80 : 50,
  },
  titleItem: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    width: '100%',
    textAlign: 'left',
    lineHeight: 60,
    paddingLeft: 20,
    backgroundColor: Theme.dimGray,
    color: 'white',
    fontSize: 16,
    fontFamily: 'U8 Bold',
  },
  viewItem: {
    width: '100%',
    borderTopWidth: 1,
    borderTopColor: Theme.dimGray,
    flex: 1,
  },
  icon: {
    width: 26,
    height: 26,
    borderRadius: 5,
    backgroundColor: Theme.ghost,
  },
});
