import React, {PureComponent} from 'react';
import {
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker';
import Permissions, {PERMISSIONS} from 'react-native-permissions';
import Icon from 'react-native-vector-icons/FontAwesome';
import {I18n} from '../../../../../../../configuration/i18n/i18n';
import {GlobalSettings} from '../../../../../../../configuration/setting/app.settings';

interface ModalProps {
  visible: boolean;
  onCloseRequest: () => void;
  onPicturing: (imageURI: string) => void;
}

const PICTURE_OPTIONS = {
  maxWidth: GlobalSettings.avatarWidth,
  maxHeight: GlobalSettings.avatarHeight,
};

export class PicturePickerModal extends PureComponent<ModalProps> {
  render() {
    return (
      <View>
        <Modal
          animationType="slide"
          transparent={true}
          visible={this.props.visible}
          onRequestClose={() => this.props.onCloseRequest()}>
          <View style={modalStyles.container}>
            <TouchableWithoutFeedback
              onPress={() => this.props.onCloseRequest()}>
              <View style={modalStyles.modalOverlay} />
            </TouchableWithoutFeedback>
            <View style={modalStyles.modalContent}>
              <View style={modalStyles.item}>
                <TouchableOpacity onPress={() => this.onCameraPress()}>
                  <View style={modalStyles.itemWithIcon}>
                    <Icon name="camera" size={28} color="black" />
                    <Text style={modalStyles.title}>
                      {I18n.getTranslation().account.profile.camera}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View style={modalStyles.item}>
                <TouchableOpacity onPress={() => this.onGalleryPress()}>
                  <View style={modalStyles.itemWithIcon}>
                    <Icon name="image" size={28} color="black" />
                    <Text style={modalStyles.title}>
                      {I18n.getTranslation().account.profile.library}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  }

  onCameraPress() {
    Permissions.request(PERMISSIONS.ANDROID.CAMERA).then(success => {
      if (success === 'granted')
        ImagePicker.launchCamera(
          {
            mediaType: 'photo',
          },
          imageData => this.props.onPicturing(imageData.assets[0].uri),
        );
      else Permissions.openSettings();
    });
  }

  onGalleryPress() {
    Permissions.request(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES).then(success => {
      if (success === 'granted')
        ImagePicker.launchImageLibrary(
          {
            mediaType: 'photo',
          },
          imageData => this.props.onPicturing(imageData.assets[0].uri),
        );
      else Permissions.openSettings();
    });
  }
}

const modalStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modalContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    flex: 2,
    width: '100%',
    justifyContent: 'flex-end',
    paddingTop: 10,
  },
  modalOverlay: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    top: 0,
    right: 0,
    backgroundColor: '#000',
    opacity: 0.3,
    flex: 6,
  },
  item: {
    width: '100%',
    backgroundColor: '#FFF',
    paddingTop: 20,
    paddingBottom: 10,
    paddingHorizontal: 20,
  },
  itemWithIcon: {
    flexDirection: 'row',
  },
  title: {
    paddingHorizontal: 20,
    fontSize: 20,
    fontFamily: 'U8 Regular',
    color: 'black',
  },
});
