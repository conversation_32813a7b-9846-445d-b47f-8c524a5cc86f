import React, { PureComponent } from 'react';
import { StyleSheet, TextInput, TextStyle, View, ViewStyle } from 'react-native';
import { Theme, ThemeRGBA } from '../../../../../../configuration/theme/app.theme';
import { RGBAColor } from '../../../../../domain/entities/types/rgba';

interface Props {
    placeholder: string
    value?: string
    defaultValue?: string
    style?: ViewStyle
    color?: RGBAColor
    disable?: boolean
    onChange?: (textValue: string) => void
    onBlur?: () => void
    error?: boolean
    numberOfLines?: number
    secureTextEntry?: boolean
    blurOnSubmit?: boolean
    returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send'
    keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
}

interface State {
    error: boolean
}

export class DefaultTextInput extends PureComponent<Props, State> {

    static defaultProps = {
        color          : ThemeRGBA.black,
        disable        : null,
        onBlur         : null,
        error          : null,
        numberOfLines  : 1,
        secureTextEntry: false,
        returnKeyType  : 'done',
        keyboardType   : 'default',
        autoCapitalize : 'sentences',
        blurOnSubmit   : true
    }

    constructor(props) {
        super(props)
        this.state = {
            error: this.props.error
        }
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps && nextProps.error !== this.props.error)
            this.setState({ error: nextProps.error });
    }

    render() {
        return (
            <View
                style={[styles.container, { ...this.props.style }, this.getBgColor(), this.getBorderColor()]}>
                <TextInput style={[styles.inputText,
                    { color: this.props.color.generateOpacity(1) }, this.getLineHeight()]}
                           value={this.props.value}
                           autoCorrect={true}
                           multiline={this.props.numberOfLines > 1}
                           blurOnSubmit={this.props.blurOnSubmit}
                           returnKeyType={this.props.returnKeyType}
                           underlineColorAndroid={'rgba(0, 0, 0, 0)'}
                           onBlur={this.props.onBlur}
                           autoCapitalize={this.props.autoCapitalize}
                           onFocus={() => this.setState({ error: null })}
                           editable={this.props.disable === null}
                           secureTextEntry={this.props.secureTextEntry}
                           keyboardType={this.props.keyboardType}
                           placeholder={this.props.placeholder}
                           placeholderTextColor={this.props.color.generateLowOpacityColor()}
                           onChangeText={this.props.onChange}
                           defaultValue={this.props.defaultValue}
                />
            </View>
        )
    }

    getLineHeight(): TextStyle {
        return { height: 50 + (this.props.numberOfLines - 1) * 10 }
    }

    getBgColor(): ViewStyle {
        return {
            backgroundColor: this.props.disable ? '#EEE' : 'transparent'
        }
    }

    getBorderColor() {
        return this.props.error === null ?
            { borderColor: Theme.dimGray } : this.props.error === true ?
                { borderColor: Theme.flamingo } : { borderColor: Theme.mountainMeadow }
    }
}

const styles = StyleSheet.create({
    container: {
        borderColor   : Theme.logan,
        borderWidth   : 1,
        width         : '100%',
        borderRadius  : 5,
        flexDirection : 'row',
        justifyContent: 'space-between',
        alignItems    : 'center'
    },
    inputText: {
        flex       : 8,
        fontSize   : 14,
        fontFamily: 'U8 Regular',
        paddingLeft: 10,
        lineHeight : 20
    }
})
