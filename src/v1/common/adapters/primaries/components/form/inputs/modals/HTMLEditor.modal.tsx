import { actions, RichTextEditor, RichTextToolbar } from '@arneson/react-native-rich-text-editor'
import React, { PureComponent } from 'react'
import { Dimensions, Modal, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';
import { Theme } from '../../../../../../../configuration/theme/app.theme';

interface Props {
    value: string
    placeholder: string
    onBlur: (value: string) => void
    onClose: () => void
    isVisible: boolean
    animationType?: 'none' | 'fade' | 'slide'
}

export class HTMLEditorModal extends PureComponent<Props, any> {
    static defaultProps = {
        animationType: 'slide'
    }
    richtext: any

    render() {
        return (
            <Modal animationType={this.props.animationType}
                   visible={this.props.isVisible}
                   transparent={false} onRequestClose={() => this.saveContentEditor()}>
                <ModalHeader title={this.props.placeholder} onPressBack={() => this.saveContentEditor()}/>
                <ScrollView>
                    <View style={styles.container}>
                        <RichTextEditor
                            ref={r => this.richtext = r}
                            hiddenTitle={true}
                            style={styles.richText}
                            contentPlaceholder={this.props.placeholder}
                            initialContentHTML={this.props.value}
                            editorInitializedCallback={() => this.onEditorInitialized()}
                        />
                        <RichTextToolbar
                            getEditor={() => this.richtext}
                            style={styles.toolbar}
                            actions={[
                                actions.setBold,
                                actions.setItalic,
                                actions.insertLink
                            ]}
                        />
                    </View>
                </ScrollView>
            </Modal>
        )
    }

    async saveContentEditor() {
        await this.getHTML().then(text => {
            this.props.onBlur(text)
            this.richtext.blurContentEditor()
            this.props.onClose()
        })
    }

    onEditorInitialized() {
        this.getHTML()
        this.richtext.setContentFocusHandler()
    }

    async getHTML() {
        return await this.richtext.getContentHtml()
    }
}

const Screen = Dimensions.get('window')
const styles = StyleSheet.create({
    container: {
        flex           : 1,
        backgroundColor: 'transparent',
        paddingTop     : 40
    },
    richText : {
        marginTop      : 30,
        alignItems     : 'center',
        justifyContent : 'center',
        backgroundColor: 'transparent',
        height         : 200,
        width          : Screen.width
    },
    toolbar  : {
        position: 'absolute',
        width   : '100%'
    }
})

interface ModalInterface {
    title: string
    onPressBack: () => void
}

class ModalHeader extends PureComponent<ModalInterface> {
    render() {
        return (
            <View style={headerStyles.container}>
                <TouchableOpacity style={headerStyles.controlHeader} activeOpacity={1}
                                  hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                                  onPress={() => this.props.onPressBack()}>
                    <IconFA name="angle-left" color="white" size={30} style={headerStyles.iconHeader}/>
                </TouchableOpacity>
                <Text style={headerStyles.title}>{this.props.title}</Text>
            </View>
        )
    }
}

const headerStyles = StyleSheet.create({
    container    : {
        flexDirection  : 'row',
        backgroundColor: Theme.blue,
        display        : 'flex',
        alignItems     : 'center',
        height         : 60,
        justifyContent : 'center'
    },
    title        : {
        color     : '#fff',
        fontSize  : 18,
        fontFamily: 'U8 Bold'
    },
    controlHeader: {
        maxWidth  : 50,
        top       : Platform.OS === 'ios' ? 40 : 20,
        opacity   : 0.8,
        left      : 15,
        position  : 'absolute',
        alignItems: 'center'
    },
    iconHeader   : {
        opacity: 0.7
    }
})
