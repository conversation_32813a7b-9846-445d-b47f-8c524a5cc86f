import React, { PureComponent } from 'react';
import { StyleSheet, TextInput, TextStyle, View, ViewStyle } from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome'
import { Theme, ThemeRGBA } from '../../../../../../configuration/theme/app.theme';
import { RGBAColor } from '../../../../../domain/entities/types/rgba';

interface Props {
    placeholder: string
    value: string
    style?: ViewStyle
    color?: RGBAColor
    disable?: boolean
    onChange?: (textValue: string) => void
    onBlur?: () => void
    error?: boolean
    numberOfLines?: number
    secureTextEntry?: boolean
    returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send'
    keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
}

interface State {
    error: boolean
}

export class SearchInput extends PureComponent<Props, State> {

    static defaultProps = {
        color          : ThemeRGBA.black,
        disable        : null,
        onBlur         : null,
        error          : null,
        numberOfLines  : 1,
        secureTextEntry: false,
        returnKeyType  : 'done',
        keyboardType   : 'default',
        autoCapitalize: 'none'
    }

    constructor(props) {
        super(props)
        this.state = {
            error: this.props.error
        }
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps && nextProps.error !== this.props.error)
            this.setState({ error: nextProps.error });
    }

    render() {
        return (
            <View
                style={[styles.container, { ...this.props.style }, this.getBgColor(), this.getBorderColor()]}>
                <IconFA style={styles.icon} name={'search'} color={Theme.dimGray} size={20} />
                <TextInput style={[styles.inputText,
                    { color: this.props.color.generateOpacity(1) }, this.getLineHeight()]}
                           value={this.props.value}
                           autoCorrect={false}
                           multiline={this.props.numberOfLines > 1}
                           blurOnSubmit={true}
                           returnKeyType={this.props.returnKeyType}
                           underlineColorAndroid={'rgba(0, 0, 0, 0)'}
                           onBlur={this.props.onBlur}
                           autoCapitalize={this.props.autoCapitalize}
                           onFocus={() => this.setState({ error: null })}
                           editable={this.props.disable === null}
                           secureTextEntry={this.props.secureTextEntry}
                           keyboardType={this.props.keyboardType}
                           placeholder={this.props.placeholder}
                           placeholderTextColor={'#343a40'}
                           onChangeText={this.props.onChange}/>
            </View>
        )
    }

    getLineHeight(): TextStyle {
        return { height: 45 + (this.props.numberOfLines - 1) * 10 }
    }

    getBgColor(): ViewStyle {
        return {
            backgroundColor: this.props.disable ? '#EEE' : 'transparent'
        }
    }

    getBorderColor() {
        return this.props.error === null ?
            { borderColor: Theme.dimGray } : this.props.error === true ?
                { borderColor: Theme.flamingo } : { borderColor: Theme.mountainMeadow }
    }
}

const styles = StyleSheet.create({
    container: {
        borderColor   : Theme.logan,
        borderWidth   : 1,
        width         : '100%',
        borderRadius  : 5,
        flexDirection : 'row',
        justifyContent: 'space-between',
        alignItems    : 'center',
        paddingLeft   : 5,
        paddingRight  : 10,
        marginTop     : 10
    },
    inputText: {
        flex       : 8,
        fontSize   : 14,
        fontFamily: 'U8 Regular',
        paddingLeft: 10
    },
    icon : {
        marginLeft: 5
    }
})
