import React, { PureComponent } from 'react';
import { StyleSheet, Text, TextInput, TextStyle, View, ViewStyle } from 'react-native';
import { Theme } from '../../../../../../configuration/theme/app.theme';
import { RGBAColor } from '../../../../../domain/entities/types/rgba';

interface Props {
    placeholder: string
    label: string
    value: string
    style?: ViewStyle
    color?: RGBAColor
    disable?: boolean
    onChange?: (textValue: string) => void
    onBlur?: () => void
    error?: boolean
    numberOfLines?: number
    keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
}

interface State {
    error: boolean
}

export class InputWithLabel extends PureComponent<Props, State> {

    static defaultProps = {
        color          : new RGBAColor(0, 0, 0, 1),
        disable        : null,
        onBlur         : null,
        error          : null,
        numberOfLines  : 1,
        keyboardType   : 'default',
        autoCapitalize: 'sentences'
    }

    constructor(props) {
        super(props)
        this.state = {
            error: this.props.error
        }
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (nextProps && nextProps.error !== this.props.error)
            this.setState({ error: nextProps.error });
    }

    render() {
        return (
            <View style={[styles.container, { ...this.props.style }, this.getBgColor(), this.getBorderColor()]}>
                <Text style={styles.label}>{this.props.label}</Text>
                <TextInput style={[styles.inputText,
                            { color: this.props.color.generateOpacity(1) }, this.getLineHeight()]}
                           value={this.props.value}
                           autoCorrect={true}
                           multiline={this.props.numberOfLines > 1}
                           blurOnSubmit={true}
                           underlineColorAndroid={'rgba(0, 0, 0, 0)'}
                           onBlur={this.props.onBlur}
                           autoCapitalize={this.props.autoCapitalize}
                           onFocus={() => this.setState({ error: null })}
                           editable={this.props.disable === null}
                           keyboardType={this.props.keyboardType}
                           placeholder={this.props.placeholder}
                           placeholderTextColor={'rgba(0, 0, 0, 0.3)'}
                           onChangeText={this.props.onChange}/>
            </View>
        )
    }

    getLineHeight(): TextStyle {
        return { height: 29 + (this.props.numberOfLines - 1) * 10 }
    }

    getBgColor(): ViewStyle {
        return {
            backgroundColor: this.props.disable ? '#EEE' : 'transparent'
        }
    }

    getBorderColor() {
        return this.props.error === true ? { borderColor: Theme.flamingo } :
               this.props.error === false ? { borderColor: Theme.mountainMeadow } : null
    }
}

const styles = StyleSheet.create({
    container: {
        borderColor   : Theme.gray50,
        borderWidth   : 1,
        width         : '100%',
        borderRadius  : 5,
        paddingTop    : 11,
        paddingLeft   : 15,
        paddingRight  : 15
    },
    inputText: {
        fontSize   : 14,
        fontFamily: 'U8 Regular',
        paddingTop: 3,
        paddingRight: 0,
        paddingLeft: 0,
        paddingBottom : 10
    },
    label: {
        fontSize   : 13,
        fontFamily: 'U8 Regular',
        color: Theme.dimGray
    }
})
