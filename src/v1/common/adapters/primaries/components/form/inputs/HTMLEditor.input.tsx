import React, { PureComponent } from 'react'
import {
    StyleSheet,
    TouchableWithoutFeedback,
    View, ViewStyle
} from 'react-native';
import HTMLView from 'react-native-htmlview'
import { Theme } from '../../../../../../configuration/theme/app.theme';
import { HTMLEditorModal } from './modals/HTMLEditor.modal';

interface Props {
    value: string
    placeholder: string
    style?: ViewStyle
    onBlur: (value: string) => void
}
interface State {
    isComponentVisible: boolean
}
export class HTMLEditorInput  extends PureComponent<Props, State> {

    constructor(props) {
        super(props)
        this.state = {
            isComponentVisible: false
        }
    }

    render() {
        const content = this.props.value === '' ? this.props.placeholder : this.props.value
        return(
            <View style={[styles.container, this.props.style]} >
            <TouchableWithoutFeedback  onPress={() => this.setState({ isComponentVisible: true })}>
               <View style={styles.descriptionZone}>
                <HTMLView
                    addLineBreaks={false}
                    paragraphBreak={null}
                    value={content}
                    stylesheet={htmlViewStyles}/>
               </View>
            </TouchableWithoutFeedback>
                <HTMLEditorModal isVisible={this.state.isComponentVisible}
                                 onClose={() => this.setState({ isComponentVisible: false }) }
                                 value={this.props.value}
                                 placeholder={this.props.placeholder}
                                 onBlur={text => this.props.onBlur(text)}
                            />
            </View>
        )
    }
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        flexDirection: 'column',
        backgroundColor: 'transparent',
        borderColor: Theme.boulder,
        borderWidth: 1,
        marginTop: 10,
        borderRadius: 5,
        height: 100
    },
    descriptionZone: {
        padding: 10
    }
})
const htmlViewStyles = StyleSheet.create({
    div: {
        fontSize    : 16,
        fontFamily: 'U8 Regular',
        color     : Theme.boulder,
        textAlign   : 'justify',
        marginTop   : 5,
        marginBottom: 5
    },
    p: {
        fontSize    : 16,
        fontFamily: 'U8 Regular',
        color     : Theme.boulder,
        textAlign   : 'justify',
        marginTop   : 5,
        marginBottom: 5
    },
    a: {
        fontSize          : 16,
        fontFamily: 'U8 Regular',
        color             : '#444',
        textDecorationLine: 'underline'
    }
})
