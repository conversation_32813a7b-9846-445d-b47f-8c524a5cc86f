import React, {PureComponent} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import PhoneInput from 'react-native-phone-input';
import IconFA from 'react-native-vector-icons/FontAwesome';
import {
  Theme,
  ThemeRGBA,
} from '../../../../../../configuration/theme/app.theme';
import {RGBAColor} from '../../../../../domain/entities/types/rgba';

interface Props {
  placeholder: string;
  value: string;
  style?: ViewStyle;
  color?: RGBAColor;
  disable?: boolean;
  backgroundDisable?: RGBAColor;
  error?: boolean;
  onChange?: (phone: string, isValid: boolean) => void;
}

interface State {
  value: string;
}

export class PhoneInputField extends PureComponent<Props, State> {
  static defaultProps = {
    disable: null,
    onChange: null,
    error: null,
    color: ThemeRGBA.boulder,
    backgroundDisable: new RGBAColor(238, 238, 238, 0.8),
  };

  phoneInput: PhoneInput;

  render() {
    return (
      <View
        style={[
          styles.globalContainer,
          {...this.props.style},
          this.getBgColor(),
          this.getBorderColor(this.props.error),
        ]}>
        <View style={styles.container}>
          <View style={styles.phoneView}>
            <PhoneInput
              ref={ref => (this.phoneInput = ref)}
              initialCountry={'cm'}
              initialValue={this.props.value}
              disabled={!!this.props.disable}
              textStyle={styles.text}
              onChangePhoneNumber={() =>
                this.props.onChange(
                  this.phoneInput.getValue(),
                  this.phoneInput.isValidNumber(),
                )
              }
              onSelectCountry={iso2 => {
                if (this.phoneInput)
                  this.phoneInput.setState({inputValue: ''}, () => {
                    this.props.onChange(undefined, false);
                    this.phoneInput.selectCountry(iso2);
                  });
              }}
              style={styles.textInput}
            />
            <IconFA
              name={'phone'}
              color={Theme.gray50}
              size={25}
              style={styles.phoneIcon}
            />
          </View>
        </View>
      </View>
    );
  }

  getBgColor(): ViewStyle {
    return {
      backgroundColor: this.props.disable
        ? this.props.backgroundDisable.generateColor()
        : 'transparent',
    };
  }

  getIconColor = error =>
    error === null
      ? {color: 'transparent'}
      : error === false
      ? {color: Theme.flamingo}
      : {color: Theme.mountainMeadow};

  getBorderColor = error =>
    error === null
      ? {borderColor: Theme.dimGray}
      : error === false
      ? {borderColor: Theme.flamingo}
      : {borderColor: Theme.mountainMeadow};
}

const styles = StyleSheet.create({
  globalContainer: {
    width: '100%',
    borderColor: Theme.logan,
    borderWidth: 1,
    height: 50,
    alignSelf: 'center',
    borderRadius: 5,
    paddingLeft: 10,
  },
  container: {
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  phoneView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textInput: {
    flex: 9,
  },
  text: {
    paddingLeft: 10,
    fontSize: 16,
    fontFamily: 'U8 Regular',
    height: 50,
  },
  info: {
    borderRadius: 5,
    backgroundColor: '#f0f0f0',
    padding: 10,
    marginTop: 20,
  },
  button: {
    marginTop: 20,
    padding: 10,
  },
  phoneIcon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
