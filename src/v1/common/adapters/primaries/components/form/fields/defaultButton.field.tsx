import React, { PureComponent } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import IconFA from 'react-native-vector-icons/FontAwesome';
import { Theme } from '../../../../../../configuration/theme/app.theme';

interface Props {
    label: string,
    onPress: () => void
    iconFA?: string
    style?: ViewStyle
    color?: string
    bgColor?: string
    sub_description?: string
    gradiant?: string
}

export class DefaultButton extends PureComponent<Props> {

    render() {
        const customBgStyle = this.props.bgColor ? {
            backgroundColor: this.props.bgColor,
            borderColor    : Theme.gainsboro,
            borderWidth    : 1
        } : null
        const subDescription = this.props.sub_description ?
            <Text style={styles.subDescription}>{this.props.sub_description}</Text> : null
        const customStyle = this.props.color ? { color: this.props.color } : null
        const icon = this.props.iconFA ?
            <IconFA name={this.props.iconFA} style={styles.iconButton} color={this.props.color} size={15}/> : null
        if (this.props.bgColor)
            return (
                <View style={[styles.container, this.props.style]}>
                    <TouchableOpacity style={[styles.button, customBgStyle]}
                                      onPress={this.props.onPress}>
                        {icon}
                        <View>
                            <Text
                                style={[styles.textButton, customStyle]}>{this.props.label}</Text>
                            {subDescription}
                        </View>
                    </TouchableOpacity>
                </View>
            )
        else if (this.props.gradiant === 'vert-bleu')
            return (
                <View style={[styles.container, this.props.style]}>
                    <LinearGradient colors={['#39b54a', '#0000ff']} style={styles.gradient}
                                    start={{ x: 0.0, y: 0.1 }} end={{ x: 0.9, y: 1.0 }}>
                        <TouchableOpacity style={[styles.button, customBgStyle]}
                                          onPress={this.props.onPress}>
                            {icon}
                            <Text style={[styles.textButton, customStyle]}>{this.props.label}</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                </View>
            )
        else
            return (
                <View style={[styles.container, this.props.style]}>
                    <LinearGradient colors={['#FF00FF', '#FCEE21']} style={styles.gradient}
                                    start={{ x: 0.0, y: 0.1 }} end={{ x: 0.9, y: 1.0 }}>
                        <TouchableOpacity style={[styles.button, customBgStyle]}
                                          onPress={this.props.onPress}>
                            {icon}
                            <Text style={[styles.textButton, customStyle]}>{this.props.label}</Text>
                        </TouchableOpacity>
                    </LinearGradient>
                </View>
            )
    }
}

const styles = StyleSheet.create({
    container     : {
        width: '100%'
    },
    button        : {
        width         : '100%',
        height        : 50,
        justifyContent: 'center',
        alignItems    : 'center',
        flexDirection : 'row',
        borderRadius  : 5
    },
    textButton    : {
        color     : 'white',
        fontSize  : 15,
        textAlign : 'center',
        fontFamily: 'U8 Bold'
    },
    iconButton    : {
        position: 'absolute',
        left    : 17,
        top     : 17
    },
    gradient      : {
        width         : '100%',
        flexDirection : 'row',
        height        : 50,
        justifyContent: 'center',
        alignItems    : 'center',
        borderRadius  : 5
    },
    subDescription: {
        fontSize: 14,
        fontFamily: 'U8 Regular',
        color   : '#333'
    }
})
