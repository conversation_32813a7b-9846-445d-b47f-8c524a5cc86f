import React, {PureComponent} from 'react';
import {StyleSheet, ViewStyle} from 'react-native';
import <PERSON><PERSON>ie<PERSON>, {<PERSON><PERSON>, PROVIDER_GOOGLE} from 'react-native-maps';
import {GlobalSettings} from '../../../../../../configuration/setting/app.settings';
import {Theme} from '../../../../../../configuration/theme/app.theme';
import {InstantSubmissionFormKey} from '../../../../../../instantSubmissioncontext/adapters/primaries/form_common/formTypes';
import {Coordinates} from '../../../../../domain/entities/Coordinates';
import {Platform} from 'react-native';
interface Props {
  position: Coordinates;
  iconType?: string;
  style?: ViewStyle;
  draggableMarker?: boolean;
  raiseUpdates?: (
    key: InstantSubmissionFormKey,
    value: string | Coordinates,
  ) => void;
}

interface State {
  markerPosition: Coordinates;
  currentRegion: Coordinates;
}

export class AddressMap extends PureComponent<Props, State> {
  static defaultProps = {
    iconType: 'default',
  };

  private map: any;
  private isMapReady: boolean = false;

  constructor(props) {
    super(props);
    if( Platform.OS === 'ios' ){
      this.state = {
      markerPosition: null,
      currentRegion: props.position || {},
    };
    }else{
      this.state = {
        markerPosition: null,
        currentRegion: {
          latitude: GlobalSettings.centerClichy.latitude,
          longitude: GlobalSettings.centerClichy.longitude,
        },
      };
    } 
  }

  static getDerivedStateFromProps(props, state) {
    if (
      props.position.latitude !== undefined &&
      props.position !== state.markerPosition
    )
      return {
        markerPosition: props.position,
        currentRegion: props.position,
      };
    return null;
  }

  componentDidUpdate() {
    if (this.isMapReady && this.props.position && this.props.position.latitude)
      this.map.animateToRegion({
        latitude: this.props.position.latitude,
        longitude: this.props.position.longitude,
        latitudeDelta: 0.0012,
        longitudeDelta: 0.0012,
      });
  }

  render() {
    const marker = this.state.markerPosition ? (
      <Marker
        draggable={
          this.props.draggableMarker ? this.props.draggableMarker : false
        }
        coordinate={{
          latitude: this.state.markerPosition.latitude,
          longitude: this.state.markerPosition.longitude,
        }}
        onDragEnd={(value: any) =>
          this.props.raiseUpdates('coordinates', value.nativeEvent.coordinate)
        }
      />
    ) : null;

    return (
      <MapView
        provider={PROVIDER_GOOGLE}
        ref={map => (this.map = map)}
        style={[styles.map, this.props.style]}
        showsUserLocation={false}
        followsUserLocation={false}
        userLocationAnnotationTitle={''}
        showsPointsOfInterest={false}
        showsCompass={false}
        showsBuildings={true}
        pitchEnabled={true}
        rotateEnabled={false}
        scrollEnabled={
          this.props.draggableMarker ? this.props.draggableMarker : false
        }
        moveOnMarkerPress={false}
        toolbarEnabled={false}
        zoomEnabled={true}
        zoomControlEnabled={false}
        loadingEnabled={true}
        onMapReady={() => {
          this.isMapReady = true;
        }}
        region={{
          ...this.state.currentRegion,
          latitudeDelta: 0.0012,
          longitudeDelta: 0.0012,
        }}
        >
        {marker}
      </MapView>
    );
  }
}

const styles = StyleSheet.create({
  map: {
    justifyContent: 'center',
    alignContent: 'center',
    height: 140,
    width: '100%',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: Theme.boulder,
  },
});
