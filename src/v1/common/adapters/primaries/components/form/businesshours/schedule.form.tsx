import React, {PureComponent} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {ActiveDaysNames} from '../../../../../../configuration/setting/app.settings';
import {ThemeRGBA} from '../../../../../../configuration/theme/app.theme';
import {
  ChoiceType,
  DateRangeType,
  ScheduleType,
} from '../../../../../domain/entities/types/AppTypes';
import {DefaultButton} from '../fields/defaultButton.field';
import {ChoicePickerContainer} from '../pickers/choicePicker.container';

import {v4 as uuidv4} from 'uuid';
import {DatePickerContainer} from 'src/_components';
import {differenceInMinutes, format, parse} from 'date-fns';
interface Props {
  style?: ViewStyle;
  show: boolean;
  creation?: boolean;
  onSubmit: (schedule: ScheduleType) => void;
}

interface State {
  date: DateRangeType;
  choiceOption: ChoiceType[];
  errorDate: boolean;
  errorTime: boolean;
}

export class ScheduleForm extends PureComponent<Props & any, any> {
  private defaultState: any;

  constructor(props) {
    super(props);

    const start = new Date();
    start.setHours(8, 0, 0, 0);

    const end = new Date();
    end.setHours(22, 0, 0, 0);

    this.defaultState = {
      date: {
        key: undefined,
        label: undefined,
        startTime: start,
        endTime: end,
      },
      choiceOption: this.generateInstantCategories(),
      errorDate: null,
      errorTime: null,
    };
    this.state = this.defaultState;
  }

  render() {
    if (this.props.show)
      return (
        <View style={[styles.parentContainer, this.props.style]}>
          <ChoicePickerContainer
            onChange={(item: ChoiceType) => this.setChoice(item)}
            error={this.state.errorDate}
            placeholder={I18n.getTranslation().instant_submission.active_days}
            options={this.state.choiceOption}
          />

          {!this.props.creation && (
            <View style={styles.container}>
              <DatePickerContainer
                onChange={startTime => {
                  this.setState({
                    date: {...this.state.date, startTime},
                  });
                }}
                value={this.state.date.startTime}
                error={this.state.errorTime}
                placeholder={I18n.getTranslation().instant_submission.starts}
                mode={'time'}
                format={'HH:mm'}
                style={{width: '40%', borderRadius: 5}}
              />

              <DatePickerContainer
                onChange={endTime => {
                  this.setState({
                    date: {...this.state.date, endTime},
                  });
                }}
                value={this.state.date.endTime}
                error={this.state.errorTime}
                mode={'time'}
                format={'HH:mm'}
                placeholder={I18n.getTranslation().instant_submission.ends}
                style={{width: '40%', borderRadius: 5}}
              />
            </View>
          )}
          {this.props.children}
          <DefaultButton
            onPress={() => this.onSubmit()}
            label={I18n.getTranslation().instant_submission.save_time}
            style={{marginTop: 10}}
          />
        </View>
      );
    else return null;
  }

  setChoice(choice) {
    this.setState({
      date: {
        ...this.state.date,
        key: choice.value,
        label: I18n.getTranslation().instant_submission[choice.value],
      },
      errorDate: null,
    });
  }

  onSubmit() {
    const schedule: ScheduleType = {
      id: uuidv4(),
      dateRange: {
        ...this.state.date,
        startTime: this.state.date.startTime,
        endTime: this.state.date.endTime,
      },
      isPaused: false,
      prePoned: false, // this value will be managed by this.props.children outside this component
    };

    if (this.validateForm(schedule)) {
      this.setState({...this.defaultState});
      this.props.onSubmit(schedule);
    } else if (this.state.date.key === undefined)
      this.setState({errorDate: true});
    else this.setState({errorTime: true});
  }

  validateForm(schedule: ScheduleType) {
    const startTime = new Date(schedule.dateRange.startTime);
    const endTime = new Date(schedule.dateRange.endTime);

    const duration = differenceInMinutes(endTime, startTime);
    return schedule.dateRange.key !== undefined && duration >= 15;
  }

  generateInstantCategories = (): ChoiceType[] => {
    const result = [];
    for (const key in ActiveDaysNames)
      if (ActiveDaysNames.hasOwnProperty(key))
        result.push({
          value: key,
          label: I18n.getTranslation().instant_submission[key],
        });

    return result;
  };
}

const styles = StyleSheet.create({
  parentContainer: {
    height: 170,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
});
