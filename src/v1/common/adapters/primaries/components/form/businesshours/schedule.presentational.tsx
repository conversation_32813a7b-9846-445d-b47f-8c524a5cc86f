import React, {PureComponent} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import IconFA from 'react-native-vector-icons/FontAwesome';
import {I18n} from '../../../../../../configuration/i18n/i18n';
import {Theme} from '../../../../../../configuration/theme/app.theme';
import {ScheduleType} from '../../../../../domain/entities/types/AppTypes';
import {format} from 'date-fns';
import {stringToDate} from 'src/_helpers';

interface Props {
  schedule: ScheduleType;
  style: ViewStyle;
  onPause: () => void;
  onRemove: () => void;
  creation?: boolean;
}

interface State {
  play: boolean;
}

export class SchedulePresentational extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      play: this.props.schedule.isPaused,
    };
  }

  render() {
    const iconColor = this.state.play ? Theme.boulder : Theme.mountainMeadow;
    const textBefore = this.props.schedule.prePoned ? (
      <Text style={styles.label}>
        {I18n.getTranslation().instant_submission.show_two_hours_before}
      </Text>
    ) : null;

    return (
      <View style={[styles.container, this.props.style]}>
        <View style={styles.displayContainer}>
          <Text style={styles.dateRange} testID="scheduleLabel">
            {this.props.schedule.dateRange.label}
          </Text>
          {!this.props.creation && (
            <Text style={styles.timeRange} testID="formatedTimeText">
              {`${format(
                new Date(stringToDate(this.props.schedule.dateRange.startTime)),
                'HH:mm',
              )}-${format(
                new Date(stringToDate(this.props.schedule.dateRange.endTime)),
                'HH:mm',
              )}`}
            </Text>
          )}
          {textBefore}
        </View>
        <View style={styles.controlContainer}>
          {!this.props.creation && (
            <TouchableOpacity
              onPress={() => {
                this.setState({play: !this.state.play});
                this.props.onPause();
              }}>
              <IconFA
                style={{paddingRight: 20}}
                name={'play-circle'}
                size={30}
                color={iconColor}
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={() => this.props.onRemove()}>
            <IconFA name={'remove'} size={30} color={Theme.flamingo} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#EEE',
    paddingTop: 10,
    paddingBottom: 10,
    width: '100%',
  },
  displayContainer: {
    flex: 3,
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    paddingLeft: 10,
  },
  controlContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingRight: 5,
  },
  dateRange: {
    fontSize: 14,
    fontFamily: 'U8 Bold',
  },
  timeRange: {
    fontSize: 12,
    fontFamily: 'U8 Regular',
  },
  label: {
    fontSize: 11,
    fontFamily: 'U8 Regular',
    color: Theme.logan,
  },
});
