import React, { PureComponent } from 'react';
import { Animated, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Theme } from '../../../../../configuration/theme/app.theme';
import { TabulationType } from '../../../../domain/entities/types/AppTypes';

interface Props {
    tabs: TabulationType[]
    currentTab?: number
    onTabPress?: (index: number) => void
}

interface State {
    selectedIndex: number
}

export class TabViewContainer extends PureComponent<Props, State> {

    static defaultProps = {
        currentTab: 0
    }

    constructor(props) {
        super(props)
        this.state = {
            selectedIndex: this.props.currentTab
        }
    }

    render() {
        return (
            <View style={styles.container}>
                <View style={styles.headerContainer}>
                    {this.renderHeader()}
                </View>
                <View style={styles.contentContainer}>
                    {this.renderContent()}
                </View>
            </View>
        )
    }

    renderHeader() {
        return this.props.tabs.map((tab: TabulationType, index: number, array) => {
            return (
                <TouchableOpacity
                    onPress={() => this.setState({ selectedIndex: index }, () => this.props.onTabPress(index))}
                    style={[styles.header, this.getTabHeaderStyle(index, array.length)]}
                    key={index}>
                    <Text key={index} style={[styles.headerText, this.getTextColor(index)]}>
                        {tab.title}
                    </Text>
                </TouchableOpacity>)
        })
    }

    renderContent() {
        return this.props.tabs.map((tab: TabulationType, index: number) => {
            return <Animated.View key={'content' + index} style={ this.state.selectedIndex === index ? 'flex' : 'none'}>
                {this.state.selectedIndex === index ?tab.content: null}
            </Animated.View>
        })
    }

    getTabHeaderStyle(index: number, tabsNumber: number) {
        const singleTabWidth = (100 / tabsNumber).toString() + '%'

        return Object.assign({},
            { width: singleTabWidth },
            this._tabBackground(index),
            this._tabBorder(index, tabsNumber))
    }

    getTextColor(index: number) {
        if (index === this.state.selectedIndex)
            return { color: 'white' }
        else
            return { color: 'black' }
    }

    private _tabBackground(index) {
        if (index === this.state.selectedIndex)
            return Object.assign({}, { backgroundColor: Theme.magenta })
        else
            return Object.assign({}, { backgroundColor: 'white' })
    }

    private _tabBorder(index: number, tabsNumber: number) {
        let style = {}
        if (index === 0)
            style = {
                borderTopLeftRadius   : 5,
                borderBottomLeftRadius: 5
            }
        else if (index === (tabsNumber - 1))
            style = {
                borderTopRightRadius: 5,
                borderBottomRightRadius: 5,
                borderLeftWidth: 0
            }
        if (index === this.state.selectedIndex)
            style = {
                ...style,
                borderColor: Theme.magenta,
                borderWidth: 1
            }
        else
            style = {
                ...style,
                borderColor: '#DDD',
                borderWidth: 1
            }
        return style
    }
}

const styles = StyleSheet.create({
    container       : {
        width     : '100%',
        paddingTop: 10
    },
    headerContainer : {
        justifyContent: 'center',
        alignItems    : 'center',
        flexDirection : 'row',
        width         : '100%'
    },
    contentContainer: {
        justifyContent: 'flex-start',
        alignItems    : 'flex-start',
        width         : '100%',
        paddingTop    : 10
    },
    header          : {
        height        : 60,
        padding       : 5,
        justifyContent: 'center',
        alignItems    : 'center'
    },
    headerText      : {
        fontFamily: 'U8 Bold'
    }
})
