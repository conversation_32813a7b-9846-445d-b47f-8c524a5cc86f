import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from './baseIcon'

export class NoGpsLocationIcon extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 1024
    }

    render() {
        return (
            <Svg width={70} height={70}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={35.5} cy={35} r={35} />
                    <Circle fill="#EEEEEE" stroke="#EEEEEE"  cx={35.5} cy={35.5} r={23} />
                    <Circle
                        cx={20}
                        cy={20}
                        r={15}
                        transform="translate(16 15)"
                        fill="#000"
                        stroke="#000"
                    />
                </G>
            </Svg>
        )
    }
}
