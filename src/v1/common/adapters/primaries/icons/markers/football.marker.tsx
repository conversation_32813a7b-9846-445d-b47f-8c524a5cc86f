import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FootballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-soccer-ball" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fillRule="nonzero">
                            <Path d="M7,0.100961538 C3.19080515,0.100961538 0.100961538,3.19080515 0.100961538,7 C0.100961538,10.8091948 3.19080515,13.8990385 7,13.8990385 C10.8091948,13.8990385 13.8990385,10.8091948 13.8990385,7 C13.8990385,3.19080515 10.8091948,0.100961538 7,0.100961538 Z M5.21634615,1.46394231 L4.93028846,1.98557692 L7,2.87740385 L9.06971154,1.98557692 L8.78365385,1.46394231 C9.97626215,1.84675508 10.9921878,2.60817308 11.7115385,3.60096154 L11.1225962,3.48317308 L10.9206731,5.72115385 L12.4014423,7.42067308 L12.8221154,6.96634615 C12.8221154,6.97686285 12.8221154,6.98948331 12.8221154,7 C12.8221154,8.29356985 12.3951321,9.48197115 11.6778846,10.4495192 L11.6105769,9.84375 L9.42307692,10.3317308 L8.27884615,12.2668269 L8.81730769,12.5192308 C8.24519231,12.7064302 7.63311285,12.8221154 7,12.8221154 C6.36688715,12.8221154 5.75480769,12.7064302 5.18269231,12.5192308 L5.72115385,12.2668269 L4.57692308,10.3317308 L2.38942308,9.84375 L2.32211538,10.4495192 C1.60486792,9.48197115 1.17788462,8.29356985 1.17788462,7 C1.17788462,6.98948331 1.17788462,6.97686285 1.17788462,6.96634615 L1.59855769,7.42067308 L3.07932692,5.72115385 L2.87740385,3.48317308 L2.28846154,3.60096154 C3.00781277,2.60817308 4.02373785,1.84675508 5.21634615,1.46394231 Z M7,4.69471154 L4.57692308,6.46153846 L5.50240385,9.30528846 L8.49759615,9.30528846 L9.42307692,6.46153846 L7,4.69471154 Z"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
