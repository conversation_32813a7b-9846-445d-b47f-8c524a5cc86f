import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class RowingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-rowing-machine-500" transform="translate(5.000000, 6.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M3.85,0 C3.2703125,0 2.8,0.479910714 2.8,1.07142857 C2.8,1.66294643 3.2703125,2.14285714 3.85,2.14285714 C4.4296875,2.14285714 4.9,1.66294643 4.9,1.07142857 C4.9,0.479910714 4.4296875,0 3.85,0 Z M2.93125,2.85714286 C2.5949217,2.84877214 2.275,2.99107143 2.1,3.30357143 C2.1,3.30357143 1.0554691,4.916295 0.809375,5.71428571 C0.6234375,6.31696429 0.5769533,6.90848214 1.4,7.14285714 L4.440625,6.96428571 L7.7,7.85714286 L8.09375,6.76339286 C8.09375,6.76339286 5.1132816,5.80357143 4.9,5.71428571 L2.1,5.71428571 L3.325,5.29017857 L3.98125,4.21875 L5.184375,4.26339286 L7,4.28571429 L7,3.25892857 C7,3.25892857 6.2179691,3.23660714 5.6,3.23660714 C5.6,3.23660714 3.7515625,3.07198643 3.28125,2.92410714 C3.1691408,2.887835 3.0433592,2.85993286 2.93125,2.85714286 Z M12.6,2.85714286 C12.0832033,2.85714286 11.6402342,3.14732143 11.396875,3.57142857 L7.7,3.57142857 L7.7,4.28571429 L11.2,4.28571429 C11.2,4.612165 11.3039066,4.916295 11.484375,5.15625 L10.2375,8.57142857 L4.9,8.57142857 L4.9,7.85714286 L0,7.85714286 L0,9.28571429 L3.5,9.28571429 L3.5,10 L11.2,10 L12.775,5.69196429 C13.4613283,5.59988857 14,5.01116071 14,4.28571429 C14,3.49609357 13.3738283,2.85714286 12.6,2.85714286 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
