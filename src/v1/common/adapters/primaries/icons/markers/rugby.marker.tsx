import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class RugbyMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-rugby-500-6" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M9.93898311,0.0345147825 C9.50674601,0.0452224877 9.05362178,0.0912720435 8.58901118,0.168552773 C6.73056869,0.477675797 4.65813108,1.36633459 2.98757345,3.00219963 L2.98547399,3.00429406 L2.98127495,3.00848269 C1.3402174,4.67613801 0.450235335,6.75871157 0.140665817,8.61922921 C-0.155491692,10.3991402 0.0137570639,11.9884619 0.827198771,12.9210113 C0.878206759,13.0217105 0.959757007,13.1037938 1.06024212,13.1555778 C1.06163874,13.156282 1.06303843,13.1569801 1.06444115,13.157672 C1.99638506,13.9689611 3.58851872,14.1539528 5.37469287,13.8655599 C7.24175723,13.5641063 9.33330682,12.6733724 11.0055232,11.0046863 C12.6491924,9.36504661 13.5384375,7.29670928 13.8482316,5.43582695 C14.1471763,3.64013554 13.9687451,2.02716354 13.1491023,1.07540352 C13.0994,0.985966263 13.0250965,0.912571642 12.9349541,0.863874847 C12.4773968,0.47266803 11.8747239,0.223949066 11.1650849,0.109911152 C10.782567,0.0484413297 10.3712191,0.0238070987 9.93898311,0.0345147825 Z M9.95157924,1.12564527 C10.3235413,1.12472253 10.6690577,1.15359464 10.9782305,1.20525288 C11.3050322,1.25985695 11.5860094,1.34092077 11.8159269,1.43772494 L10.9068471,2.3445758 C10.7664477,2.47905382 10.7098946,2.6787393 10.7589975,2.86662362 C10.8081004,3.05450794 10.9551867,3.20123359 11.1435327,3.25021589 C11.3318787,3.29919819 11.5320547,3.2427835 11.666863,3.10272813 L12.5738424,2.19797169 C12.8543756,2.8672519 12.9992612,3.99083758 12.7879896,5.25990208 C12.5119443,6.91805314 11.7051867,8.79043326 10.2455083,10.2465342 C8.75633139,11.7325662 6.86368758,12.5376144 5.2025348,12.8058223 C3.93741035,13.0100876 2.81932686,12.8580918 2.1645736,12.5817269 L3.06735399,11.6811599 C3.22552322,11.526903 3.27281181,11.2917377 3.18651724,11.0885631 C3.10022268,10.8853885 2.89796661,10.7556936 2.67684902,10.761743 C2.53736488,10.765616 2.40485693,10.8234459 2.30733888,10.9230067 L1.40875742,11.8193863 C1.13729426,11.1670653 0.991090679,10.0561595 1.20090794,8.79515407 C1.47669751,7.13765476 2.2853437,5.2500044 3.7454891,3.7645407 C5.2357719,2.30681043 7.11375709,1.50126538 8.76746775,1.22619626 C9.18121698,1.15737569 9.57961932,1.12661301 9.95157924,1.12564527 Z M8.05574082,3.25352086 C7.8367517,3.25352086 7.6396827,3.38612947 7.55755734,3.58863845 C7.47543197,3.79114744 7.52467096,4.02311017 7.68203176,4.17503205 L8.37696278,4.8682598 L6.98710074,6.25471532 L6.29216972,5.56148756 C6.1909027,5.45707861 6.05149977,5.39812871 5.90586367,5.39812871 C5.68687455,5.39817312 5.48980555,5.53073731 5.40768019,5.7332463 C5.32555482,5.93575528 5.37479381,6.16771801 5.53215461,6.31963989 L6.22708563,7.01286765 L4.83722359,8.39932316 L4.14229257,7.7060954 C4.04102555,7.60168645 3.90162262,7.54273655 3.75598652,7.54273655 C3.5369974,7.54278097 3.3399284,7.67534516 3.25780304,7.87785414 C3.17567767,8.08036313 3.22491666,8.31232585 3.38227746,8.46424773 L4.38163441,9.46115528 C4.42239703,9.52217897 4.47527406,9.5742136 4.53699658,9.61404234 L5.53215461,10.608856 C5.66696304,10.7489112 5.86713915,10.8053257 6.0554851,10.7563432 C6.24383105,10.7073607 6.39091721,10.5606349 6.44001986,10.3727505 C6.48912251,10.1848661 6.43256921,9.98518068 6.29216972,9.85070282 L5.5972387,9.15747549 L6.98710074,7.77101998 L7.68203176,8.46424773 C7.81683868,8.60430935 8.01701781,8.66072878 8.2053677,8.61174722 C8.39371759,8.56276566 8.54080685,8.41603691 8.58990876,8.22814866 C8.63901067,8.04026041 8.58245261,7.84057192 8.44204687,7.7060954 L7.74711585,7.01286765 L9.13697789,5.62641213 L9.83190848,6.31963989 C9.96671675,6.45969527 10.1668928,6.51610995 10.3552388,6.46712765 C10.5435848,6.41814535 10.6906711,6.2714197 10.739774,6.08353538 C10.7888769,5.89565106 10.7323238,5.69596558 10.5919244,5.56148756 L8.44204687,3.41687972 C8.34077985,3.31247077 8.20137692,3.25352086 8.05574082,3.25352086 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
