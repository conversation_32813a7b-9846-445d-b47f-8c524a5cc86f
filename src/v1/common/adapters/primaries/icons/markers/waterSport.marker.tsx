import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class WaterSportMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-water-polo-500" transform="translate(5.000000, 6.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M3.51515152,0 C2.76151515,0 2.15151515,0.560464037 2.15151515,1.25290023 C2.15151515,1.27785132 2.15823888,1.30118024 2.15980115,1.32576858 C1.91898582,1.1308592 1.57528415,1.05660688 1.25189394,1.1637181 C0.779469697,1.32019142 0.535004727,1.79903421 0.705610788,2.23281606 L2.18880209,7.07964752 C2.22728694,7.20577281 2.31358427,7.31474032 2.43146306,7.38852223 L3.96969697,8.35266821 L3.21863152,9.70290738 C2.61566121,9.59839044 2.28564573,9.32989949 2.23733427,9.28744928 C2.17827648,9.22101751 2.08919105,9.18330175 1.995857,9.18521596 C1.99546243,9.18521525 1.99506787,9.18521525 1.9946733,9.18521596 C1.99289603,9.18538288 1.99112039,9.18556423 1.98934661,9.18576 C1.90119686,9.18791444 1.81843982,9.22523785 1.76266573,9.28799304 C1.70265006,9.34068724 1.21667506,9.74477958 0.333333333,9.74477958 C0.224049722,9.74335955 0.122399586,9.79611377 0.0673067511,9.88284163 C0.0122139156,9.96956949 0.0122139156,10.0768342 0.0673067511,10.1635621 C0.122399586,10.2502899 0.224049722,10.3030442 0.333333333,10.3016521 C1.13423797,10.3016521 1.71017212,10.0074251 2,9.8187355 C2.28982785,10.0074251 2.86576212,10.3016521 3.66666667,10.3016521 C4.38409424,10.3016521 4.90843152,10.0682818 5.22088061,9.88344696 L5.43927545,9.87964037 C5.75077879,10.0650148 6.27771758,10.3016521 7,10.3016521 C7.79170818,10.3016521 8.36125091,10.0146707 8.65423758,9.82580492 L8.67850364,9.82580492 C8.97138485,10.0146674 9.54130939,10.3016521 10.3333333,10.3016521 C11.1342379,10.3016521 11.7101721,10.0074251 12,9.8187355 C12.2898279,10.0074251 12.8657621,10.3016521 13.6666667,10.3016521 C13.7759503,10.3030442 13.8776004,10.2502899 13.9326932,10.1635621 C13.9877861,10.0768342 13.9877861,9.96956949 13.9326932,9.88284163 C13.8776004,9.79611377 13.7759503,9.74335955 13.6666667,9.74477958 C12.8016527,9.74477958 12.3217961,9.35953336 12.2450285,9.2934309 C12.1746603,9.20884471 12.0579563,9.1685291 11.9443655,9.18956631 C11.8729254,9.20132666 11.808451,9.23625243 11.7626658,9.28799304 C11.72418,9.32178376 11.5082818,9.49939852 11.1270124,9.62296984 L3.85606061,5.88873995 L2.58652939,2.16647332 C2.83014636,2.37554645 3.15514909,2.50580046 3.51515152,2.50580046 C4.26818182,2.50580046 4.87878788,1.94533643 4.87878788,1.25290023 C4.87878788,0.560464037 4.26818182,0 3.51515152,0 Z M10.1818182,3.61948956 C9.09398458,3.61948956 8.21212121,4.4297399 8.21212121,5.42923434 C8.21212121,6.42872878 9.09398458,7.23897912 10.1818182,7.23897912 C11.2696518,7.23897912 12.1515152,6.42872878 12.1515152,5.42923434 C12.1515152,4.4297399 11.2696518,3.61948956 10.1818182,3.61948956 Z M5.33155788,10.8546621 C5.31346415,10.8549936 5.2954391,10.8568135 5.27769879,10.8601 C5.20625872,10.8718603 5.14178436,10.9067861 5.09599909,10.9585267 C5.03598333,11.0112209 4.55000818,11.4153132 3.66666667,11.4153132 C2.78259727,11.4153132 2.29666782,11.0101181 2.23733427,10.9579829 C2.17827648,10.8915512 2.08919105,10.8538354 1.995857,10.8557496 C1.99546243,10.8557489 1.99506787,10.8557489 1.9946733,10.8557496 C1.99289603,10.8559165 1.99112039,10.8560979 1.98934661,10.8562936 C1.90119686,10.8584481 1.81843982,10.8957715 1.76266573,10.9585267 C1.70265006,11.0112209 1.21667506,11.4153132 0.333333333,11.4153132 C0.224049722,11.4138932 0.122399586,11.4666474 0.0673067511,11.5533753 C0.0122139156,11.6401031 0.0122139156,11.7473679 0.0673067511,11.8340957 C0.122399586,11.9208236 0.224049722,11.9735778 0.333333333,11.9721578 C1.13423797,11.9721578 1.71017212,11.6779587 2,11.4892691 C2.28982785,11.6779587 2.86576212,11.9721578 3.66666667,11.9721578 C4.46757121,11.9721578 5.04350545,11.6779587 5.33333333,11.4892691 C5.62316121,11.6779587 6.19909545,11.9721578 7,11.9721578 C7.80090455,11.9721578 8.37683879,11.6779587 8.66666667,11.4892691 C8.95649455,11.6779587 9.53242879,11.9721578 10.3333333,11.9721578 C11.1342379,11.9721578 11.7101721,11.6779587 12,11.4892691 C12.2898279,11.6779587 12.8657621,11.9721578 13.6666667,11.9721578 C13.7759503,11.9735778 13.8776004,11.9208236 13.9326932,11.8340957 C13.9877861,11.7473679 13.9877861,11.6401031 13.9326932,11.5533753 C13.8776004,11.4666474 13.7759503,11.4138932 13.6666667,11.4153132 C12.8016527,11.4153132 12.3217961,11.030067 12.2450285,10.9639645 C12.1746603,10.8793784 12.0579563,10.8390627 11.9443655,10.8601 C11.8729254,10.8718603 11.808451,10.9067861 11.7626658,10.9585267 C11.70265,11.0112209 11.2166748,11.4153132 10.3333333,11.4153132 C9.46831939,11.4153132 8.98846273,11.030067 8.91169515,10.9639645 C8.84132698,10.8793784 8.72462299,10.8390627 8.61103212,10.8601 C8.53959206,10.8718603 8.47511769,10.9067861 8.42933242,10.9585267 C8.36931667,11.0112209 7.88334152,11.4153132 7,11.4153132 C6.13498606,11.4153132 5.65512939,11.030067 5.57836182,10.9639645 C5.51973718,10.8934928 5.42807781,10.8528995 5.33155788,10.8546621 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
