import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class JudoMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                    <Path id="prefix__b" d="M5 6.4h14v11.2H5z" />
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M9.407 7.864c.17.472.678.786 1.101.786.424 0 .933-.314 1.102-.786.17-.471-.339-.864-1.102-.864-.762 0-1.27.393-1.101.864z" />
                            <Path d="M13.475 8.886c0 .628.593 1.728 1.356 2.435 2.372 2.122.677 2.122-2.712 0L8.814 9.28l-1.526 1.728c-.763.943-1.271 2.357-1.017 3.143.254.707.085 1.571-.424 1.886C4.746 16.664 4.746 18 5.763 18c.423 0 1.101-.707 1.356-1.571.678-2.043 1.78-2.043 2.373 0 .254.864.847 1.571 1.355 1.571 1.017 0 .34-2.2-1.355-4.007-1.017-1.179-.763-2.279.677-2.279.34 0 1.78.943 3.306 2.043 2.542 2.043 2.627 2.043 4.322.707 1.017-.943 1.44-1.964 1.101-2.907-.254-.786 0-1.964.509-2.593.678-.785.762-1.178.17-1.178-.51 0-1.526.785-2.289 1.728l-1.271 1.807-.509-1.807c-.593-1.885-2.033-2.357-2.033-.628z" />
                            <Path d="M12.373 15.329c-.678.628.085 1.885 1.186 1.885.424 0 .763-.55.763-1.178 0-1.179-1.017-1.572-1.95-.707z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
