import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class TableTennisMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-tennis-racquet-500" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M4.50080321,4.4408921e-16 C3.93855422,4.4408921e-16 3.51827309,0.136390887 3.09518072,0.415467626 C3.3748996,0.835831335 3.79799197,1.47022382 3.79799197,2.44804157 C3.79799197,3.42585931 3.44658635,4.0602518 3.09518072,4.48061551 C3.51827309,4.75969225 3.93855422,4.89608313 4.50080321,4.89608313 C5.06305221,4.89608313 5.48333333,4.75969225 5.9064257,4.48061551 C5.55502008,4.13089528 5.20361446,3.49720224 5.20361446,2.44804157 C5.20361446,1.47022382 5.55502008,0.835831335 5.9064257,0.415467626 C5.48333333,0.136390887 5.06305221,4.4408921e-16 4.50080321,4.4408921e-16 Z M10.0572289,0 C9.1435743,0 8.16315261,0.276278977 7.18062249,0.76518785 C7.46034137,1.18555156 7.66345382,1.60381695 7.66345382,2.16406875 C8.43795181,1.67446043 9.28343373,1.3988809 10.0572289,1.3988809 C10.5491968,1.3988809 11.3173695,1.53876898 11.8803213,2.09832134 C12.4425703,2.6578737 12.6569277,3.43705036 12.5170683,4.41556755 C12.3772088,5.46472822 11.8002008,6.49990008 10.9582329,7.40917266 C9.975,8.38838929 8.63825301,9.02697842 7.44417671,9.02697842 C6.95220884,9.02697842 6.18403614,8.88778977 5.62108434,8.32753797 C4.98925703,7.62809752 4.85783133,6.57893685 5.1375502,5.52977618 C4.92670683,5.6011191 4.70672691,5.59552358 4.56686747,5.59552358 C4.28644578,5.59552358 4.01164659,5.5353717 3.73192771,5.46402878 C3.62472816,6.01903742 3.61000681,6.58781688 3.68835341,7.14758193 C3.70732932,8.1309952 3.57028112,9.72641886 2.8752008,10.3608114 L3.57801205,11.0602518 C4.20702811,10.4342526 5.58805221,10.2621902 6.56495984,10.2509992 L6.5874498,10.2509992 C6.88965863,10.3167466 7.21295181,10.3608114 7.53202811,10.3608114 C9.00863454,10.3608114 10.6778112,9.65577538 12.011747,8.32753797 C14.260743,6.09002798 14.6262048,2.86350919 12.8691767,1.11490807 C12.1663655,0.3441247 11.1824297,0 10.0579317,0 L10.0572289,0 Z M2.67771084,0.76518785 C2.32630522,1.25409672 2.04096386,1.81994404 2.04096386,2.44804157 C2.04096386,3.07613909 2.32630522,3.64198641 2.67771084,4.13089528 C2.95813253,3.85251799 3.22730924,3.28667066 3.22730924,2.44804157 C3.29829317,1.60871303 2.95813253,1.04356515 2.67771084,0.76518785 L2.67771084,0.76518785 Z M6.32389558,0.76518785 C6.0434739,1.04356515 5.70331325,1.60941247 5.775,2.44804157 C5.775,3.2873701 6.0434739,3.85251799 6.32319277,4.13089528 C6.67459839,3.64198641 6.96064257,3.07613909 6.96064257,2.44804157 C6.96064257,1.81994404 6.6753012,1.25409672 6.32389558,0.76518785 L6.32389558,0.76518785 Z M2.04096386,10.5573541 L0.217871486,12.5241807 C0.0835698169,12.652984 0.00769606692,12.8306174 0.00769606692,13.016237 C0.00769606692,13.2018566 0.0835698169,13.37949 0.217871486,13.5082934 L0.503212851,13.7705835 C0.783634538,14.0489608 1.18985944,14.0489608 1.47028112,13.7705835 L3.37981928,11.8904876 L2.04096386,10.5573541 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
