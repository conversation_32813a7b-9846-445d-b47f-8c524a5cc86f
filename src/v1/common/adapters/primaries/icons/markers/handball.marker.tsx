import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class HandballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-handball-500" transform="translate(7.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M2.51125402,0 C1.72925058,0 1.09646302,0.57250016 1.09646302,1.28 C1.09646302,1.98750016 1.72925058,2.56 2.51125402,2.56 C3.29325781,2.56 3.92604502,1.98750016 3.92604502,1.28 C3.92604502,0.57250016 3.29325781,0 2.51125402,0 Z M8.34726688,1.28 C7.46302251,1.28 6.75562701,1.92 6.75562701,2.72 C6.75562701,3.52 7.46302251,4.16 8.34726688,4.16 C9.23151125,4.16 9.93890675,3.52 9.93890675,2.72 C9.93890675,1.92 9.23151125,1.28 8.34726688,1.28 Z M0.444332797,1.92987974 C0.0187901929,1.94 -0.163585209,2.36624992 0.322749196,2.85 C0.535520675,3.07375008 1.73063212,4.25375008 2.19071543,4.67 C2.33164206,4.79750016 2.44493569,4.89750016 2.62178457,4.93 L4.27974277,5.18 L1.59385048,8.96 C1.59385048,8.96 0.676446945,10.1425002 0.145900322,11.07 C-0.207797428,11.6775002 0.529994148,12.16 1.2733119,11.52 C1.76793627,11.1037501 4.16921222,8.9 4.16921222,8.9 C4.16921222,8.9 1.19870395,14.4862499 0.985932476,14.87 C0.809083601,15.19 0.458149293,15.7362499 1.12962219,15.96 C1.73063212,16.1837501 2.25703376,15.35 2.25703376,15.35 L8.11515273,6.91 C8.11515273,6.91 9.2080236,7.74 9.41941318,7.9 C9.66672537,8.06 9.5990255,8.18624992 9.56310289,8.41 C9.49263958,8.63375008 9.22460318,9.22375008 9.15413987,9.48 C9.08367656,9.70375008 9.10440113,10.085 9.55204984,10.21 C9.99969855,10.335 10.2193781,9.95375008 10.3257637,9.73 C10.396227,9.57 10.7540697,8.82750016 10.9668408,8.22 C11.0013815,7.99624992 11.0331592,7.71375008 10.8563103,7.49 L8.94413183,5.31 C8.83774627,5.21375008 8.73136071,5.12375008 8.59043408,5.06 C8.02396521,4.80375008 6.71555977,4.16 6.29139871,4 C5.76085209,3.77624992 5.16398714,3.74 5.16398714,3.74 L2.9312701,3.58 C2.9312701,3.58 1.55378325,2.49750016 1.12962219,2.21 C0.875401929,2.01 0.637761254,1.92624992 0.444332797,1.92987974 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
