import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class DanceMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-dancing-500" transform="translate(5.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M4.9030303,0 C4.06623523,0 3.38787879,0.715771783 3.38787879,1.59872102 C3.38787879,2.48167026 4.06623523,3.19744205 4.9030303,3.19744205 C5.73982538,3.19744205 6.41818182,2.48167026 6.41818182,1.59872102 C6.41818182,0.715771783 5.73982538,0 4.9030303,0 Z M6.86818182,3.34772182 C6.75227273,3.34772182 6.6280303,3.36930456 6.51287879,3.39728217 L6.51287879,3.372502 L6.39469697,3.39728217 L4.78484848,3.69704237 L4.71363636,3.69704237 L4.66590909,3.72182254 C4.25997203,3.86503978 3.91044675,4.14614013 3.6719697,4.52118305 L3.62424242,4.57154277 L2.58257576,6.56994404 L2.55984848,6.56994404 L2.55984848,6.59472422 C2.41439394,6.9008793 2.10075758,7.0567546 1.73030303,6.9696243 L0.404545455,6.59472422 L0.00303030303,8.11830536 L1.37575758,8.51798561 L1.39924242,8.54276579 C2.38181818,8.77458034 3.41439394,8.29336531 3.88484848,7.34372502 L3.90909091,7.34372502 C3.91136364,7.3381295 3.90606061,7.32533973 3.90909091,7.3197442 L4.14545455,6.86890488 L4.14545455,9.51638689 L2.93787879,11.4396483 C2.63636364,11.9208633 2.62121212,12.4580336 2.77272727,12.9384492 L4.00378788,15.9872102 L5.58939394,15.9872102 L4.38181818,12.6394884 C4.30530303,12.3996803 4.30227273,12.1494804 4.4530303,11.9904077 L5.42348485,10.441247 L5.66060606,10.3916867 L5.66060606,11.2661871 C5.66060606,11.9216627 5.89772727,12.5371703 6.27651515,13.0391687 L8.09924242,15.4372502 L9.28257576,14.4380496 L7.48333333,12.0655476 L7.45984848,12.039968 C7.23484848,11.7442046 7.17575758,11.5683453 7.17575758,11.2661871 L7.17575758,5.32054357 L7.64924242,5.79536371 L7.67272727,5.79536371 C8.44242424,6.49160671 9.58181818,6.62909672 10.4666667,6.04556355 L10.5136364,5.99520384 L11.4136364,5.27098321 L10.5136364,3.99680256 L9.66136364,4.67146283 C9.34977926,4.87698707 8.94805796,4.84661772 8.66742424,4.59632294 L8.64393939,4.59632294 L7.79090909,3.74660272 C7.78257576,3.73780975 7.77651515,3.72821743 7.76818182,3.7226219 C7.52373346,3.48225479 7.20231915,3.34808237 6.86818182,3.34692246 L6.86818182,3.34772182 Z M11.7212121,7.34452438 L11.7212121,8.04316547 C11.7182046,8.07666362 11.7182046,8.11038674 11.7212121,8.14388489 L11.7212121,10.5915268 C11.5932446,10.5876213 11.4654738,10.6046084 11.3424242,10.6418865 C10.7363636,10.8009592 10.2060606,11.3565148 10.2060606,11.9152678 C10.2060606,12.4748201 10.7363636,12.7993605 11.3424242,12.6402878 C11.8984848,12.4932054 12.375,12 12.455303,11.4908074 C12.4636364,11.4660272 12.4727273,11.4404476 12.4787879,11.4156675 L12.4787879,11.3661071 L12.4787879,8.89288569 C13.7666667,9.23661071 13.9939394,10.3916867 13.9939394,10.3916867 L13.9939394,9.59232614 C13.9939394,8.47482014 13.1590909,7.34372502 11.7212121,7.34372502 L11.7212121,7.34452438 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
