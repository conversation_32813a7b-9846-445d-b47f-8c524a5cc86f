import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class ShootingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="u0BP0nGh01" transform="translate(5.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <G id="Group" transform="translate(6.968182, 8.066667) scale(-1, 1) rotate(-180.000000) translate(-6.968182, -8.066667) translate(0.254545, 0.133333)">
                            <Path d="M3.18181818,15 C2.92727273,14.4 3.05454545,13.6 3.43636364,13.2 C4.39090909,12.2 6.23636364,13.1333333 5.98181818,14.4666667 C5.72727273,16 3.69090909,16.4 3.18181818,15 Z" id="Path"></Path>
                            <Path d="M10.2454545,14.8 C8.27272727,14.6666667 7.50909091,14.2666667 7.06363636,13.1333333 C6.23636364,11.2666667 3.56363636,10.8 2.54545455,12.3333333 C1.84545455,13.3333333 1.59090909,13.2 0.7,10.9333333 C-0.0636363636,9 -0.127272727,8 0.381818182,6.8 C0.7,5.93333333 0.827272727,4.06666667 0.636363636,2.6 C0.381818182,1.06666667 0.445454545,0 0.827272727,0 C1.59090909,0 2.92727273,4.46666667 2.92727273,6.8 C2.92727273,8.8 4.00909091,10 5.79090909,10 C6.61818182,10 7.44545455,10.6 7.82727273,11.4666667 C8.27272727,12.4666667 9.35454545,13.2 11.2636364,13.6666667 C14.5090909,14.5333333 13.9363636,15.1333333 10.2454545,14.8 Z" id="Path"></Path>
                            <Path d="M3.81818182,4.4 C3.30909091,3 4.00909091,0 4.9,0 C5.28181818,0 5.34545455,0.933333333 5.09090909,2.53333333 C4.58181818,5.53333333 4.32727273,5.86666667 3.81818182,4.4 Z" id="Path"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
