import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class PetanqueMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M10.6 4.632c-.92.968-.7 2.231.46 2.8 1.68.842 3.16-1.327 1.84-2.695-.62-.653-1.72-.716-2.3-.105zm1.84.968c.22.484.2.61-.22.947-.78.653-1.7-.126-1.22-1.052.32-.653 1.14-.59 1.44.105zM8.76 7.242c-.58.316-3.12 3.053-3.5 3.79-.42.757-.3 1.894.26 2.757l.52.822-.52 1.81c-.6 2.126-.64 2.59-.2 3.032.34.379 1.38.463 1.58.126.08-.147.24-.147.5 0 .54.316 1.12.253 1.48-.126.44-.464.44-4.421 0-5.958-.16-.61-.28-1.137-.24-1.158.04-.021.58-.4 1.22-.821l1.16-.8.74.652c.68.59.84.632 2.28.632 1.3 0 1.62-.063 1.96-.421.5-.526.5-.863-.02-1.558-.36-.463-.56-.547-1.5-.547-1 0-1.22-.106-2.64-1.137C10.06 7.01 9.5 6.82 8.76 7.242zm2.5 1.684c.96.78 1.1.948.84 1.18-.22.189-.42.147-.98-.317-.72-.568-1.36-.568-.9.022.12.168.2.336.16.357-.04.043-.6.4-1.22.843L8 11.789l-.7-.463c-.38-.273-.7-.526-.7-.59 0-.062.54-.694 1.2-1.43 1.46-1.622 1.84-1.664 3.46-.38zm4.18 1.663c.26.443-.06.569-1.3.569-1.4 0-1.94-.232-1.6-.674.28-.337 2.68-.252 2.9.105zm-7.38 3.074c.46 1.705.48 5.284.04 5.284-.24 0-.3-.336-.3-1.768v-1.747l-1-1.327c-.94-1.242-1.16-1.852-.88-2.61.1-.274.26-.232.96.273.72.527.9.8 1.18 1.895zm-1.14 2.821c-.16 1.516-.36 2.295-.66 2.4-.5.21-.52-.126-.06-1.81.5-1.769.86-2.085.72-.59zM17.34 11.537c-.44.526-.42.947.06 1.41.58.569 1.48.21 1.56-.59.12-1.115-.92-1.62-1.62-.82zm.98.484c.2.358-.26.758-.54.463-.24-.252-.08-.695.22-.695.1 0 .24.106.32.232z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
