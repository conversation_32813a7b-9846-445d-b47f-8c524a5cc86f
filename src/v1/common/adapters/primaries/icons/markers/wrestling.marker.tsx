import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class WrestlingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="lYcfK5cg01" transform="translate(4.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <G id="Group" transform="translate(8.050000, 7.000000) scale(-1, 1) rotate(-180.000000) translate(-8.050000, -7.000000) translate(0.500000, 0.000000)">
                            <Path d="M4.5,12.5 C4.5,11.7 5,11 5.5,11 C6.1,11 6.5,11.7 6.5,12.5 C6.5,13.3 6.1,14 5.5,14 C5,14 4.5,13.3 4.5,12.5 Z" id="Path"></Path>
                            <Path d="M7.5,11.1 C7.5,10.6 8.1,10 8.8,9.7 C9.6,9.4 9.5,8.7 8.1,7.1 C7,6 6.5,4.7 6.9,4.3 C7.3,3.9 8.5,4.6 9.5,6 C11.8,9 12.2,6.9 9.9,3.6 C8.9,2 8.7,1 9.4,0.5 C10,0.2 10.6,0.1 10.6,0.4 C10.7,1 10.9,1.6 11.4,3.8 C11.5,4.2 12,3.5 12.5,2.2 C13.9,-1.1 15.2,-0.5 15,3.5 C14.7,7.3 13,10 10.9,10 C10.1,10 9.5,10.5 9.5,11 C9.5,11.6 9.1,12 8.5,12 C8,12 7.5,11.6 7.5,11.1 Z" id="Path"></Path>
                            <Path d="M1.6,8.8 C0.1,4.7 -0.5,0 0.5,0 C1,0 1.8,1.2 2.2,2.8 L2.8,5.5 L4,2.6 C4.7,1 5.5,0.2 5.8,0.7 C6.1,1.2 5.7,2.9 4.9,4.4 C3.1,8 3.1,8.3 5.5,7.5 C8,6.7 8.1,7.8 5.8,9.6 C3.3,11.5 2.6,11.3 1.6,8.8 Z" id="Path"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
