import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class SkateboardingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-skateboarding-500" transform="translate(5.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M12.2255489,0 C11.2609797,0 10.4790419,0.715771783 10.4790419,1.59872102 C10.4790419,2.48167026 11.2609797,3.19744205 12.2255489,3.19744205 C13.1901181,3.19744205 13.9720559,2.48167026 13.9720559,1.59872102 C13.9720559,0.715771783 13.1901181,0 12.2255489,0 Z M7.61389721,1.84892086 L3.79341317,2.67226219 L4.17502495,4.2470024 L7.20434132,3.5971223 L8.02345309,4.04716227 L5.18537924,5.79536371 C4.53043912,6.23501199 4.43088822,7.10631495 4.99326347,7.64428457 L8.13260978,10.1670663 L7.47766966,12.7897682 L4.01085329,12.7897682 C3.6877495,12.7897682 3.34630739,12.7274181 3.05638723,12.6147082 L1.74650699,11.9904077 L0.764096806,13.0647482 L2.61976048,14.0383693 C3.17397902,14.2508827 3.76574615,14.3695134 4.36626747,14.3884892 L9.60578842,14.3884892 C10.2063097,14.3695134 10.7980769,14.2508827 11.3522954,14.0383693 L12.8534182,13.2390088 L12.2255489,11.9904077 L10.9156687,12.6147082 C10.6257485,12.7266187 10.2843064,12.7897682 9.96032934,12.7897682 L9.2512475,12.7897682 L9.87911677,10.3916867 C9.96476648,9.97507888 9.84454703,9.54513574 9.55077345,9.21742606 L7.99638224,7.46922462 L10.7244261,5.69544365 C11.2256737,5.34852118 11.3488024,4.48681055 10.9156687,4.07194245 C10.5635892,3.74424344 10.1995263,3.42751635 9.8241018,3.12230216 C9.28879741,2.74180655 7.61389721,1.84892086 7.61389721,1.84892086 Z M9.60578842,14.3884892 C9.125499,14.3884892 8.73253493,14.7482014 8.73253493,15.1878497 C8.73253493,15.627498 9.125499,15.9872102 9.60578842,15.9872102 C10.0860778,15.9872102 10.4790419,15.627498 10.4790419,15.1878497 C10.4790419,14.7482014 10.0860778,14.3884892 9.60578842,14.3884892 Z M4.36626747,14.3884892 C3.88597804,14.3884892 3.49301397,14.7482014 3.49301397,15.1878497 C3.49301397,15.627498 3.88597804,15.9872102 4.36626747,15.9872102 C4.84655689,15.9872102 5.23952096,15.627498 5.23952096,15.1878497 C5.23952096,14.7482014 4.84655689,14.3884892 4.36626747,14.3884892 Z M10.806512,6.34452438 L9.41454591,7.29416467 L10.806512,8.8177458 L12.2255489,7.86890488 L10.806512,6.34452438 Z M4.23003992,7.99360512 L3.32971557,9.56754596 L0,9.56754596 L0,11.166267 L3.32971557,11.166267 C3.97198377,11.1648249 4.5604557,10.8376173 4.85790918,10.3165468 L5.56699102,9.117506 L4.23003992,7.99360512 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
