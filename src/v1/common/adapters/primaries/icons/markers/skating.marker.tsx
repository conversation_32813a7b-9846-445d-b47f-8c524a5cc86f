import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class SkatingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M15.256 5.295c.288 1.6 2.383 1.905 2.383.229 0-.61-.578-1.22-1.3-1.372-1.011-.228-1.3.077-1.083 1.143zM7.167 7.124c0 .533 1.01.762 2.527.533 2.962-.38 3.034-.152 1.084 2.057L9.26 11.39l1.806 1.22c1.372.99 1.733 1.752 1.589 3.961-.217 2.439-.073 2.667 1.66 2.59 1.012-.075 1.373-.228.723-.456-.795-.229-1.011-.915-.795-2.667.217-1.676 0-2.667-.938-3.428-1.3-.915-1.3-1.067.216-2.743 2.528-2.82 2.022-3.581-2.383-3.581-2.456 0-3.972.304-3.972.838z" />
                            <Path d="M7.094 15.048C4.711 17.333 4.64 17.562 5.867 18.4c1.589 1.067 2.239 1.067 1.589 0-.217-.457.433-1.752 1.516-2.971C11.717 12.38 10.2 12 7.094 15.048z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
