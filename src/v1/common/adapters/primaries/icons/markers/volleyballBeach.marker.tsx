import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class VolleyballBeachMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="Fi80SlGp01" transform="translate(5.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <G id="Group" transform="translate(7.000000, 7.000000) scale(-1, 1) rotate(-180.000000) translate(-7.000000, -7.000000) translate(0.000000, 0.000000)">
                            <Path d="M8.32222222,12.6823529 C7.62222222,11.8588235 8.47777778,10.7058824 9.8,10.7058824 C10.4222222,10.7058824 10.8888889,11.2823529 10.8888889,11.9411765 C10.8888889,13.1764706 9.25555556,13.6705882 8.32222222,12.6823529 Z" id="Path"></Path>
                            <Path d="M5.6,7.98823529 C4.66666667,6.91764706 3.88888889,5.6 3.88888889,5.10588235 C3.88888889,4.52941176 3.18888889,4.11764706 2.33333333,4.11764706 C1.47777778,4.11764706 0.777777778,3.78823529 0.777777778,3.29411765 C0.777777778,1.97647059 4.66666667,2.30588235 5.13333333,3.70588235 C5.67777778,5.02352941 7,5.43529412 7,4.2 C7,2.38823529 4.51111111,0.823529412 2.25555556,1.23529412 C1.01111111,1.48235294 0,1.31764706 0,0.823529412 C0,0.329411765 2.8,0 7,0 C12.3666667,0 14,0.247058824 14,1.15294118 C14,1.97647059 13.3,2.14117647 10.8888889,1.64705882 C7.46666667,0.988235294 6.61111111,1.4 7.85555556,3.04705882 C8.55555556,3.78823529 8.47777778,4.44705882 7.85555556,5.27058824 C6.45555556,7.08235294 8.24444444,7.82352941 10.4222222,6.34117647 C11.9777778,5.18823529 12.2111111,5.27058824 13.0666667,6.67058824 C14.3111111,8.64705882 14.2333333,9.05882353 12.8333333,9.05882353 C12.2111111,9.05882353 11.6666667,8.64705882 11.6666667,8.15294118 C11.6666667,7.74117647 10.8888889,7.90588235 9.95555556,8.56470588 C7.7,10.2941176 7.62222222,10.2117647 5.6,7.98823529 Z" id="Path"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
