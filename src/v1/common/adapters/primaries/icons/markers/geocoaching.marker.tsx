import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class GeocoachingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M8.178 4.978C6.667 5.867 4 10.844 4 12.8c0 1.867 4.622 6.222 7.2 6.756 4.711 1.066 9.689-4.178 8.267-8.623-.445-1.51-1.067-1.689-4-1.244C12.8 10.133 12 9.956 12 8.978c0-.711-.444-1.6-.978-1.956-.8-.444-.889.09-.444 1.69.444 2.044.178 2.488-1.69 2.932-1.244.267-2.221.89-2.221 1.334 0 1.066 2.4.978 3.11-.09.979-1.51 2.045-.977 2.579 1.334.355 1.245.977 2.222 1.51 2.222.534 0 .8-.533.534-1.155-1.778-4.267-1.778-4.178 1.067-4.178 3.2 0 3.644 1.867 1.333 5.156-2.756 4-11.022.977-11.022-4.178 0-5.333 5.244-8.178 9.422-4.978 2.044 1.511 3.022 1.689 3.022.356C18.222 6.489 12.89 4 10.933 4c-.622 0-1.866.444-2.755.978z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
