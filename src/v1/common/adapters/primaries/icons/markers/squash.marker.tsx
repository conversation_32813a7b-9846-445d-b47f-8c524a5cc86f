import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class SquashMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="V4yUeuKgNd" transform="translate(4.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <G id="Group" transform="translate(7.887719, 8.070175) scale(-1, 1) rotate(-180.000000) translate(-7.887719, -8.070175) translate(0.000000, 0.140351)">
                                <Path d="M11.0315789,15.7473684 C10.1052632,15.5789474 8.14035088,14.1192982 8.14035088,13.6140351 C8.14035088,13.445614 8,13.1649123 7.80350877,12.9684211 C7.41052632,12.5754386 6.45614035,9.65614035 6.45614035,8.75789474 C6.45614035,7.52280702 4.49122807,5.08070175 2.77894737,4.21052632 C1.74035088,3.67719298 0,1.74035088 0,1.12280702 C0,0.673684211 0.729824561,0 1.20701754,0 C1.7122807,0 3.81754386,2.07719298 4.9122807,3.67719298 C5.92280702,5.10877193 7.71929825,6.42807018 8.70175439,6.45614035 C10.6666667,6.5122807 14.1192982,8.2245614 15.2421053,9.74035088 C15.9157895,10.6105263 15.9438596,13.2210526 15.3263158,14.2035088 C14.4561404,15.4947368 12.8561404,16.0842105 11.0315789,15.7473684 Z M11.5087719,14.3438596 C11.5087719,14.0912281 9.65614035,12.0701754 9.43157895,12.0701754 C8.98245614,12.0701754 9.3754386,12.9684211 10.1333333,13.7263158 C10.9754386,14.5964912 11.5087719,14.8210526 11.5087719,14.3438596 Z M13.9789474,13.3894737 C14.0350877,13.1087719 13.8947368,12.9122807 13.6421053,12.9122807 C13.1368421,12.9122807 12.8280702,13.3614035 13.0526316,13.7824561 C13.3052632,14.1754386 13.8666667,13.9508772 13.9789474,13.3894737 Z M12.3508772,12.8 C12.3508772,12.5754386 12.6315789,12.322807 12.9964912,12.2385965 L13.6140351,12.0421053 L12.9684211,11.7894737 C12.6315789,11.6491228 12.2666667,11.3403509 12.1824561,11.0877193 C11.9578947,10.5263158 10.9754386,10.554386 10.7508772,11.1157895 C10.6666667,11.3403509 10.8631579,11.8175439 11.2,12.1824561 C11.5087719,12.5192982 11.7894737,12.9684211 11.7894737,13.1649123 C11.7894737,13.3614035 11.9298246,13.4175439 12.0701754,13.3333333 C12.2385965,13.2491228 12.3508772,12.9964912 12.3508772,12.8 Z M9.90877193,10.8631579 C9.8245614,10.722807 9.62807018,10.7508772 9.4877193,10.8912281 C9.31929825,11.0596491 9.29122807,11.3403509 9.40350877,11.5087719 C9.57192982,11.7614035 9.65614035,11.7614035 9.85263158,11.4807018 C9.96491228,11.2842105 9.99298246,11.0035088 9.90877193,10.8631579 Z M14.0631579,10.554386 C13.8947368,10.1614035 13.5859649,9.8245614 13.3333333,9.8245614 C13.1087719,9.8245614 12.9122807,9.7122807 12.9122807,9.54385965 C12.9122807,9.40350877 12.4070175,9.26315789 11.7894737,9.26315789 C10.9754386,9.26315789 10.6666667,9.3754386 10.6666667,9.68421053 C10.6666667,9.99298246 10.8912281,10.0491228 11.5087719,9.93684211 C12.1824561,9.79649123 12.4912281,9.90877193 13.2210526,10.6385965 C14.2315789,11.6491228 14.4561404,11.6210526 14.0631579,10.554386 Z M9.96491228,8.44912281 C9.15087719,7.60701754 7.66315789,9.03859649 8.42105263,9.96491228 C8.70175439,10.3017544 8.87017544,10.245614 9.54385965,9.6 C10.1894737,8.98245614 10.2736842,8.78596491 9.96491228,8.44912281 Z M7.8877193,7.49473684 C7.52280702,7.12982456 7.15789474,7.41052632 7.43859649,7.83157895 C7.52280702,8 7.74736842,8.05614035 7.91578947,7.97192982 C8.08421053,7.85964912 8.08421053,7.69122807 7.8877193,7.49473684 Z" id="Shape"></Path>
                            </G>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
