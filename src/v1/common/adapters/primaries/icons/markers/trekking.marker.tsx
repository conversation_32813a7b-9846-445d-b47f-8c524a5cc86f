import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class TrekkingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M11.486 4.88C10.8 6 13.029 7.36 14.057 6.4c.772-.72 0-2.4-1.2-2.4-.428 0-1.028.4-1.371.88zM7.371 8.72c-.857.88-1.285 2-.942 2.4.942 1.52 1.971.96 2.914-1.52.943-2.72.171-3.12-1.972-.88zM16.286 8.32c0 1.44-1.372 1.68-2.143.48-1.029-1.6-2.829-.72-3.772 1.76-.857 2.16-.685 2.64.772 3.6 1.114.72 1.714 2.08 1.714 3.68 0 1.92.257 2.24.857 1.36 1.2-1.76 1.029-4.48-.343-5.52-.685-.56-1.028-1.68-.771-2.56.343-1.12.771-1.36 1.543-.72.514.4 1.286.56 1.543.24.343-.24.6 1.68.6 4.4 0 2.96.343 4.96.857 4.96C18.257 20 18 8.08 16.97 7.52c-.342-.24-.685.16-.685.8zM8.571 16.8C7.2 20 7.886 20.64 9.857 17.92c.857-1.28 1.114-2.4.6-2.88-.514-.48-1.2.16-1.886 1.76z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
