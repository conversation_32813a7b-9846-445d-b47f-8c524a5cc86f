import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class VolleyballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-volleyball-player-500" transform="translate(6.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M9.81873112,0 C8.98446412,0 8.3081571,0.62630031 8.3081571,1.3988809 C8.3081571,2.17146148 8.98446412,2.79776179 9.81873112,2.79776179 C10.6529981,2.79776179 11.3293051,2.17146148 11.3293051,1.3988809 C11.3293051,0.62630031 10.6529981,0 9.81873112,0 Z M5.28700906,0.699440448 C4.45274207,0.699440448 3.77643505,1.32574076 3.77643505,2.09832134 C3.77643505,2.87090193 4.45274207,3.49720224 5.28700906,3.49720224 C6.12127606,3.49720224 6.79758308,2.87090193 6.79758308,2.09832134 C6.79758308,1.32574076 6.12127606,0.699440448 5.28700906,0.699440448 Z M0.771513897,2.12974154 L0.0162268882,5.45208366 C-0.0338714271,5.67444399 0.0357397494,5.90548664 0.202914208,6.07170734 C0.370088666,6.23792803 0.614008891,6.31862803 0.857073716,6.28813329 L3.77643505,5.92202028 L3.77643505,9.51211661 L3.22177115,10.6637348 C3.21478634,10.6790018 3.20838991,10.6944939 3.20259396,10.7101819 C3.19664685,10.7132952 3.19074563,10.716483 3.18489192,10.7197446 L0.764138066,12.0790085 L1.54745325,13.2757077 L3.95050514,11.9260059 C4.26491579,11.7606351 4.50011299,11.4983218 4.6217077,11.1883151 L4.60253021,11.2333962 L5.44337689,9.48342906 L6.04229607,9.37277548 L6.04229607,11.0995191 L5.31208686,13.6336247 L6.77250529,13.994273 L7.49976435,11.4642661 C7.53601586,11.3416542 7.55287009,11.2193269 7.55287009,11.0995191 L7.55287009,9.09272582 L7.55287009,5.44798536 L11.9975574,4.88925268 L11.7939834,3.50403269 L1.71414743,4.76903635 L2.24963414,2.41662137 L0.771513897,2.12974154 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
