import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FencingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-fencing-500" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M4.76579521,-2.22044605e-16 C3.88843654,-2.22044605e-16 3.1771968,0.711239731 3.1771968,1.5885984 C3.1771968,2.46595707 3.88843654,3.1771968 4.76579521,3.1771968 C5.1871177,3.1771968 5.59118392,3.0098271 5.88910391,2.71190711 C6.1870239,2.41398711 6.35439361,2.00992089 6.35439361,1.5885984 C6.35439361,1.16727591 6.1870239,0.763209691 5.88910391,0.465289699 C5.59118392,0.167369708 5.1871177,-2.22044605e-16 4.76579521,-2.22044605e-16 Z M12.9979617,0.724798021 L10.0342331,4.50764797 L9.27716621,3.93922763 L8.51513589,4.95692342 L9.24986265,5.50796855 L8.7794882,6.10865714 L6.80739261,4.62803392 C6.55149737,4.14561611 6.04941195,3.81263617 5.46577138,3.81263617 L4.06457796,3.81263617 C3.84146592,3.81263617 3.63142023,3.86388346 3.44030842,3.95039745 L3.43162075,3.93922763 L3.35343197,3.99755893 C3.20771886,4.07538894 3.07518807,4.17274645 2.96248782,4.29169784 L0.695011801,5.99199492 L0.0124108932,9.40748148 L1.25846783,9.65569935 L1.84674564,6.7167923 L2.54175744,6.19553377 L2.54175744,8.64420951 C2.54175744,8.7305295 2.55396595,8.81371614 2.56782037,8.89615105 L2.54175744,8.89615105 L2.54175744,11.1201888 L0.254423947,12.8353788 L1.01645477,13.8530743 L3.81263617,11.7556282 L3.81263617,10.1409666 C3.89507095,10.1548211 3.97825784,10.1670298 4.06457796,10.1670298 L5.46701245,10.1670298 C5.55333257,10.1670298 5.63651946,10.1548217 5.71895425,10.1409666 L6.35439361,11.2145122 L6.35439361,13.9796659 L7.62527233,13.9796659 L7.62527233,10.8670056 L6.76891866,9.42237427 C6.90618627,9.19362119 6.98983297,8.92881899 6.98983297,8.64296786 L6.98983297,6.35439361 L9.01281391,7.87100881 L10.2663172,6.26999964 L11.0568933,6.86324138 L11.8189237,5.84554584 L11.0506876,5.26967879 L13.9982824,1.50792741 L12.9979617,0.724798021 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
