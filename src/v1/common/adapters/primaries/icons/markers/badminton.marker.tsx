import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BadmintonMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G >
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path d="M16.5652174,5 C15.9422554,5 15.3181047,5.23896052 14.84375,5.71331522 C14.5988453,5.95822026 14.4133833,6.25662357 14.2921196,6.57880435 L5.18070652,10.5067935 C5.06895396,10.5543478 5,10.6613453 5,10.7826087 C5,11.6492866 5.39945652,12.3566576 5.69429348,12.7608696 L10.9918478,9.60326087 L11.4388587,10.0407609 L5.94157609,13.3220109 C6.10682752,14.869905 7.1875,15.5416101 7.82472826,15.8043478 L12.4850543,11.0964674 L12.9130435,11.5149457 L8.29076087,16.1942935 C8.64266304,16.787534 9.4118547,17.8158967 10.611413,18.0394022 L13.9497283,12.5611413 L14.3872283,12.9986413 L11.1915761,18.267663 C11.5838996,18.5660667 12.3091033,19 13.2173913,19 C13.338655,19 13.4456522,18.9310463 13.4932065,18.8192935 L15.6521739,13.8165761 C15.7092391,13.7583221 15.7401496,13.6786685 15.7377717,13.5978261 L16.5557065,11.7146739 C16.6246605,11.6552311 16.662704,11.5672554 16.6603261,11.4769022 C16.6603261,11.4733355 16.6603261,11.470958 16.6603261,11.4673913 L17.4211957,9.70788043 C17.7445652,9.586617 18.04178,9.40115504 18.2866848,9.15625 C19.2353942,8.20754091 19.2353942,6.66202461 18.2866848,5.71331522 C17.8123301,5.23896052 17.1881793,5 16.5652174,5 Z M14.4538043,7.16847826 L14.6440217,7.35869565 L13.4171196,8.11956522 L13.0652174,7.76766304 L14.4538043,7.16847826 Z M15.0910326,7.80570652 L15.423913,8.13858696 L14.4347826,9.12771739 L13.8546196,8.55706522 L15.0910326,7.80570652 Z M12.4755435,8.03396739 L12.8845109,8.44293478 L11.5339674,9.27038043 L10.9538043,8.69021739 L12.4755435,8.03396739 Z M15.861413,8.57608696 L16.1942935,8.90896739 L15.4334239,10.1358696 L14.8722826,9.56521739 L15.861413,8.57608696 Z M13.3315217,8.88994565 L13.9972826,9.56521739 L12.9130435,10.6494565 L11.9714674,9.7173913 L13.3315217,8.88994565 Z M16.6413043,9.35597826 L16.8315217,9.54619565 L16.232337,10.9347826 L15.8804348,10.5828804 L16.6413043,9.35597826 Z M14.4347826,10.0027174 L15.1100543,10.6684783 L14.2730978,12.0190217 L13.3505435,11.0869565 L14.4347826,10.0027174 Z M15.5570652,11.1154891 L15.9755435,11.5339674 L15.3097826,13.0557065 L14.7201087,12.4660326 L15.5570652,11.1154891 Z" fill="#FFFFFF" fillRule="nonzero"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
