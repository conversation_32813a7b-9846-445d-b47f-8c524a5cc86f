import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FitnessMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-fitness-500" transform="translate(4.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M8.29083748,0.0372340426 C7.37419652,0.0554151152 6.63894113,0.7553709 6.65837479,1.6128934 C6.67780929,2.47041589 7.45193284,3.15825199 8.3685738,3.14007092 C9.28521476,3.12189062 9.99455887,2.42193406 9.97512438,1.56441157 C9.95569071,0.706889074 9.20747844,0.0190537456 8.29083748,0.0372340426 Z M12.7995439,1.70985705 L10.5970149,2.99462544 L11.4521144,4.25515293 L13.6546434,2.97038453 L12.7995439,1.70985705 Z M6.89158375,3.60064827 C6.74906716,3.61579865 6.61302902,3.65822063 6.50290216,3.72185284 C6.18871808,3.90062977 6.07859121,4.01577371 5.88101161,4.30363475 L4.06716418,7.01861702 L0.879975124,7.79432624 C0.52368408,7.83674823 0.0248756219,8.23066268 0.0248756219,8.78820368 C0.0248756219,9.96691789 0.854063018,14 0.854063018,14 L2.51243781,14 L1.81281095,9.34574468 L4.94817579,9.34574468 C5.77736318,9.34574468 6.16604478,9.26393141 6.60655058,8.57003546 L8.31674959,6.00049867 L10.8302239,7.13982159 L14.1210614,5.20054854 L13.2141376,3.91578014 L10.6747512,5.34599402 L8.10945274,3.91578014 C7.83413682,3.7127623 7.3191335,3.55822706 6.89158375,3.60064827 Z M15.1057214,5.20054854 L13.0068408,6.43683511 L13.8878524,7.74584441 L15.986733,6.50955785 L15.1057214,5.20054854 Z M8.7831675,7.21254433 L6.78793532,10.1214539 L2.71973466,10.1214539 L3.00476783,11.6728723 L7.66894693,11.6728723 L10.2601575,7.93977172 L8.7831675,7.21254433 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
