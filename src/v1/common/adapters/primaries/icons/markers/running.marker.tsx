import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class RunningMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G fill="#FFF" fillRule="nonzero">
                        <Path d="M13.474 4.8c-.869.56-.948.96-.237 1.68 1.105 1.12 3.631.32 3.237-1.04-.474-1.28-1.58-1.52-3-.64zM7.947 7.2C6.842 8 6.29 8.96 6.605 9.28c.316.32 1.184 0 1.895-.64 2.21-1.92 3.632-.96 2.053 1.44-1.185 1.84-1.185 2.08.473 3.84 1.5 1.6 1.658 2.24.948 3.52-1.027 2-1.106 2.56-.158 2.56.395 0 1.105-1.12 1.658-2.4.868-2.08.79-2.64-.316-3.92-1.105-1.28-1.184-1.6-.158-2.4.79-.64 1.263-.72 1.658-.08.631 1.04 4.342 1.04 4.342 0 0-.48-.553-.8-1.263-.8-.632 0-2.369-.88-3.71-1.92C10.156 5.68 10 5.6 7.946 7.2z" />
                        <Path d="M6.605 14.24C5.185 14.4 4 14.8 4 15.28c0 .96 5.29.96 5.921 0 .632-1.04-.395-1.36-3.316-1.04z" />
                    </G>
                </G>
            </Svg>
        )
    }
}
