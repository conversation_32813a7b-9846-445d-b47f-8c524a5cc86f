import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class CyclingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icon-cycling" transform="translate(4.000000, 5.000000)" fill="#FFFFFF" fillRule="nonzero">
                            <Path d="M11.25,0 C10.4544773,0 9.80769231,0.633661826 9.80769231,1.41304348 C9.80769231,2.19242513 10.4544773,2.82608696 11.25,2.82608696 C12.0455227,2.82608696 12.6923077,2.19242513 12.6923077,1.41304348 C12.6923077,0.633661826 12.0455227,0 11.25,0 Z M8.29326923,1.69565217 C7.78846154,1.69565217 7.23858173,2.09086291 6.5625,2.57880435 C5.73317308,3.17714009 4.86778846,3.81521739 4.52524038,4.11548913 C4.340445,4.27887257 4.03846154,4.58797583 4.03846154,5.29891304 C4.03846154,6.00985083 4.57932692,6.27921196 5.17427885,6.64130435 C5.79627404,7.02105978 7.04927885,7.78940217 7.04927885,7.78940217 L6.68870192,10.580163 C6.68870192,10.580163 6.69320942,11.2292802 7.265625,11.3043478 C7.83804115,11.379416 8.00480769,10.7744565 8.00480769,10.7744565 C8.00480769,10.7744565 8.50285442,8.25305735 8.58173077,7.77173913 C8.66286058,7.27496617 8.78680904,6.94378383 8.203125,6.53532609 C7.62620192,6.13349213 6.61658654,5.59918478 6.61658654,5.59918478 C6.61658654,5.59918478 8.52764423,4.13315217 8.65384615,4.0625 C8.79807692,3.9830163 8.92427885,3.859375 9.06850962,4.04483696 C9.21724788,4.23471496 10.2043269,5.51086957 10.2043269,5.51086957 C10.2043269,5.51086957 10.3485577,5.65217391 10.7271635,5.65217391 L13.2692308,5.65217391 C13.586989,5.65217391 13.8461538,5.39826752 13.8461538,5.08695652 C13.8461538,4.77564552 13.586989,4.52173913 13.2692308,4.52173913 L10.8533654,4.52173913 C10.8533654,4.52173913 9.61838942,2.65387257 9.375,2.24320652 C9.12259615,1.81929348 8.72596154,1.69565217 8.29326923,1.69565217 Z M2.88461538,7.34782609 C1.29131596,7.34782609 0,8.61294143 0,10.173913 C0,11.7348847 1.29131596,13 2.88461538,13 C4.47791481,13 5.76923077,11.7348847 5.76923077,10.173913 C5.76923077,8.61294143 4.47791481,7.34782609 2.88461538,7.34782609 Z M12.1153846,7.34782609 C10.5220852,7.34782609 9.23076923,8.61294143 9.23076923,10.173913 C9.23076923,11.7348847 10.5220852,13 12.1153846,13 C13.708684,13 15,11.7348847 15,10.173913 C15,8.61294143 13.708684,7.34782609 12.1153846,7.34782609 Z M2.88461538,8.47826087 C3.83789077,8.47826087 4.61538462,9.23997948 4.61538462,10.173913 C4.61538462,11.1078466 3.83789077,11.8695652 2.88461538,11.8695652 C1.93134,11.8695652 1.15384615,11.1078466 1.15384615,10.173913 C1.15384615,9.23997948 1.93134,8.47826087 2.88461538,8.47826087 Z M12.1153846,8.47826087 C13.06866,8.47826087 13.8461538,9.23997948 13.8461538,10.173913 C13.8461538,11.1078466 13.06866,11.8695652 12.1153846,11.8695652 C11.1621092,11.8695652 10.3846154,11.1078466 10.3846154,10.173913 C10.3846154,9.23997948 11.1621092,8.47826087 12.1153846,8.47826087 Z"></Path>
                        </G>
                    </G>
                </G>
            </Svg>)
    }
}
