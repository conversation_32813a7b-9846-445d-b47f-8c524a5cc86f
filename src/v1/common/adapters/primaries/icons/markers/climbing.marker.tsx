import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class ClimbingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-climbing-500" transform="translate(6.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M4.8011718,0 C3.9714843,0 3.3,0.728290998 3.3,1.62689805 C3.3,2.5255051 3.9714843,3.2537961 4.8011718,3.2537961 C5.6285157,3.2537961 6.3,2.5255051 6.3,1.62689805 C6.3,0.728290998 5.6285157,0 4.8011718,0 Z M11.7,0 C11.5546875,0 11.4386718,0.115662364 11.4105468,0.264370933 L11.4046875,0.264370933 C10.9664064,2.7237833 9.9363282,5.39037072 7.2,6.77069219 L7.2046875,6.78213124 L7.2,6.77323406 L7.2,7.50279631 L4.4988282,8.77635228 L4.5,8.78524946 L3.0726564,13.2846394 C3.0246093,13.4053858 3,13.5350291 3,13.6659436 C3,14.2048536 3.403125,14.6420824 3.9,14.6420824 C4.3066407,14.6420824 4.6628907,14.3459362 4.7683593,13.9188755 L6,10.0867679 L8.5183593,8.91235087 L8.7011718,11.083243 C8.7105468,11.6132557 9.1101564,12.0390456 9.6,12.0390456 C10.096875,12.0390456 10.5,11.6018167 10.5,11.0629067 C10.5,11.0463836 10.4988282,11.0298605 10.4976564,11.0120662 L10.5011718,11.0120662 L10.1964843,7.38967614 C10.1988282,7.36679772 10.2,7.34391963 10.2,7.32104121 C10.2,6.87237332 9.8636718,6.50759219 9.45,6.50759219 C9.3164064,6.50759219 9.1851564,6.54699371 9.0703125,6.62071236 L7.5046875,7.36044273 L7.4929689,7.33883557 C10.415625,5.84285174 11.540625,2.93731367 11.9953125,0.386388286 L11.9894532,0.386388286 C11.9929689,0.366052061 12,0.346986768 12,0.32537961 C12,0.146166703 11.8664064,0 11.7,0 Z M7.2,6.77069219 L7.2,5.47044469 L10.0265625,1.66884143 L10.021875,1.66502863 C10.1367189,1.517591 10.2,1.33075184 10.2,1.13882863 C10.2,0.690160738 9.8636718,0.32537961 9.45,0.32537961 C9.1792968,0.32537961 8.9308593,0.484256291 8.7984375,0.739730369 L6.6,3.5791757 L2.4691407,5.11837386 L1.4109375,3.35801941 C1.2808593,3.09364848 1.0265625,2.92841649 0.75,2.92841649 C0.3363282,2.92841649 0,3.29319761 0,3.74186551 C0,3.91853655 0.0539064,4.09139447 0.1523439,4.23247711 L1.5515625,6.57241399 C1.6300782,6.73256161 1.7835939,6.8329718 1.95,6.8329718 C1.9945314,6.8329718 2.0378907,6.82534588 2.08125,6.81263557 L2.0824218,6.81136464 C2.0835939,6.81136464 2.0835939,6.81136464 2.0835939,6.81136464 L4.2,6.18221258 L4.4214843,8.10271497 L7.2,6.77450531 L7.2,6.77069219 Z M7.5,10.0943941 L6.9,10.3396998 L6.9,14.6420824 C6.8988282,14.7590157 6.9550782,14.8683231 7.0488282,14.9280605 C7.1425782,14.9865273 7.2574218,14.9865273 7.3511718,14.9280605 C7.4449218,14.8683231 7.5011718,14.7590157 7.5,14.6420824 L7.5,10.0943941 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
