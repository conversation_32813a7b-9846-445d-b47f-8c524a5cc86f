import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class PaintballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M9.867 7.438c-.99.75-.534 2.062.762 2.062.685 0 .99.187.685.437-.304.25-1.752.376-3.2.25-2.743-.187-4.343 1.25-2.743 2.563.534.437.534 1.188.153 2.063-.381.812-.457 1.624-.229 1.812.99.75 2.438.312 2.895-.875.229-.688.839-1.25 1.296-1.25.457 0 1.524-.438 2.285-1.063 1.296-.937 1.372-.937 1.372.626 0 2.25 1.371 2.187 1.676-.063.152-1.375.61-1.75 2.743-1.875C18.857 12 20 11.625 20 11.25s-.914-.5-2.286-.312c-1.219.25-2.438.124-2.666-.188-.229-.312-.915-.437-1.524-.187-.61.187-1.143.062-1.143-.313 0-.313.533-.813 1.219-1 .99-.313 1.067-.563.305-1.313-.99-1-3.2-1.25-4.038-.5zm3.047 1.187c-.228.188-.914.25-1.447.063-.61-.188-.381-.376.457-.376.838-.062 1.295.126.99.313zm-.304 3.188c-1.372.124-3.658.124-5.029 0-1.295-.126-.229-.25 2.514-.25s3.81.124 2.515.25zm-4.877 2.75c-.99 1.624-1.523 1.312-.914-.563.229-.75.686-1.188 1.067-1 .38.187.304.875-.153 1.563zm3.124-1.688c0 .188-.305.5-.762.688-.38.187-.762.062-.762-.25 0-.376.381-.688.762-.688.457 0 .762.125.762.25z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
