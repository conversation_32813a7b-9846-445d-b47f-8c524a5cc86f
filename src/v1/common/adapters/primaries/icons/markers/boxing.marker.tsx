import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BoxingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="icons8-boxing-glove-500" transform="translate(6.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <Path d="M7.03859857,0 C3.55804646,0 2.28763354,0.91 2.13064133,3.67 C2.25423107,6.10750016 2.87440618,7.71624992 3.16389549,8.46 C3.20175192,8.55875008 3.22736038,8.64 3.25296912,8.71 C3.31309397,8.87375008 3.24740209,9.06250016 3.10154394,9.13 C3.06591449,9.14624992 3.03028504,9.15 2.99465558,9.15 C2.88220019,9.15 2.7719715,9.07375008 2.72743468,8.95 C2.70293957,8.88375008 2.67510385,8.80250016 2.63836105,8.71 C2.36891344,8.01624992 1.81999696,6.61375008 1.61401425,4.51 C1.13412998,4.58375008 0.683194774,4.88375008 0.393705463,5.36 C0.12759791,5.8 -0.235377007,6.77624992 0.304631829,8.31 C1.57059078,12.04 3.05700713,12.5875002 3.94774347,12.92 C4.0490648,12.9575002 4.14259211,12.9937501 4.22387173,13.03 C4.60466166,13.2012499 5.40743772,13.24 6.01425178,13.24 C7.03859857,13.24 9.84775838,13.0762499 10.4946556,12.35 C11.608076,11.1 12,7.245 12,4.32 C12,1.05 10.7930523,0 7.03859857,0 Z M10.8331354,12.87 C9.84775838,13.8325002 6.65558195,13.88 6.01425178,13.88 C4.57459924,13.88 4.15595316,13.6912499 4.01900238,13.63 C3.94885682,13.5987501 3.8653505,13.5662499 3.7695962,13.53 C3.708358,13.5075002 3.64155278,13.4862499 3.5736342,13.46 C3.76402917,14.6512499 3.914341,15.1525002 4.1347981,15.4 C4.57125891,15.8512499 5.34285919,16 7.29691211,16 C9.33669834,16 10.1984859,15.7762499 10.4590261,15.19 C10.6939579,14.7025002 10.7919389,13.6775002 10.8331354,12.87 Z" id="Shape"></Path>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
