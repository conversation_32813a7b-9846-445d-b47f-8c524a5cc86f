import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class CrossfitMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="Lo4WbX9201" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <G id="Group" transform="translate(7.000000, 7.000000) scale(-1, 1) rotate(-180.000000) translate(-7.000000, -7.000000) ">
                                <Path d="M5.1625,12.6875 C5.1625,11.8125 5.775,11.375 6.7375,11.55 C8.575,11.9 8.4,13.475 6.5625,13.825 C5.6875,14 5.1625,13.5625 5.1625,12.6875 Z" id="Path"></Path>
                                <Path d="M2.975,8.1375 C2.5375,6.3 2.8875,5.425 4.2875,4.375 C5.5125,3.5 5.95,2.45 5.5125,1.4875 C5.25,0.7 5.3375,0 5.8625,0 C7.35,0 7.7875,3.7625 6.475,5.25 C4.725,7.175 4.8125,7.7 7.0875,8.05 C8.1375,8.225 9.1875,8.925 9.3625,9.625 C9.7125,10.7625 9.625,10.7625 8.4875,9.8 C7.525,9.0125 6.9125,8.925 6.475,9.625 C5.425,11.2875 3.5875,10.5 2.975,8.1375 Z" id="Path"></Path>
                                <Path d="M10.5,8.4 C8.75,6.3 10.0625,0 12.25,0 C13.825,0 13.9125,0.4375 13.3875,3.325 C13.0375,5.075 12.5125,7.2625 12.25,8.1375 C11.725,9.625 11.55,9.625 10.5,8.4 Z" id="Path"></Path>
                                <Path d="M0.7875,2.1875 C0.175,1.4 -0.0875,0.6125 0.175,0.2625 C0.875,-0.35 3.4125,1.575 3.4125,2.7125 C3.4125,3.9375 2.0125,3.675 0.7875,2.1875 Z" id="Path"></Path>
                            </G>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
