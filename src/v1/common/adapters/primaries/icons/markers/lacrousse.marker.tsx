import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class LacrousseMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M11.162 3.724c-.572.457-1.372 1.79-1.829 3.01-.457 1.18-1.257 2.818-1.79 3.58-.762 1.143-.914 1.753-.8 3.048.19 1.486.076 1.79-1.333 3.238-1.22 1.295-1.448 1.752-1.105 2.095.343.343.8.115 2.095-1.105 1.41-1.371 1.714-1.485 2.705-1.219 1.219.343 3.047-.266 4.61-1.485.495-.42 1.714-.953 2.666-1.22C18.476 13.097 20 11.458 20 9.744c0-4.343-5.867-8.343-8.838-6.02zm1.98 1.18c0 .496-.266.763-.761.763-.876 0-.99-.381-.305-1.067.686-.686 1.067-.571 1.067.305zm1.296-.533c.876.343.762 1.296-.152 1.296-.496 0-.762-.267-.762-.762 0-.838.038-.876.914-.534zm1.943.915c.114.19-.038.38-.343.38-.343 0-.61-.19-.61-.38 0-.229.153-.381.343-.381.229 0 .496.152.61.38zm-5.295 2.171c-.42.42-.724-.228-.42-.914.305-.648.305-.648.458 0 .114.38.076.8-.038.914zm2.057-.647c0 .495-.267.761-.762.761s-.762-.266-.762-.761c0-.496.267-.762.762-.762s.762.266.762.762zm1.905 0c0 .495-.267.761-.762.761-.496 0-.762-.266-.762-.761 0-.496.266-.762.762-.762.495 0 .762.266.762.762zm1.904 0c0 .495-.266.761-.762.761-.495 0-.761-.266-.761-.761 0-.496.266-.762.761-.762.496 0 .762.266.762.762zm.915.19c.114.305.038.571-.153.571-.228 0-.38-.266-.38-.571 0-.305.076-.571.152-.571s.228.266.38.571zm-6.629 1.714c0 .496-.267.762-.724.762-.533 0-.685-.19-.533-.762.114-.419.457-.762.724-.762.305 0 .533.343.533.762zm1.905 0c0 .496-.267.762-.762.762s-.762-.266-.762-.762c0-.495.267-.762.762-.762s.762.267.762.762zm1.905 0c0 .496-.267.762-.762.762-.496 0-.762-.266-.762-.762 0-.495.266-.762.762-.762.495 0 .762.267.762.762zm1.904 0c0 .496-.266.762-.762.762-.495 0-.761-.266-.761-.762 0-.495.266-.762.761-.762.496 0 .762.267.762.762zm1.524 0c0 .42-.266.762-.571.762-.305 0-.572-.343-.572-.762s.267-.762.572-.762c.305 0 .571.343.571.762zm-7.238 1.905c0 .495-.267.762-.762.762s-.762-.267-.762-.762.267-.762.762-.762.762.267.762.762zm1.905 0c0 .495-.267.762-.762.762s-.762-.267-.762-.762.267-.762.762-.762.762.267.762.762zm1.905 0c0 .495-.267.762-.762.762-.496 0-.762-.267-.762-.762s.266-.762.762-.762c.495 0 .762.267.762.762zm1.904 0c0 .495-.266.762-.762.762-.495 0-.761-.267-.761-.762s.266-.762.761-.762c.496 0 .762.267.762.762zm1.448-.114c-.19.952-1.067 1.066-1.067.152 0-.457.229-.8.61-.8.343 0 .533.267.457.648zm-9.067.305c0 .304-.266.571-.61.571-.533 0-.533-.114-.152-.571.267-.305.534-.572.61-.572.076 0 .152.267.152.572zm0 1.714c0 .419-.076.762-.19.762-.076 0-.343-.343-.572-.762-.342-.61-.304-.762.19-.762.306 0 .572.343.572.762zm1.905 0c0 .495-.267.762-.762.762s-.762-.267-.762-.762.267-.762.762-.762.762.267.762.762zm1.905 0c0 .495-.267.762-.762.762s-.762-.267-.762-.762.267-.762.762-.762.762.267.762.762zm1.905-.229c0 .267-.343.61-.762.724-.572.152-.762 0-.762-.533 0-.457.266-.724.762-.724.419 0 .762.228.762.533zm1.523-.152c-.723.495-1.142.495-1.142 0 0-.229.38-.381.876-.381.685 0 .724.076.266.38zm-7.238 2.971c-.571.19-1.523-.647-1.523-1.371 0-.457.228-.381.99.343.533.495.762.952.533 1.028zm1.905-.876c0 .495-.152.533-.762.19-.419-.228-.762-.495-.762-.57 0-.115.343-.191.762-.191.42 0 .762.266.762.571zm1.333-.19c-.114.19-.38.38-.61.38-.19 0-.342-.19-.342-.38 0-.229.267-.381.61-.381.304 0 .457.152.342.38z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
