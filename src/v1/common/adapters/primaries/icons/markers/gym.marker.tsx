import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class GymMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G  stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle  fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path d="M 8 2 L 8 12.8125 L 6.03125 19.71875 L 7.96875 20.28125 L 9.96875 13.28125 L 10 13.125 L 10 13 L 14.875 13 L 17.65625 22 L 19.75 22 L 16.59375 11.8125 C 16.097656 10.203125 14.691406 9.132813 13.03125 9.03125 L 13 9 L 10 9 L 10 2 Z M 5 9 C 3.894531 9 3 9.894531 3 11 C 3 12.105469 3.894531 13 5 13 C 6.105469 13 7 12.105469 7 11 C 7 9.894531 6.105469 9 5 9 Z M 12.21875 14 L 10.21875 22 L 12.28125 22 L 14.28125 14 Z" fill="#FFFFFF" fillRule="nonzero"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
