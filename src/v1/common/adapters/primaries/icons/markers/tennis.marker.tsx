import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class TennisMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-tennis-player-500" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M6.25783133,0 C5.48152746,0 4.85220884,0.612405119 4.85220884,1.36784512 C4.85220884,2.12328512 5.48152746,2.73569024 6.25783133,2.73569024 C7.03413519,2.73569024 7.66345382,2.12328512 7.66345382,1.36784512 C7.66345382,0.612405119 7.03413519,0 6.25783133,0 Z M12.4495984,2.0586069 C12.3171837,2.04728278 12.1839455,2.04865724 12.0518072,2.06271044 C11.5148594,2.11810816 10.9659639,2.37252736 10.5337349,2.79313973 C9.76485944,3.54135101 9.53925703,4.64315025 10.0417671,5.4084596 L9.06907631,6.35569234 L9.06907631,6.15530303 L7.1187751,6.15530303 L6.32811245,4.71222643 L6.30562249,4.67255892 L6.1685743,4.42292719 L6.16716867,4.42429503 C6.12640562,4.34906355 6.10953815,4.31144781 6.07369478,4.25605008 C5.74901026,3.73681181 5.16968116,3.41981071 4.54508032,3.41961279 C3.94769076,3.41961279 3.41285141,3.69728535 3.09658635,4.13978325 C2.58002008,4.86474116 1.50190763,6.20386153 1.35431727,7.01020623 C1.34400234,7.06665663 1.33859498,7.12385261 1.33815261,7.18118687 C1.33815261,7.34054082 1.46676707,7.92666246 1.52369478,8.14141414 C1.70291165,8.81644571 1.94538153,9.67613636 2.03534137,9.9955282 L0.031626506,12.0623422 L1.06194779,12.9904251 L3.25823293,10.7239057 C3.37919208,10.5976386 3.44649435,10.4314612 3.44658635,10.2588384 L3.44658635,8.16945497 C3.44639514,8.0040377 3.5078184,7.84415852 3.61947791,7.71943392 L5.33714859,5.81334175 L6.07720884,7.16066919 C6.199319,7.38383048 6.43798228,7.52323248 6.69779116,7.52314815 L9.06907631,7.52314815 L9.06907631,7.32275884 L10.5393574,5.89199285 C11.3258032,6.3816814 12.4580321,6.16214226 13.2269076,5.41393098 C14.0913655,4.57270623 14.2740964,3.28009259 13.5003012,2.52777778 C13.2185358,2.25518579 12.8463946,2.08890217 12.4503012,2.0586069 L12.4495984,2.0586069 Z M12.3582329,2.74116162 C12.611245,2.75962753 12.8389558,2.85058923 13.0034137,3.0106271 C13.0038822,3.01062802 13.0043507,3.01062802 13.0048193,3.0106271 C13.4426707,3.43671086 13.3801205,4.29776936 12.7300201,4.93039773 C12.0799197,5.56302609 11.1964859,5.62321128 10.7572289,5.1964436 C10.3186747,4.768992 10.3805221,3.90930135 11.0306225,3.27667298 C11.3560241,2.96001684 11.7404618,2.78766835 12.09749,2.746633 C12.186747,2.73637416 12.2731928,2.73500631 12.3575301,2.74116162 L12.3582329,2.74116162 Z M13.2859438,7.52314815 C12.8977918,7.52314815 12.5831325,7.82935071 12.5831325,8.20707071 C12.5831325,8.58479071 12.8977918,8.89099327 13.2859438,8.89099327 C13.6740957,8.89099327 13.988755,8.58479071 13.988755,8.20707071 C13.988755,7.82935071 13.6740957,7.52314815 13.2859438,7.52314815 Z M4.26746988,8.03882576 L4.14939759,8.16945497 L4.14939759,9.85874369 L4.9253012,10.6131103 L5.57891566,12.9589646 L6.9374498,12.6060606 L6.23463855,10.0823864 C6.20258333,9.96615151 6.13954697,9.86021824 6.05190763,9.77530513 L4.26746988,8.03814184 L4.26746988,8.03882576 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
