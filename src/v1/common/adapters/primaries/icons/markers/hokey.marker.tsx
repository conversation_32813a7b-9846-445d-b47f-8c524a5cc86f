import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class HokeyMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-hockey" transform="translate(6.000000, 6.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M10.8,0 C10.8,0 8.9671878,7.3570314 8.4,7.8 C7.3875,8.5875 1.4203128,8.4 1.2,8.4 C0.6,8.4 0,8.90625 0,9.6 L0,10.8 C0,11.4 0.6,12 1.8,12 C4.2,12 6.7289064,11.6929686 8.2125,11.04375 C9.4335936,10.509375 9.4429686,9.7570314 12,0 L10.8,0 Z M3.3,1.8 C1.6476564,1.8 0.6,2.446875 0.6,3.28125 C0.6,4.115625 1.6476564,4.78125 3.3,4.78125 C4.9523436,4.78125 6,4.115625 6,3.28125 C6,2.446875 4.9523436,1.8 3.3,1.8 Z M0.6,4.55625 L0.6,5.11875 C0.6,5.953125 1.6476564,6.6 3.3,6.6 C4.9523436,6.6 6,5.953125 6,5.11875 L6,4.575 C5.4234378,5.0929686 4.471875,5.4 3.3,5.4 C2.128125,5.4 1.1765628,5.0742186 0.6,4.55625 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
