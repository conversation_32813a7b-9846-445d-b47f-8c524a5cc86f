import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class CricketMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M16.02 5.022c-.313.007-.475.129-.475.129l-7.344 7.375a.476.476 0 000 .667l2.606 2.606a.476.476 0 00.667 0l7.374-7.344s.637-.849-.91-2.395c-.868-.869-1.517-1.047-1.918-1.038zm-7.505.008c-.122 0-.239.015-.354.038-.21.28-.328 1.135.325 1.762.622.599 1.349.57 1.601.417A1.67 1.67 0 008.515 5.03zm-1.03.359a1.663 1.663 0 001.03 2.975c.482 0 .916-.208 1.222-.535a5.899 5.899 0 01-.249.006c-.443 0-.93-.165-1.388-.604a2.063 2.063 0 01-.615-1.842zm.578 8.639v.334c0 .333-.152.636-.363.848L5.15 17.758a.515.515 0 000 .697l.394.393a.513.513 0 00.697 0L8.79 16.3c.242-.241.545-.363.88-.363h.332l-1.939-1.91z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
