import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class DefaultMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width      : 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <Circle  fill="#EEEEEE" stroke="#EEEEEE" cx={12} cy={12} r={8} />
                    <Circle stroke="#000" fill="#000" cx={12} cy={12} r={4} />
                </G>
            </Svg>
        )
    }
}
