import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class AmericanFootballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M9.987 6.478c0 1.166.438 1.555 1.575 1.4.788-.156 1.488-.778 1.488-1.4 0-.622-.7-1.245-1.488-1.4-1.137-.156-1.574.233-1.574 1.4z" />
                            <Path d="M14.8 7.722c-2.625 1.867-7.438 2.567-7.438 1.09 0-.468.438-.779.875-.779.525 0 .875-.389.875-.777 0-.467-.787-.778-1.662-.778-.963 0-2.013.31-2.275.778-.263.388-.088.777.35.777.525 0 .962.856.962 1.945 0 1.71.263 1.944 2.188 1.478 1.575-.312 2.188-.156 2.188.622 0 .544.787 1.478 1.75 2.022 1.05.544 2.187 1.867 2.625 2.878 1.05 2.489 2.975 2.644 2.1.233-.35-1.011-1.4-2.722-2.363-3.889l-1.837-2.178 2.975-2.177c3.587-2.723 2.362-3.89-1.313-1.245z" />
                            <Path d="M9.813 16.122c-1.4.934-2.45 1.945-2.45 2.256 0 1.089 2.362.544 3.85-.934 2.362-2.333 1.312-3.266-1.4-1.322z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
