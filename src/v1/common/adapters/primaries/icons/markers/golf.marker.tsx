import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class GolfMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                            <Path
                                d="M8.616 2.587C8.538 2.933 8.46 6 8.46 9.413c0 5.52.052 6.214.42 6.374.288.106.603-.08.944-.534.472-.666.656-.72 2.334-.72 1.68 0 1.81.027 1.732.56-.027.32.104.64.288.694.157.08.315-.054.315-.267 0-.56.315-.613 1.915-.453 1.154.106 1.363.213 1.442.72.053.453-.079.613-.524.72-.341.053-1.233.24-1.994.4-1.81.373-5.64.373-7.449 0-.76-.16-1.652-.347-1.967-.4-.394-.08-.604-.32-.604-.64 0-.507.289-.64 1.705-.854.604-.106.866-1.2.341-1.413-.446-.187-2.23.533-2.885 1.147-.656.56-.577 1.706.131 2.213 1.05.747 2.964 1.04 7.004 1.04 3.986 0 6.059-.293 6.845-.96.21-.213.604-.373.84-.373.708 0 .577-1.414-.131-1.787-2.57-1.36-2.938-1.44-6.112-1.52l-3.147-.08v-2.24c0-1.227.078-2.427.183-2.693.105-.24 1.076-.88 2.151-1.414 2.046-1.04 2.675-1.52 2.413-1.973-.157-.24-2.911-1.653-3.882-1.973-.262-.107-.472-.347-.472-.587 0-.267-.262-.4-.76-.4-.551 0-.787.16-.919.587zm2.204 1.68c.183.213.55.4.813.4 1.075 0 .813.746-.499 1.44-1.075.56-1.364.373-1.364-.88 0-1.387.394-1.76 1.05-.96z"
                                fill="#FFF"
                                fillRule="nonzero"
                            />
                    </G>
                </G>
            </Svg>
        )
    }
}
