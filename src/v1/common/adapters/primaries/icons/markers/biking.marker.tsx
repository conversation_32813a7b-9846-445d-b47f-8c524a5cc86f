import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BikingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="AIJtTJerVG" transform="translate(4.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <G id="Group" transform="translate(8.000000, 8.000000) scale(-1, 1) rotate(-180.000000) translate(-8.000000, -8.000000) ">
                                <Path d="M6.44444444,15.5777778 C6.44444444,15.3555556 6.28888889,15.0888889 6.08888889,15.0222222 C5.82222222,14.9111111 5.75555556,14.6666667 5.82222222,14.0888889 C5.88888889,13.4444444 5.82222222,13.2666667 5.46666667,13.1111111 C4.91111111,12.9111111 2.66666667,10.2444444 2.31111111,9.4 C2.13333333,9 2.13333333,8.64444444 2.26666667,8.33333333 C2.48888889,7.82222222 3.62222222,7.11111111 4.22222222,7.11111111 C4.73333333,7.11111111 6.6,6.28888889 6.8,5.97777778 C6.88888889,5.84444444 7.02222222,5.42222222 7.13333333,5.02222222 C7.55555556,3.35555556 7.95555556,2.55555556 8.42222222,2.48888889 C9.04444444,2.4 9.26666667,3 8.91111111,3.86666667 C8.75555556,4.24444444 8.53333333,4.86666667 8.44444444,5.22222222 C7.95555556,6.91111111 7.57777778,7.6 6.91111111,8.02222222 C6.53333333,8.24444444 6.02222222,8.44444444 5.8,8.44444444 C5.08888889,8.44444444 5.13333333,8.68888889 5.95555556,9.8 C6.91111111,11.0666667 7.48888889,11.2888889 8.62222222,10.8444444 C10.3777778,10.1333333 11.0222222,9.93333333 11.6666667,9.91111111 C12.2222222,9.88888889 12.3333333,9.97777778 12.3333333,10.4 C12.3333333,10.9333333 11.6666667,11.3111111 9.82222222,11.7777778 C8.53333333,12.0888889 8.15555556,12.6444444 8.77777778,13.2444444 C8.95555556,13.4444444 9.11111111,13.8222222 9.11111111,14.1111111 C9.11111111,14.4 8.95555556,14.7777778 8.77777778,14.9777778 C8.6,15.1555556 8.44444444,15.4666667 8.44444444,15.6444444 C8.44444444,15.9111111 8.2,16 7.44444444,16 C6.62222222,16 6.44444444,15.9333333 6.44444444,15.5777778 Z" id="Path"></Path>
                                <Path d="M2.17777778,13.3333333 C1.26666667,12.8444444 0.444444444,11.8222222 0.444444444,11.1555556 C0.444444444,10.9111111 0.688888889,10.5555556 1,10.3555556 C1.53333333,10 1.55555556,10.0222222 2.77777778,11.5333333 C3.44444444,12.4 4.08888889,13.1555556 4.22222222,13.2222222 C4.6,13.4666667 4.24444444,13.7777778 3.6,13.7777778 C3.26666667,13.7555556 2.62222222,13.5555556 2.17777778,13.3333333 Z" id="Path"></Path>
                                <Path d="M11.2444444,8.02222222 C9.28888889,6.73333333 9.28888889,4.15555556 11.2444444,2.86666667 C12.0444444,2.33333333 13.4888889,2.33333333 14.2222222,2.84444444 C15.5333333,3.73333333 16,4.42222222 16,5.44444444 C16,6.46666667 15.5333333,7.15555556 14.2222222,8.04444444 C13.4888889,8.55555556 12.0444444,8.55555556 11.2444444,8.02222222 Z M14.1555556,6.75555556 C14.6222222,6.26666667 14.7111111,6.04444444 14.6,5.28888889 C14.4444444,4.15555556 14.0666667,3.77777778 12.9333333,3.62222222 C12.1777778,3.51111111 11.9555556,3.6 11.4666667,4.06666667 C11.0666667,4.46666667 10.8888889,4.88888889 10.8888889,5.44444444 C10.8888889,7.15555556 12.9333333,7.97777778 14.1555556,6.75555556 Z" id="Shape"></Path>
                                <Path d="M1.26666667,5.37777778 C0.244444444,4.68888889 0.0222222222,4.2 0,2.73333333 C0,0.755555556 0.755555556,0 2.73333333,0 C3.57777778,0.0222222222 4.26666667,0.133333333 4.53333333,0.333333333 C6.26666667,1.64444444 6.15555556,4.13333333 4.31111111,5.35555556 C3.48888889,5.88888889 2.06666667,5.88888889 1.26666667,5.37777778 Z M4.15555556,4.08888889 C4.62222222,3.6 4.71111111,3.37777778 4.6,2.62222222 C4.44444444,1.48888889 4.06666667,1.11111111 2.93333333,0.955555556 C2.17777778,0.844444444 1.95555556,0.933333333 1.46666667,1.4 C0.244444444,2.62222222 1.06666667,4.66666667 2.77777778,4.66666667 C3.33333333,4.66666667 3.75555556,4.48888889 4.15555556,4.08888889 Z" id="Shape"></Path>
                            </G>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
