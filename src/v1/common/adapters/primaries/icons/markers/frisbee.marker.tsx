import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FrisbeeMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-frisbee-500" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M5.72877358,0 C4.96832911,0 4.35298742,0.631730932 4.35298742,1.41242938 C4.35298742,2.19312782 4.96832911,2.82485876 5.72877358,2.82485876 C6.48921806,2.82485876 7.10455975,2.19312782 7.10455975,1.41242938 C7.10455975,0.631730932 6.48921806,0 5.72877358,0 Z M12.2637579,0 C11.315218,0 10.5440252,0.262072034 10.5440252,0.573799435 C10.5440252,0.885526836 11.315218,1.12552966 12.2637579,1.12552966 C13.2122978,1.12552966 13.9834906,0.885526836 13.9834906,0.573799435 C13.9834906,0.262072034 13.2122978,0 12.2637579,0 Z M9.21123231,0.46345339 L8.48034591,2.42761299 C8.34330454,2.71175424 8.11758962,2.9352048 7.92143278,3.00141243 L7.89993612,3.00141243 L7.87843947,3.02348164 L7.06156643,3.37658898 L7.04006977,3.37658898 C6.80091961,3.47038347 6.44891165,3.53107345 6.20170008,3.53107345 L4.82591392,3.53107345 C4.37179717,3.53107345 3.92574116,3.64693715 3.49312107,3.88418079 L3.49312107,3.86211158 L3.38563778,3.90625 L2.54726808,4.21521893 L2.54726808,4.19314972 C2.04747111,4.36418644 1.60141509,4.65660311 1.19297858,5.07591808 L0.0106623428,6.28972458 L0.978011989,7.28283898 L2.16032822,6.06903249 C2.43978479,5.78213277 2.67356112,5.64971751 2.99869792,5.53937147 L3.92305425,5.20833333 L3.42863109,7.98905367 L2.9557046,9.11458333 L2.9557046,9.13665254 C2.82672465,9.48975989 2.43709788,9.89804025 2.16032822,10.0194209 L2.13883156,10.0194209 L0.268622248,10.9463277 L0.870528695,12.2263418 L2.69774469,11.3215042 L2.71924135,11.299435 C3.38295086,10.9987422 3.94186399,10.4166667 4.22400747,9.66631356 L4.24550413,9.64424435 L4.50346403,9.0263065 L5.0408805,9.09251412 L5.0408805,10.0194209 C5.0408805,10.2649407 4.98713886,10.5297712 4.89040389,10.7918432 L3.72958432,13.3960099 L4.97639053,13.9918785 L6.1372101,11.3877119 L6.15870676,11.3656427 L6.15870676,11.3435734 C6.32530621,10.9159823 6.41666667,10.4663227 6.41666667,10.0194209 L6.41666667,9.24699859 L7.08306309,4.81108757 C7.23891421,4.77246681 7.40282606,4.74212147 7.55598958,4.67867232 L7.57748624,4.67867232 L8.35136596,4.34763418 L8.37286262,4.32556497 C8.37823713,4.3228065 8.38898546,4.32832345 8.39435928,4.32556497 C8.99895263,4.10211441 9.43694723,3.61659181 9.70565546,3.06762006 L9.72715212,3.04555085 L9.74864878,3.00141243 L10.5010318,0.948975989 L9.21123231,0.46345339 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
