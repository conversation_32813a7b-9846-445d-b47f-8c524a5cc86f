import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class KayakingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M11.31 4.49c-.255.28-.4.875-.328 1.33.073.63.327.805 1.163.805 1.31 0 2.037-.945 1.455-1.855-.51-.77-1.745-.945-2.29-.28z" />
                            <Path d="M16.145 5.715c.255 1.855 0 2.135-1.854 2.135-.91 0-.91.035-.327.665.363.35.909.595 1.272.49.546-.14.655 0 .51.735-.11.49-.255.91-.364.91-.4 0-1.855-.875-2.946-1.82-1.09-.91-1.309-.98-1.963-.56-.437.28-1.055 1.47-1.528 2.835l-.727 2.31-1.673.105c-1.345.105-1.672.245-1.745.875-.145.945.727 1.12 5.673 1.05 2.8-.07 3.709.035 3.709.385 0 .77-1.855 1.225-3.31.805-.945-.28-1.636-.28-2.436 0-.836.28-1.49.28-2.436-.035-.69-.21-1.418-.315-1.636-.175-1.346.805 1.854 1.89 4 1.33.763-.175 1.636-.21 1.89-.07.619.385 2.873.385 3.491 0 .255-.14 1.128-.105 1.891.07 2.11.56 5.382-.525 4-1.33-.181-.105-.981-.035-1.709.175-.945.28-1.672.28-2.363.035l-.946-.35.546-1.855c.29-1.015.618-2.1.727-2.38.109-.28.254-1.05.327-1.715.073-.665.291-1.365.51-1.575.181-.175.363-.63.363-.945 0-.35.327-1.19.764-1.855.872-1.47.69-1.96-.837-1.96h-1.127l.254 1.715zm-4.327 3.92c.146.175 0 .63-.29.945-.8.84-1.092 2.345-.655 3.115.327.595.254.665-.728.525-.581-.07-1.054-.21-1.054-.245 0-.07.327-1.155.727-2.415.727-2.205 1.418-2.87 2-1.925zm2.4 1.75c.764.28.91.525.764 1.19-.146.77-.327.875-1.891.875h-1.71l.4-1.4c.292-1.05.51-1.365.946-1.19.291.105.982.35 1.491.525z" />
                            <Path d="M17.89 12.68c-.763.35-1.272.91-1.454 1.61-.29.98-.218 1.085.582 1.19 1.091.14 2.546-1.12 2.691-2.345.146-1.05-.327-1.19-1.818-.455z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
