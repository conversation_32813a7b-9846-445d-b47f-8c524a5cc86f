import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BasketballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path d="M12,5 C8.14032438,5 5,8.14032438 5,12 C5,15.8596756 8.14032438,19 12,19 C15.8596756,19 19,15.8596756 19,12 C19,8.14032438 15.8596756,5 12,5 Z M11.3942308,6.11057692 C11.4173679,6.10847369 11.4384013,6.11268015 11.4615385,6.11057692 L11.4615385,11.4615385 L9.8125,11.4615385 C9.69681477,9.94711538 9.10787246,8.56520446 8.18028846,7.47355769 C9.06580515,6.72475962 10.1721756,6.23467562 11.3942308,6.11057692 Z M12.5384615,6.11057692 C13.7857571,6.22205515 14.9173679,6.71003592 15.8197115,7.47355769 C14.8921275,8.56520446 14.3031852,9.94711538 14.1875,11.4615385 L12.5384615,11.4615385 L12.5384615,6.11057692 Z M7.42307692,8.24759615 C8.15925508,9.13942308 8.62409869,10.2436898 8.73557692,11.4615385 L6.11057692,11.4615385 C6.21995192,10.2436898 6.69110577,9.13942308 7.42307692,8.24759615 Z M16.5769231,8.24759615 C17.3088942,9.13942308 17.7800481,10.2436898 17.8894231,11.4615385 L15.2644231,11.4615385 C15.3759013,10.2436898 15.8407455,9.13942308 16.5769231,8.24759615 Z M6.11057692,12.5384615 L8.73557692,12.5384615 C8.62409869,13.7563102 8.15925508,14.8605769 7.42307692,15.7524038 C6.69110577,14.8605769 6.21995192,13.7563102 6.11057692,12.5384615 Z M9.8125,12.5384615 L11.4615385,12.5384615 L11.4615385,17.8894231 C10.2142429,17.7779448 9.08263208,17.2899641 8.18028846,16.5264423 C9.10787246,15.4347955 9.69681477,14.0528846 9.8125,12.5384615 Z M12.5384615,12.5384615 L14.1875,12.5384615 C14.3031852,14.0528846 14.8921275,15.4347955 15.8197115,16.5264423 C14.9173679,17.2899641 13.7857571,17.7779448 12.5384615,17.8894231 L12.5384615,12.5384615 Z M15.2644231,12.5384615 L17.8894231,12.5384615 C17.7800481,13.7563102 17.3088942,14.8605769 16.5769231,15.7524038 C15.8407455,14.8605769 15.3759013,13.7563102 15.2644231,12.5384615 Z"  fill="#FFFFFF" fillRule="nonzero"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
