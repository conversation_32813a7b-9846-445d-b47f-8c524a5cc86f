import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class SnowboardingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="t4Ck5lBH01" transform="translate(5.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <G id="Group" transform="translate(7.600000, 8.000000) scale(-1, 1) rotate(-180.000000) translate(-7.600000, -8.000000) translate(0.500000, 0.000000)">
                                <Path d="M5.5,14.5 C5.5,13.7 6.2,13 7,13 C7.8,13 8.5,13.7 8.5,14.5 C8.5,15.3 7.8,16 7,16 C6.2,16 5.5,15.3 5.5,14.5 Z" id="Path"></Path>
                                <Path d="M11.1,14.1 C10.1,13 8,11.6 6.4,11 C4.8,10.3 3.5,9.4 3.5,8.9 C3.5,7.6 4.9,7.8 6.3,9.2 C6.9,9.8 7.8,10.1 8.2,9.7 C8.5,9.3 8,8.3 6.9,7.4 C5.1,6 5.1,5.9 7.3,6.5 C9.4,7.1 9.6,6.9 9,5.1 C8.1,2.1 9.9,2.5 11.5,5.6 C12.6,7.5 12.6,8.6 11.8,9.9 C10.9,11.4 11.1,12.1 12.8,13.9 C14,15 14.5,16 13.9,16 C13.3,16 12,15.1 11.1,14.1 Z" id="Path"></Path>
                                <Path d="M0.1,6.1 C-0.7,4.9 6.7,0 9.4,0 C11.3,0 10.8,0.6 6.6,3.5 C1.1,7.3 0.9,7.4 0.1,6.1 Z" id="Path"></Path>
                            </G>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
