import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class CanoeingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M11.36 4.56c-.8.32-.8.64 0 1.6.8.96.72 1.36-.4 2.24-.72.56-1.36 1.52-1.36 2.16 0 .72-.56 1.68-1.28 2.32-1.12.96-1.12 1.12.24 1.6.8.24 2.8.4 4.4.4 2.24-.08 3.04.24 3.28 1.28.4 1.36 2.16 2.08 2.16.8 0-.32-1.04-2.72-2.4-5.2C14.72 9.2 13.6 6.64 13.6 6c0-1.36-.88-1.92-2.24-1.44zm3.04 5.76c1.36 2.64.96 3.52-1.36 2.88-2.88-.72-2.96-.8-1.84-2.72 1.04-2.08 2.08-2.16 3.2-.16z" />
                            <Path d="M12 11.2c0 .4.4.8.8.8.48 0 .8-.4.8-.8 0-.48-.32-.8-.8-.8-.4 0-.8.32-.8.8zM11.2 15.52c0 .24.72.8 1.6 1.28.88.48 1.6.64 1.6.48 0-.24-.72-.8-1.6-1.28-.88-.48-1.6-.64-1.6-.48z" />
                            <Path d="M4.8 17.2c-.8.96-.56 1.04 1.76.72 1.6-.32 3.6 0 5.04.8 2.96 1.6 5.2 1.6 7.28.16 1.36-1.04 1.2-1.12-1.76-.72-2.16.24-4.08 0-5.76-.88-3.04-1.6-5.28-1.6-6.56-.08z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
