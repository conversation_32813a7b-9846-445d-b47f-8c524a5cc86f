import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class RollerSportMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <Path
                        d="M17.444 5v.778H6.556V5H5v14h1.556v-3.889h10.888V19H19V5h-1.556zm0 2.333V8.89H15.89V7.333h1.555zm-6.222 2.334h1.556v1.555h-1.556V9.667zm-.778 1.555H8.89V9.667h1.555v1.555zm3.112-1.555h1.555v1.555h-1.555V9.667zm2.333 0h1.555v1.555H15.89V9.667zm-.778-2.334V8.89h-1.555V7.333h1.555zm-2.333 0V8.89h-1.556V7.333h1.556zm-2.334 0V8.89H8.89V7.333h1.555zm-3.888 0H8.11V8.89H6.556V7.333zm0 2.334H8.11v1.555H6.556V9.667zm0 3.889V12H8.11v1.556H6.556zm2.333 0V12h1.555v1.556H8.89zm2.333 0V12h1.556v1.556h-1.556zm2.334 0V12h1.555v1.556h-1.555zm2.333 0V12h1.555v1.556H15.89z"
                        fill="#FFF"
                        fillRule="nonzero"
                    />
                </G>
            </Svg>
        )
    }
}
