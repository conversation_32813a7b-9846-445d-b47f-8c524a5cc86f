import React, { PureComponent } from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg';
import { BaseIconProps } from '../baseIcon';

export class ShopMarker extends PureComponent<BaseIconProps> {
    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#39b54a" offset="0%"/>
                        <Stop stopColor="#0000ff" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                    <G transform="translate(4,2.5)">
                        <Path
                            d="M3.6 3.2C1.7 5.6 1 7.5 1 10.7V15h13v-4.3c0-3.2-.7-5.1-2.6-7.5-3.4-4-4.4-4-7.8 0zm5.2-.4C9 3.5 8.5 4 7.6 4 6.7 4 6 3.3 6 2.4 6 .7 8.2 1 8.8 2.8zm3.4 4.7c.9 3.1.9 6.5-.1 6.5-.5 0-1.1-1-1.3-2.3-.4-3.2-6.2-3.2-6.6 0C4 13 3.4 14 2.9 14c-1 0-1.1-1.6-.3-5.7.5-2.7.9-2.9 4.7-2.9 3.3.1 4.3.5 4.9 2.1zm-5.6 6.2c-1.1 1.1-1.9-.6-1.1-2.4.8-1.7.8-1.7 1.2 0 .3 1 .2 2.1-.1 2.4zm3.4-1.1c0 .8-.4 1.4-1 1.4-.5 0-1-.9-1-2.1 0-1.1.5-1.7 1-1.4.6.3 1 1.3 1 2.1z"
                            fill="#fff"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
