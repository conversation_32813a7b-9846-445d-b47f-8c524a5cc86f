import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class YogaMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <G transform="translate(-0.000000, 0.000000)">
                            <Circle  fill="url(#linearGradient-1)" fillRule="evenodd" cx="12" cy="12" r="12"></Circle>
                            <Path d="M12,4 C10.9456382,4 10.0909091,4.85961328 10.0909091,5.92 C10.0909091,6.98038672 10.9456382,7.84 12,7.84 C13.0543618,7.84 13.9090909,6.98038672 13.9090909,5.92 C13.9090909,4.85961328 13.0543618,4 12,4 Z"  fill="#FFFFFF" fillRule="nonzero"></Path>
                            <Path d="M6.27272727,9.11993572 C6.04323169,9.11673581 5.8297664,9.23800019 5.71407145,9.43735862 C5.5983765,9.63671706 5.5983765,9.88328294 5.71407145,10.0826414 C5.8297664,10.2819998 6.04323169,10.4032642 6.27272727,10.4 L10.0909091,10.64 L10.0909091,19.3387501 C10.0909091,19.7035501 10.3856746,20 10.7484019,20 C11.0945837,20 11.3817428,19.7300198 11.4046519,19.3824998 L11.683061,15.1775002 C11.6945155,15.0104602 11.8326364,14.88 12,14.88 C12.0598182,14.88 12.6363636,14.8737498 12.6363636,14.8737498 L13.837003,15.8724998 L12.3567117,16.8649997 L12.3517398,16.8675002 C12.0374178,17.0256265 11.9100387,17.4100567 12.0672132,17.7262017 C12.2243878,18.0423467 12.6066121,18.1705189 12.9209875,18.0124998 L15.466442,16.7324998 C15.6376086,16.6463848 15.7611533,16.4873717 15.8028499,16.2995125 C15.8445466,16.1116533 15.7999676,15.9148963 15.6814629,15.7637498 L13.9090909,13.1112499 L13.9090909,10.64 L17.7272727,10.4 C17.9567683,10.4032642 18.1702336,10.2819998 18.2859286,10.0826414 C18.4016235,9.88328294 18.4016235,9.63671706 18.2859286,9.43735862 C18.1702336,9.23800019 17.9567683,9.11673581 17.7272727,9.11993572 L6.27272727,9.11993572 Z" fill="#FFFFFF" fillRule="nonzero"></Path>
                        </G>
                    </G>
                </G>
            </Svg>)
    }
}
