import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BilliardsMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M5.96 5.96c-.823.797-.987 2.8-.246 3.075.357.138.411.796.357 4.722-.055 4.557-.055 4.557.604 4.886.466.247 2.25.33 6.423.275l5.765-.055.055-5.765c.055-4.173-.028-5.957-.275-6.423-.33-.66-.33-.66-4.886-.604-3.926.054-4.584 0-4.722-.357-.274-.741-2.278-.577-3.074.247zm4.942 4.393c0 .823-1.07.741-1.18-.082-.083-.55 0-.632.549-.55.411.055.631.275.631.632zm6.286.027c.604.247.55.851-.11 1.208-.439.247-.603.22-.713-.137-.165-.412.027-1.235.274-1.235.055 0 .302.082.55.164zm-1.647 6.589c.22.576-.027.933-.686.933-.384 0-.522-.192-.522-.686 0-.796.934-.989 1.208-.247z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
