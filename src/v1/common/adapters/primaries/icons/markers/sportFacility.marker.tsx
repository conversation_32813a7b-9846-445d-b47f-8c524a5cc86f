import React, { PureComponent } from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg';
import { BaseIconProps } from '../baseIcon';

export class SportFacilityMarker extends PureComponent<BaseIconProps> {
    render() {
        const { width } = this.props
        return <Svg width={width} height={width}>
            <Defs>
                <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                    <Stop stopColor="#39b54a" offset="0%"/>
                    <Stop stopColor="#0000ff" offset="100%"/>
                </LinearGradient>
            </Defs>
            <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                <G fill="#fff" transform="translate(5.5,4)">
                    <Path d="M2 2c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM5 2c0 .5.5 1 1 1 .6 0 1-.5 1-1 0-.6-.4-1-1-1-.5 0-1 .4-1 1zM8 2c0 .5.5 1 1.1 1 .5 0 .7-.5.4-1-.3-.6-.8-1-1.1-1-.2 0-.4.4-.4 1zM1.8 4.7C-3.3 6 3.6 8.7 9 7.5c1.7-.4 3-1 3-1.5 0-1.6-6.1-2.4-10.2-1.3zm6 1c-1 .2-2.6.2-3.5 0-1-.3-.2-.5 1.7-.5s2.7.2 1.8.5z" />
                    <Path d="M0 9.1C0 11 2 12 6 12s6-1 5.9-2.9c0-1-.2-1.1-.6-.3-.2.6-.9 1-1.4.6-.5-.3-.9-.1-.9.5 0 1.4-7.1.6-8.1-1-.7-1.1-.9-1.1-.9.2z" />
                </G>
            </G>
        </Svg>
    }

}
