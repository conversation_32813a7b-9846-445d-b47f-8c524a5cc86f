import React, { PureComponent } from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg';
import { BaseIconProps } from '../baseIcon';

export class ProMarker extends PureComponent<BaseIconProps> {
    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#39b54a" offset="0%"/>
                        <Stop stopColor="#0000ff" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                    <G transform="translate(2,2)">
                        <Path
                            d="M6.9 1.6c-1 1.2-1 2-.1 3.4.9 1.5.8 1.9-.8 2.3-1 .2-2.6 1.7-3.5 3.1-1.7 2.5-1.7 2.7 0 4.6 2.8 3.1 4.2 2.5 3.7-1.5-.2-1.9-.9-3.2-1.4-2.9-.5.3-.7 1.8-.4 3.3.6 2.4.5 2.5-.8.7-1.3-1.6-1.3-2.1.1-4.2 2.1-3.2 3.2-3 4 .6.3 1.6.9 3 1.3 3s1-1.4 1.4-3c.7-3.6 1.8-3.8 3.9-.6 1.4 2.1 1.4 2.6.1 4.2-1.3 1.8-1.4 1.7-.8-.7.3-1.5.1-3-.4-3.3-.5-.3-1.2 1-1.4 2.9-.5 4 .9 4.6 3.7 1.5 1.7-1.9 1.7-2.1 0-4.6C14.6 9 13 7.5 12 7.3c-1.6-.4-1.7-.8-.8-2.3.9-1.4.9-2.2-.1-3.4C10.3.7 9.4 0 9 0c-.4 0-1.3.7-2.1 1.6zm3.9 2.2c.2.7-.6 1.2-1.7 1.2C7 5 6.4 3.9 7.7 2.6c.9-.9 2.6-.2 3.1 1.2zM10 9c0 .5-.4 1-1 1-.5 0-1-.5-1-1 0-.6.5-1 1-1 .6 0 1 .4 1 1z"
                            fill="#fff"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
