import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FloorballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G id="sfjB6toPl3" transform="translate(4.000000, 4.000000)" fill="#FFFFFF" fill-rule="nonzero">
                            <G id="Group" transform="translate(8.088889, 7.737500) scale(-1, 1) rotate(-180.000000) translate(-8.088889, -7.737500) translate(0.088889, 0.737500)">
                                <Path d="M4.88888889,12.075 C3.64444444,10.7625 3.55555556,9.975 4.35555556,8.575 C5.33333333,6.65 6.66666667,6.3 9.15555556,7.2625 C11.8222222,8.225 10.3111111,13.7375 7.46666667,13.7375 C6.84444444,13.7375 5.68888889,12.95 4.88888889,12.075 Z M9.24444444,10.15 C9.24444444,9.45 8.35555556,8.8375 7.28888889,8.6625 C5.33333333,8.4 4.44444444,10.0625 5.77777778,11.2875 C6.66666667,12.25 9.24444444,11.375 9.24444444,10.15 Z" id="Shape"></Path>
                                <Path d="M13.2444444,8.925 C11.2,5.6875 11.2,5.6875 5.77777778,6.0375 C0.444444444,6.3 0.355555556,6.2125 0.0888888889,3.9375 C-0.177777778,1.8375 0.177777778,1.4875 3.46666667,0.6125 C5.42222222,0.0875 7.82222222,-0.0875 8.71111111,0.175 C9.6,0.4375 11.6444444,3.0625 13.1555556,5.95 C16.3555556,11.9 16.3555556,13.9125 13.2444444,8.925 Z M1.77777778,3.5 C0.711111111,1.75 0.533333333,2.275 1.33333333,4.55 C1.68888889,5.3375 2.13333333,5.775 2.31111111,5.5125 C2.57777778,5.3375 2.31111111,4.375 1.77777778,3.5 Z M5.24444444,4.375 C5.24444444,3.15 3.55555556,1.05 3.02222222,1.575 C2.31111111,2.275 3.46666667,4.9875 4.44444444,4.9875 C4.88888889,4.9875 5.24444444,4.725 5.24444444,4.375 Z M7.91111111,4.1125 C7.91111111,3.675 7.55555556,3.2375 7.02222222,3.2375 C6.57777778,3.2375 6.13333333,3.675 6.13333333,4.1125 C6.13333333,4.6375 6.57777778,4.9875 7.02222222,4.9875 C7.55555556,4.9875 7.91111111,4.6375 7.91111111,4.1125 Z M10.5777778,4.1125 C10.5777778,3.675 10.2222222,3.2375 9.68888889,3.2375 C9.24444444,3.2375 8.8,3.675 8.8,4.1125 C8.8,4.6375 9.24444444,4.9875 9.68888889,4.9875 C10.2222222,4.9875 10.5777778,4.6375 10.5777778,4.1125 Z" id="Shape"></Path>
                            </G>
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
