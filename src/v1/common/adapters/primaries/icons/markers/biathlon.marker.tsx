import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BiathlonMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <G fill="#FFF" fillRule="nonzero">
                            <Path d="M14.141 5.988c-.494.577-1.235 1.07-1.565 1.153-.329 0-1.235.33-2.058.741-.906.412-1.977.165-2.965-.74C5.988 5.741 5 5.411 5 6.481c0 1.07 4.859 3.624 6.012 3.047.494-.329.33 0-.412.66-.824.658-1.235 1.4-.906 1.646.824.906-1.482 3.047-3.212 3.047-3.047 0-.988 1.977 3.13 3.047 5.682 1.483 8.153 1.153 3.459-.411-2.224-.66-3.953-1.565-3.953-1.812 0-1.153 3.211-3.459 4.035-2.882 1.318.74 1.07 2.058-.33 2.058-.658 0-1.235.33-1.235.824 0 .412 1.07.823 2.388.823 1.318 0 2.965.247 3.706.495.906.329 1.318.082 1.318-.824 0-.906-.741-1.318-2.141-1.318-1.565 0-2.059-.33-1.73-1.235.33-.659.165-1.4-.329-1.73-.494-.329-.33-1.07.494-1.976.906-1.07 1.07-1.647.412-2.059-.494-.33-.741-1.07-.494-1.73.576-1.4.33-1.481-1.07-.164z" />
                            <Path d="M17.024 5.576c-.989.989-.495 2.718.74 2.718.66 0 1.236-.494 1.236-1.153 0-1.4-1.153-2.306-1.976-1.565z" />
                        </G>
                    </G>
                </G>
            </Svg>
        )
    }
}
