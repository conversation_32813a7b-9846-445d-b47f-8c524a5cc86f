import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class SwimmingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle id="Oval" fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path d="M15.5178571,7 C14.4823232,7 13.6428571,7.87055743 13.6428571,8.94444444 C13.6428571,9.46014317 13.8404012,9.95472023 14.1920319,10.3193743 C14.5436626,10.6840284 15.0205762,10.8888889 15.5178571,10.8888889 C16.553391,10.8888889 17.3928571,10.0183315 17.3928571,8.94444444 C17.3928571,7.87055743 16.553391,7 15.5178571,7 L15.5178571,7 Z M9.36655964,7.55555556 C9.2802112,7.55398767 9.19477696,7.57408984 9.11753607,7.61414933 L5.90325057,9.28081611 C5.652121,9.41487694 5.54447862,9.72808883 5.65723997,9.99664425 C5.77000131,10.2651997 6.06443339,10.3968511 6.33014789,10.2975261 L6.32386996,10.3029511 L9.08719321,9.44791667 L10.2559289,11.3630644 L6.32910154,13.6872828 C6.59588725,13.8750606 7.23892857,14.2319878 8.28571429,14.2319878 C9.00142857,14.2319878 9.72109375,13.8849567 10.5541295,13.48329 C11.5564509,12.9999567 12.6903904,12.4574911 14.0655689,12.4286022 L12.6153737,10.90625 C12.6102741,10.8999819 12.6050412,10.8938317 12.5996789,10.8878039 L9.74846554,7.73133678 C9.64934958,7.62155795 9.51155528,7.55813487 9.36655964,7.55555556 Z M14.1785714,13.5332033 C12.9617409,13.5332033 11.9436384,14.0378733 11.0061384,14.4902344 C10.0686384,14.9425956 9.21174089,15.3430989 8.28571429,15.3430989 C7.30433034,15.3430989 6.58831139,15.088415 6.11669923,14.8417967 C5.64508702,14.5951789 5.45228795,14.3860678 5.45228795,14.3860678 C5.31734146,14.2449603 5.12005438,14.1892311 4.93474283,14.2398728 C4.74943128,14.2905145 4.6042485,14.4398336 4.55388344,14.6315828 C4.50351839,14.8233321 4.5556227,15.0283803 4.6905692,15.1694878 C4.6905692,15.1694878 5.03348441,15.5209761 5.63330077,15.8346356 C6.23311718,16.148295 7.12424109,16.45421 8.28571429,16.45421 C9.50254482,16.45421 10.5206473,15.94954 11.4581473,15.4971789 C12.3956473,15.0448178 13.2525448,14.6443144 14.1785714,14.6443144 C16.1214288,14.6443144 17.6083986,15.7782117 17.6083986,15.7782117 C17.7610208,15.9070111 17.9694434,15.9386852 18.1512842,15.8607143 C18.3331251,15.7827433 18.4591186,15.6076761 18.479464,15.4047103 C18.4998095,15.2017446 18.4112325,15.0035458 18.2487443,14.888455 C18.2487443,14.888455 16.5214284,13.5332033 14.1785714,13.5332033 Z" id="Shape" fill="#FFFFFF" fill-rule="nonzero"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
