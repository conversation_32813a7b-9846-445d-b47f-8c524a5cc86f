import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class WalkingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24,
        fillColor: '#FFF'
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <Circle fill="url(#prefix__a)" cx={12} cy={12} r={12} />
                    <G id="icons8-walking-stick-500" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <Path d="M9.72659373,0.00994198312 C9.4725003,0.0260123418 9.23206765,0.109040243 9.09272582,0.245635549 C8.7430056,0.51882616 8.75120234,0.853617985 8.96158074,1.05984968 C9.31130096,1.40267669 10.2812283,1.53927199 9.44244604,2.4311577 L7.56269984,4.29527954 L8.87415068,5.5808808 L10.972472,3.52391878 C11.6008759,2.90790185 12.1609745,3.67390559 12.3713529,4.01673259 C12.5817313,4.42919668 12.3713529,4.76666667 12.0216327,5.45232068 C11.8112543,5.93174299 11.9642569,6.4861587 12.5243555,6.75934863 C13.0844541,7.03253924 13.4997469,6.47276651 13.6390887,6.26653481 C13.710126,6.06030311 13.988809,5.45232068 13.988809,4.76666667 C13.988809,3.94441735 13.9095756,3.31768708 13.0707934,2.49543776 C12.6500366,2.08297437 11.7511458,1.88745533 11.1910472,1.33839662 C10.9123642,1.0652067 10.6364126,0.454546097 10.4260342,0.181355485 C10.2511741,0.0447601793 9.98068715,-0.00612768987 9.72659373,0.00994198312 Z M6.66654177,5.13092036 L2.01089129,9.71623154 C1.7649946,9.64123813 1.49177568,9.70284003 1.33330835,9.90907173 L0,11.1518196 L1.8797462,12.9945148 L3.21305456,11.6874868 C3.41796892,11.4866114 3.45895194,11.2134215 3.36605715,11.0018328 L8.02170763,6.459375 L6.66654177,5.13092036 Z" id="Shape"></Path>
                    </G>
                </G>
            </Svg>
        )
    }
}
