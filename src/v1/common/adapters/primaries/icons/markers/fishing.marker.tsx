import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class FishingMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M16.995 10.72c.003-.054.005-.109.005-.16 0-2.466-2.086-4.484-4.706-4.626V4h-.588v1.934C9.086 6.076 7 8.094 7 10.56c0 .051.002.106.005.16H7c0 .024.006.048.007.071.134 2.397 2.267 6.92 4.699 7.267V20h.588v-1.942c2.432-.347 4.565-4.87 4.7-7.267 0-.023.006-.047.006-.071h-.005zM12 17.176c-1.656 0-3.485-3.14-4.09-5.647.83.612 2.274.977 4.09.977 1.816 0 3.26-.365 4.09-.977-.606 2.507-2.434 5.647-4.09 5.647z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
