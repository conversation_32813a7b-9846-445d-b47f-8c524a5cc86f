import React from 'react'
import Svg, { Circle, Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from '../baseIcon'

export class BaseballMarker extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 24
    }

    render() {
        const { width } = this.props
        return (
            <Svg width={width} height={width}>
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="linearGradient-1">
                        <Stop stopColor="#FCEE21" offset="0%"/>
                        <Stop stopColor="#F0F" offset="100%"/>
                    </LinearGradient>
                </Defs>
                <G stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                    <G>
                        <Circle fill="url(#linearGradient-1)" cx="12" cy="12" r="12"></Circle>
                        <Path
                            d="M17.307 4.026c-.451 0-.878.18-1.202.504l-3.152 3.15c-.62.62-1.53 1.84-2.497 3.13-1.04 1.394-1.511 2.227-2.32 3.052L6.527 15.47l1.032 1.032 1.609-1.609c.835-.852 1.724-1.363 3.17-2.444 1.241-.928 2.412-1.804 3.01-2.402l3.092-3.092c.614-.614.71-1.55.227-2.226a1.676 1.676 0 00-1.361-.703zM8.97 4.03a1.82 1.82 0 00-1.811 1.956 2.69 2.69 0 001.233-.728c.366-.366.61-.783.726-1.22a2.03 2.03 0 00-.148-.008zm.592.102a3.142 3.142 0 01-.85 1.447 3.143 3.143 0 01-1.463.853c.178.524.59.94 1.11 1.126.147-.52.446-1.018.885-1.457.411-.411.898-.687 1.448-.83a1.825 1.825 0 00-1.13-1.139zm1.22 1.589c-.462.125-.87.357-1.216.702-.376.376-.635.799-.763 1.235a1.82 1.82 0 001.985-1.81c0-.042-.004-.085-.006-.127zm-5.199 9.897a.555.555 0 00-.394.16.567.567 0 000 .787l1.249 1.248a.567.567 0 00.787 0 .566.566 0 000-.788l-1.248-1.248a.555.555 0 00-.394-.16z"
                            fill="#FFF"
                            fillRule="nonzero"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
