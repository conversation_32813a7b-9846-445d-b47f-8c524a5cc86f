import React from 'react'
import Svg, { Defs, G, LinearGradient, Path, Stop } from 'react-native-svg'
import { BaseIcon, BaseIconProps } from './baseIcon'

export class NoNetworkIcon extends BaseIcon<BaseIconProps, any> {

    static defaultProps = {
        width: 1036
    }

    protected ratio = 1078 / 1390

    render() {
        const { width } = this.props
        return (
            <Svg
                width={width}
                height={width * this.ratio}
                viewBox="0 0 1078 1390"
            >
                <Defs>
                    <LinearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="prefix__a">
                        <Stop stopColor="#FCEE21" offset="0%" />
                        <Stop stopColor="#F0F" offset="100%" />
                    </LinearGradient>
                </Defs>
                <G fill="none" fillRule="evenodd">
                    <G fillRule="nonzero">
                        <Path
                            d="M1056.707 1252.803l-71.951-72.197 71.95-72.198c28.174-28.352 28.152-74.243-.043-102.552-28.195-28.265-73.88-28.265-102.053-.043l-71.951 72.24-71.908-72.197c-28.195-28.265-73.9-28.265-102.14 0-28.151 28.31-28.195 74.2.044 102.552l71.907 72.154-71.907 72.241c-28.195 28.265-28.239 74.243-.043 102.508 28.195 28.31 73.944 28.31 102.14 0l71.907-72.197 71.908 72.197c28.216 28.31 73.923 28.31 102.096 0 28.217-28.287 28.217-74.199.044-102.508z"
                            fill="#FFCB00"
                        />
                        <Path
                            d="M308.618 179.556c-71.236 73.982-72.688 190.742-4.595 266.552l28.239-28.352c-52.533-60.187-51.038-151.51 4.616-209.826l-28.26-28.374z"
                            fill="url(#prefix__a)"
                        />
                        <Path
                            d="M255.247 142.674l-28.26-28.396c-106.908 109.863-108.316 284.83-4.594 396.608l28.26-28.353C162.47 386.4 163.9 236.848 255.247 142.674z"
                            fill="url(#prefix__a)"
                        />
                        <Path
                            d="M194.34 77.396L166.1 49C24.78 193.417 23.391 424.566 161.485 570.92l28.26-28.353C67.191 411.86 68.578 206.124 194.34 77.396zM735.447 417.756l28.26 28.352c68.093-75.81 66.62-192.57-4.594-266.552l-28.282 28.396c55.675 58.293 57.149 149.617 4.616 209.804z"
                            fill="url(#prefix__a)"
                        />
                        <Path
                            d="M800.463 482.533l28.238 28.353c103.722-111.778 102.314-286.745-4.573-396.608l-28.281 28.396c91.347 94.174 92.777 243.726 4.616 339.86z"
                            fill="url(#prefix__a)"
                        />
                        <Path
                            d="M887.15 542.567l28.26 28.353c138.094-146.375 136.707-377.525-4.615-521.92l-28.26 28.396c125.762 128.706 127.17 334.441 4.616 465.171zM537.734 157.797c-84.846 0-153.655 69.064-153.655 154.273 0 75.767 54.462 138.673 126.175 151.685v925.795h54.938V463.755c71.734-13.012 126.196-75.897 126.196-151.685 0-85.21-68.809-154.273-153.654-154.273z"
                            fill="url(#prefix__a)"
                        />
                    </G>
                </G>
            </Svg>
        )
    }
}
