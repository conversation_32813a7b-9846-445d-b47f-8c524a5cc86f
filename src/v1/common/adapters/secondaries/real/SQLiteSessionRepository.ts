import {Observable} from 'rxjs';
import {fromPromise} from 'rxjs/internal-compatibility';
import {
  ACTIVITY_APP_PREFERENCE_SAVED,
  JWT_TOKEN_KEY,
} from '../../../../helpers/constant.helper';
import {storage} from '../../../../helpers/storage.helper';
import {ApplicationContext} from '../../../configuration/application.context';
import {AppPreference} from '../../../domain/entities/appPreference';
import {SessionRepository} from '../../../domain/gateway/sessionRepository';
import {AppPreferenceBuilder} from '../../../usecases/applicationPreference/appPreference.builder';

const db = ApplicationContext.getInstance().db();

export class SQLiteSessionRepository implements SessionRepository {
  initStorage(): Observable<void> {
    const initialisationPromise = new Promise<void>((resolve, reject) => {
      db.transaction(
        transaction => {
          transaction.executeSql('DROP TABLE IF EXISTS config');
          transaction.executeSql('DROP TABLE IF EXISTS user');
          transaction.executeSql('DROP TABLE IF EXISTS business');
          transaction.executeSql('DROP TABLE IF EXISTS preference');
          transaction.executeSql('DROP TABLE IF EXISTS migration_versions');
          transaction.executeSql('DROP TABLE IF EXISTS Instant_Shedule_Table');
        },
        error => reject(error),
        () => resolve(void 0),
      );
    });
    return fromPromise(initialisationPromise);
  }

  saveAuthorizationToken(token: string): Observable<void> {
    storage.set(JWT_TOKEN_KEY, token);
    const saveTokenPromise = new Promise<void>((resolve, reject) => {
      db.transaction(transactionInsertion => {
        transactionInsertion.executeSql(
          'INSERT INTO config (token) values (?)',
          [token],
          (transaction, result) => {
            if (result.insertId) resolve(void 0);
          },
          err => {
            reject(err);
            return true;
          },
        );
      });
    });

    return fromPromise(saveTokenPromise);
  }

  getAuthorizationToken(): Observable<string> {
    const getTokenPromise = new Promise<string>((resolve, reject) => {
      db.transaction(
        transactionSelect => {
          transactionSelect.executeSql(
            'SELECT * from config',
            [],
            (transaction, result) => {
              if (result.rows.length > 0)
                resolve(result.rows.item(result.rows.length - 1).token);
            },
            err => {
              console.log('PPPPPPP>>>>>>>> err ', err);
              reject(err);
              return true;
            },
          );
        },
        err => {
          console.log('PPPPPPP>>>>>>>> err ', err);
          reject(err);
          return true;
        },
      );
    });

    return fromPromise(getTokenPromise);
  }

  retrieveApplicationPreferences(): Observable<AppPreference> {
    const retrievePreferencePromise = new Promise<AppPreference>(
      (resolve, reject) =>
        db.transaction(
          transactionSelect =>
            transactionSelect.executeSql(
              'SELECT * from preference',
              [],
              (transaction, result) => {
                if (result.rows.length > 0) {
                  const preferenceBuilder = new AppPreferenceBuilder();
                  for (let i = 0; i < result.rows.length; i++) {
                    if (result.rows.item(i).key === 'foryou_categories')
                      preferenceBuilder.withForYouCategories([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_days')
                      preferenceBuilder.withForYouDays([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_cities')
                      preferenceBuilder.withForYouCities([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_business')
                      preferenceBuilder.withForYouBusiness([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_price')
                      preferenceBuilder.withForYouPrice(
                        result.rows.item(i).value,
                      );
                    if (result.rows.item(i).key === 'map_categories')
                      preferenceBuilder.withMapCategories([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'map_price')
                      preferenceBuilder.withMapPrice(result.rows.item(i).value);
                    if (result.rows.item(i).key === 'map_business')
                      preferenceBuilder.withMapBusiness([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'language')
                      preferenceBuilder.withLanguage(result.rows.item(i).value);
                  }
                  storage.get(ACTIVITY_APP_PREFERENCE_SAVED).then(saved => {
                    if (saved) {
                      try {
                        saved =
                          typeof saved === 'string' ? JSON.parse(saved) : saved;
                        let days = saved.find(d => d.key === 'map_days');
                        days = days ? days.value : [];
                        preferenceBuilder.withMapDays(days || []);
                      } catch (error) {
                        console.error(error);
                      }
                    }
                    resolve(preferenceBuilder.build());
                  });
                } else reject(0);
              },
              err => {
                console.log('PPPPPPP>>>>>>>> err ', err);
                reject(err);
                return true;
              },
            ),
          error => reject(error),
        ),
    );

    return fromPromise(retrievePreferencePromise);
  }

  saveApplicationPreferences(preference: AppPreference): Observable<void> {
    const savePreferencePromise = new Promise<void>((resolve, reject) =>
      db.transaction(transactionInsertion => {
        preference.toArray().map((item: {key: string; value: any}) => {
          if (item.value !== undefined)
            if (
              item.key === 'foryou_price' ||
              item.key === 'map_price' ||
              item.key === 'language'
            )
              transactionInsertion.executeSql(
                `UPDATE preference set value = ? WHERE key = "${item.key}"`,
                [item.value],
                () => resolve(void 0),
                err => {
                  console.log('PPPPPPP>>>>>>>> err ', err);
                  reject(err);
                  return true;
                },
              );
            else
              transactionInsertion.executeSql(
                `UPDATE preference set value = ? WHERE key = "${item.key}"`,
                [JSON.stringify(item.value)],
                () => resolve(void 0),
                err => {
                  console.log('PPPPPPP>>>>>>>> err ', err);
                  reject(err);
                  return true;
                },
              );
        });
      }),
    );
    return fromPromise(savePreferencePromise);
  }

  retrieveApplicationPreferences2(): Observable<AppPreference> {
    const retrievePreferencePromise = new Promise<AppPreference>(
      (resolve, reject) =>
        db.transaction(
          transactionSelect =>
            transactionSelect.executeSql(
              'SELECT * from preference',
              [],
              (transaction, result) => {
                if (result.rows.length > 0) {
                  const preferenceBuilder = new AppPreferenceBuilder();
                  for (let i = 0; i < result.rows.length; i++) {
                    if (result.rows.item(i).key === 'foryou_categories2')
                      preferenceBuilder.withForYouCategories([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_days2')
                      preferenceBuilder.withForYouDays([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_cities2')
                      preferenceBuilder.withForYouCities([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_business2')
                      preferenceBuilder.withForYouBusiness([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'foryou_price2')
                      preferenceBuilder.withForYouPrice(
                        result.rows.item(i).value,
                      );
                    if (result.rows.item(i).key === 'map_categories2')
                      preferenceBuilder.withMapCategories([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'map_price2')
                      preferenceBuilder.withMapPrice(result.rows.item(i).value);
                    if (result.rows.item(i).key === 'map_business2')
                      preferenceBuilder.withMapBusiness([
                        ...JSON.parse(result.rows.item(i).value),
                      ]);
                    if (result.rows.item(i).key === 'language2')
                      preferenceBuilder.withLanguage(result.rows.item(i).value);
                  }
                  resolve(preferenceBuilder.build());
                } else reject(0);
              },
              err => {
                console.log('PPPPPPP>>>>>>>> err ', err);
                reject(err);
                return true;
              },
            ),
          error => reject(error),
        ),
    );

    return fromPromise(retrievePreferencePromise);
  }

  saveApplicationPreferences2(preference: AppPreference): Observable<void> {
    const savePreferencePromise = new Promise<void>((resolve, reject) =>
      db.transaction(transactionInsertion => {
        preference.toArray2().map((item: {key: string; value: any}) => {
          if (item.value !== undefined)
            if (
              item.key === 'foryou_price2' ||
              item.key === 'map_price2' ||
              item.key === 'language'
            )
              transactionInsertion.executeSql(
                `UPDATE preference set value = ? WHERE key = "${item.key}"`,
                [item.value],
                () => resolve(void 0),
                err => {
                  console.log('PPPPPPP>>>>>>>> err ', err);
                  reject(err);
                  return true;
                },
              );
            else
              transactionInsertion.executeSql(
                `UPDATE preference set value = ? WHERE key = "${item.key}"`,
                [JSON.stringify(item.value)],
                () => resolve(void 0),
                err => {
                  console.log('PPPPPPP>>>>>>>> err ', err);
                  reject(err);
                  return true;
                },
              );
        });
      }),
    );
    return fromPromise(savePreferencePromise);
  }
}
