import {fromPromise} from 'rxjs/internal/observable/fromPromise';
import {Observable} from 'rxjs/Rx';
import {ApplicationContext} from '../../../configuration/application.context';
import {MigrationEntity} from '../../../domain/entities/migrationVersion';
import {MigrationQuery} from '../../../domain/entities/types/AppTypes';
import {MigrationRepository} from '../../../domain/gateway/migrationRepository';
import {MigrationsHelper} from './migrations/migrations.helper';
import {SQLError} from 'react-native-sqlite-2';

const database = ApplicationContext.getInstance().db();

export class SqlMigrationRepository
  extends MigrationsHelper
  implements MigrationRepository
{
  retrieveMigrationVersions(): Observable<MigrationEntity[]> {
    const retrieveMigrationsPromise = new Promise<MigrationEntity[]>(
      (resolve, reject) => {
        database.transaction(
          selectTX => {
            selectTX.executeSql(
              'SELECT * FROM migration_versions',
              [],
              (transaction, result) => {
                const migrations = [];
                if (result.rows.length > 0)
                  for (let i = 0; i < result.rows.length; i++)
                    migrations.push(
                      new MigrationEntity(
                        result.rows.item(i).version,
                        result.rows.item(i).executed_at,
                      ),
                    );
                resolve(migrations);
              },
              (tnx, err: SQLError) => {
                console.log('===++++== MMM >> ', err.message);
                reject(err);
                return false;
              },
            );
          },
          (err: SQLError) => reject(err),
        );
      },
    );
    return fromPromise(retrieveMigrationsPromise);
  }

  executeMigrations(migrations: MigrationQuery[]): Observable<string> {
    const now = new Date().toJSON();
    const promise = new Promise<string>((resolve, reject) => {
      database.transaction(
        transaction => {
          migrations.map((migration: MigrationQuery) => {
            transaction.executeSql(migration.query);
            transaction.executeSql(
              'INSERT INTO migration_versions values (?,?)',
              [migration.version, now],
            );
          });
        },
        error => reject(error),
        () => resolve('SUCCESS'),
      );
    });
    return fromPromise(promise);
  }
}
