import {EventRegister} from 'react-native-event-listeners';
import {AjaxError, ajax} from 'rxjs/ajax';
import {Observable} from 'rxjs/internal/Observable';
import {AjaxResponse} from 'rxjs/Rx';
import {API_ERROR} from '../../../../helpers/constant.helper';
import {HttpClient} from './HttpClient';
import {catchError} from 'rxjs/operators';

export class ObservableAjaxHttpClient implements HttpClient {
  get<R>(url: string, headers?: object): Observable<R> {
    return ajax.getJSON<R>(url, headers).catch(err => {
      setTimeout(() => {
        EventRegister.emit(API_ERROR, err.response.message);
      }, 400);

      return Observable.throwError(err);
    });
  }
  post(url: string, body?: any, headers?: object): Observable<AjaxResponse> {
    return ajax.post(url, body, headers).pipe(
      catchError((err: AjaxError) => {
        setTimeout(() => {
          EventRegister.emit(API_ERROR, err.response?.message);
        }, 400);

        return Observable.throwError(err);
      }),
    );
  }
}
