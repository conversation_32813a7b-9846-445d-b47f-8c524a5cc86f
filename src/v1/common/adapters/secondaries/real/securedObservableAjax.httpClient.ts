import {EventRegister} from 'react-native-event-listeners';
import {ajax} from 'rxjs/ajax';
import {Observable} from 'rxjs/internal/Observable';
import {map, switchMap} from 'rxjs/internal/operators';
import {AjaxResponse} from 'rxjs/Rx';
import {API_ERROR, USER_DISABLED} from '../../../../helpers/constant.helper';
import {SessionRepository} from '../../../domain/gateway/sessionRepository';
import {HttpClient} from './HttpClient';

export class SecuredObservableAjaxHttpClient implements HttpClient {
  private sessionRepository: SessionRepository;

  constructor(sessionRepository: SessionRepository) {
    this.sessionRepository = sessionRepository;
  }

  get<R>(url: string, headers?: object): Observable<R> {
    console.log('=== SecuredObservableAjaxHttpClient.get === ');

    this.sessionRepository.getAuthorizationToken().pipe(
      map((token: string) => {
        console.log(
          '=== SecuredObservableAjaxHttpClient.get 00 === token ',
          token,
        );

        return ajax
          .getJSON<R>(url, this.getHeaders(token, headers))
          .toPromise()
          .then((res: R) => {
            console.log(
              '=== SecuredObservableAjaxHttpClient.get === res ',
              res,
            );
          })
          .catch(err => {
            console.log(
              '=== SecuredObservableAjaxHttpClient.get === err ',
              err,
            );
            EventRegister.emit(API_ERROR, err.response.message);
            return Observable.throwError(err);
          });
      }),
    );
    return this.sessionRepository.getAuthorizationToken().pipe(
      switchMap((token: string) => {
        return ajax
          .getJSON<R>(url, this.getHeaders(token, headers))
          .catch(err => {
            EventRegister.emit(API_ERROR, err.response.message);
            return Observable.throwError(err);
          });
      }),
    );
  }

  post(url: string, body?: any, headers?: object): Observable<AjaxResponse> {
    return this.sessionRepository.getAuthorizationToken().pipe(
      switchMap((token: string) => {
        return ajax
          .post(url, body, this.getHeaders(token, headers))
          .catch(err => {
            EventRegister.emit(API_ERROR, err.response.message);
            return Observable.throwError(err);
          });
      }),
    );
  }

  delete(url: string, headers?: object): Observable<AjaxResponse> {
    return this.sessionRepository
      .getAuthorizationToken()
      .pipe(
        switchMap((token: string) =>
          ajax.delete(url, this.getHeaders(token, headers)),
        ),
      );
  }

  private getHeaders(token: string, headers?: object) {
    return {
      ...headers,
      Authorization: `${token}`,
    };
  }
}
