import {Observable} from 'rxjs/index';
import {fromPromise} from 'rxjs/internal/observable/fromPromise';
import {ApplicationContext} from '../../../../configuration/application.context';
import {SqlError} from '../../../../domain/entities/types/AppTypes';

const db = ApplicationContext.getInstance().db();

export class MigrationsHelper {
  createMigrationTable(): Observable<string> {
    const createPromise = new Promise<string>((resolve, reject) => {
      db.transaction(
        createTX => {
          createTX.executeSql(
            'CREATE TABLE migration_versions (version INTEGER PRIMARY KEY, executed_at TEXT NOT NULL)',
            [],
            (tnx, res) => {
              resolve('');
            },
            (tnx, error: SqlError) => {
              reject(error);
              return true;
            },
          );
        },
        (error: SqlError) => reject(error),
      );
    });
    return fromPromise(createPromise);
  }

  executeQuery(query: string): Observable<any> {
    const promise = new Promise<void>((resolve, reject) => {
      db.transaction(
        transaction => {
          transaction.executeSql(
            query,
            [],
            (tnx, res) => resolve(),
            (tnx, err: SqlError) => {
              reject(err);
              return false;
            },
          );
        },
        error => reject(error),
        () => resolve(),
      );
    });
    return fromPromise(promise);
  }

  updateVersion(version: number): Observable<any> {
    const promise = new Promise<void>((resolve, reject) => {
      db.transaction(
        transaction => {
          transaction.executeSql(
            'INSERT INTO migration_versions values ( ?, ?)',
            [version, new Date().toJSON()],
            () => {
              resolve();
            },
            (tnx, err: SqlError) => {
              reject(err);
              return false;
            },
          );
        },
        error => reject(error),
        () => resolve(),
      );
    });
    return fromPromise(promise);
  }
}
