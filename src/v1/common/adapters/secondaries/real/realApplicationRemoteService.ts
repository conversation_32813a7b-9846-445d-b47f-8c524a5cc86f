import Config from "react-native-config";
import { of } from "rxjs/index";
import { catchError, map } from "rxjs/operators";
import { Observable } from "rxjs/Rx";
import { ApplicationContextDependenciesFactory } from "../../../configuration/applicationContextDependencies.factory";
import { Coordinates } from "../../../domain/entities/Coordinates";
import { ApplicationRemoteService } from "../../../domain/gateway/applicationRemoteService";
import { ObservableAjaxHttpClient } from "./observableAjax.httpClient";
import { SecuredObservableAjaxHttpClient } from "./securedObservableAjax.httpClient";

export class RealApplicationRemoteService implements ApplicationRemoteService {
  geocodeAddress(
    zipCode: string,
    city: string,
    country,
    address: string
  ): Observable<Coordinates> {
    const ajaxClient = new ObservableAjaxHttpClient();

    const key = "AIzaSyDq3EfG7M6rh2ajWdFZ5axtrWU8efmxstI";
    const URL = `https://maps.googleapis.com/maps/api/geocode/json?address=
        ${encodeURI(address)}+${encodeURI(city)}+${encodeURI(
      country
    )}+${zipCode}&region=fr&key=${key}`;

    return ajaxClient.get(URL).pipe(
      map((response: any) => {
        if (response.status !== "OK") {
          throw new Error("ERROR");
        } else {
          const value1 = response.results[0].geometry.location.lat.toString();
          const parts1 = value1.split(".");
          const parts21 =
            parts1[1].length > 7 ? parts1[1].substring(0, 7) : parts1[1];

          const value = response.results[0].geometry.location.lng.toString();
          const parts = value.split(".");
          const parts2 =
            parts[1].length > 7 ? parts[1].substring(0, 7) : parts[1];

          return {
            latitude: parseFloat(`${parts1[0]}.${parts21}` as any),
            longitude: parseFloat(`${parts[0]}.${parts2}` as any),
          };
        }
      }),
      catchError((err) => Observable.throwError(err))
    );
  }

  loadApplicationCities(): Observable<string[]> {
    const url = `${Config.API_URL}/v1/instants/cities`;
    return new SecuredObservableAjaxHttpClient(
      ApplicationContextDependenciesFactory.sessionRepository()
    )
      .get(url)
      .pipe(
        map((response: { data: string[] }) => response.data),
        catchError((err) => Observable.throwError(err))
      );
  }

  sendReport(error: string): Observable<void> {
    const url = `${Config.API_URL}/v1/migration/log=` + error;
    //const body = new FormData()
    //body.append('error', JSON.stringify(error))
    return new SecuredObservableAjaxHttpClient(
      ApplicationContextDependenciesFactory.sessionRepository()
    )
      .get(url)
      .pipe(
        map(() => void 0),
        catchError(() => of("ERROR"))
      );
  }
}
