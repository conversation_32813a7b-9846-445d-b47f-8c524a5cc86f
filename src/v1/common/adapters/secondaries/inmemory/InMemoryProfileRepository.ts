import { of } from 'rxjs';
import { Observable } from 'rxjs/Rx';
import { Profile } from '../../../../accountcontext/profilecontext/domaine/entities/profile';
import { ProfileBuilder } from '../../../../accountcontext/profilecontext/domaine/entities/profile.builder';
import { ProfileRepository } from '../../../../accountcontext/profilecontext/domaine/gateway/profileRepository';
import { ApplicationContext } from '../../../configuration/application.context';

const moment = ApplicationContext.getInstance().momentJs()

export class InMemoryProfileRepository implements ProfileRepository {

    saveLocalProfile(profile: Profile): Observable<void> {
        return (of(void 0))
    }

    loadLocalProfile(): Observable<Profile> {
        return (of(new ProfileBuilder()
            .withFirstName('Jhon')
            .withLastName('Doh')
            .withPhoneNumber('+***********')
            .withBirthDate(moment('2000-01-01').format('YYYY-MM-DD'))
            .build()))
    }

    updateLocalProfile(profile: Profile): Observable<void> {
        return (of(void 0))
    }

    updateLocalPhotoURL(photoURL: string, phoneNumber): Observable<void> {
        return of(void 0)
    }
}
