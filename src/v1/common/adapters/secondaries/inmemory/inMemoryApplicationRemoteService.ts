import {of} from 'rxjs/index';
import {Observable} from 'rxjs/Rx';
import {Coordinates} from '../../../domain/entities/Coordinates';
import {ApplicationRemoteService} from '../../../domain/gateway/applicationRemoteService';

export class InMemoryApplicationRemoteService
  implements ApplicationRemoteService
{
  geocodeAddress(
    zipCode: string,
    city: string,
    country: string,
    address: string,
  ): Observable<Coordinates> {
    return of({latitude: 48.9061441, longitude: 2.3026598});
  }

  loadApplicationCities(): Observable<string[]> {
    return of(['<PERSON>nis', 'Paris', 'Clich<PERSON>', '<PERSON>st<PERSON>', '<PERSON>uss<PERSON>', 'Toulouse']);
  }

  sendReport(error: string): Observable<void> {
    console.log('======= ERRRR', error);
    return of(void 0);
  }
}
