import { Observable, of } from "rxjs";
import { AppPreference } from "../../../domain/entities/appPreference";
import { SessionRepository } from "../../../domain/gateway/sessionRepository";

export class InMemorySessionRepository implements SessionRepository {
  initStorage(): Observable<void> {
    return of(void 0);
  }
  saveAuthorizationToken(token: string): Observable<void> {
    return of(void 0);
  }

  getAuthorizationToken(): Observable<string> {
    return of("JEUEEJEDJsvhhdvhsvvhevhs");
  }

  retrieveApplicationPreferences(): Observable<AppPreference> {
    return of(void 0);
  }

  saveApplicationPreferences(filter: AppPreference): Observable<void> {
    return of(void 0);
  }

  retrieveApplicationPreferences2(): Observable<AppPreference> {
    return of(void 0);
  }

  saveApplicationPreferences2(filter: AppPreference): Observable<void> {
    return of(void 0);
  }
}
