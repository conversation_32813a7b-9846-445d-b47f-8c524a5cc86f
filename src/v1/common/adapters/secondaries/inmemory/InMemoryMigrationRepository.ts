import { Observable } from 'rxjs/Rx';
import { MigrationEntity } from '../../../domain/entities/migrationVersion';
import { MigrationQuery } from '../../../domain/entities/types/AppTypes';
import { MigrationRepository } from '../../../domain/gateway/migrationRepository';

export class InMemoryMigrationRepository implements MigrationRepository {

    createMigrationTable(): Observable<string> {
        return undefined;
    }

    executeMigrations(migrations: MigrationQuery[]): Observable<string> {
        return undefined;
    }

    retrieveMigrationVersions(): Observable<MigrationEntity[]> {
        return undefined;
    }
}
