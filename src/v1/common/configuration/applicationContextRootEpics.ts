import { combineEpics } from 'redux-observable';
import { geocodeAddressEpic } from '../usecases/addressgeocode/geocodeaddress.epic';
import { applicationCitiesEpic } from '../usecases/applicationCities/applicationCities.epic';
import {
    retrieveApplicationPreferenceEpic,
    saveApplicationPreferenceEpic
} from '../usecases/applicationPreference/applicationPreference.epic';
import { initStorageEpic } from '../usecases/applicationStorage/applicationStorage.epic';
import { authenticatingApplicationUserEpic } from '../usecases/applicationuser/applicationuser.epic';
import { migrationEpic } from '../usecases/migrations/migration.epic';

export const ApplicationContextRootEpics = combineEpics(
    authenticatingApplicationUserEpic,
    geocodeAddressEpic,
    initStorageEpic,
    applicationCitiesEpic,
    saveApplicationPreferenceEpic,
    retrieveApplicationPreferenceEpic,
    migrationEpic
)
