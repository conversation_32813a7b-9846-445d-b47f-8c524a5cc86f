import {i18nActivitiesFR} from './fr/fr.i18n.activities';
import {i18nCountriesFR} from './fr/fr.i18n.countries';
import {i18nTypesFR} from './fr/fr.i18n.types';

export const Commoni18nFR = {
  title_no_gps: 'La géolocalisation est désactivée',
  open_setting: 'Modifier les paramètres',
  description_no_gps:
    "Veuillez activer votre géolocalisation afin de profiter pleinement de l'application sportaabe.",
  title_no_network: 'Aucune connexion internet',
  description_no_network:
    "Veuillez vérifier votre connexion internet afin de profiter pleinement de l'application sportaabe.",
  open_android_location_settings:
    "Pour continuer, activez la localisation de l'appareil, qui utilise le service de localisation de Google",
  ok: 'OK',
  validate: 'Valider',
  cancel: 'Annuler',
  send_feedback: 'Contactez-nous',
  about: 'À Propos',
  logout: 'Déconnexion',
  aboutContent:
    "sportaabe a pour mission de faciliter l'accès au sport à tous," +
    ' et ainsi promouvoir la santé et le bien-être de chacun.\n' +
    'Grâce à sa plateforme innovante, elle met la technologie à la disposition de la communauté sportive. \n' +
    'Cette offre, conçue pour tous types de profils utilisateurs,' +
    " leur permet d'organiser ou de participer à des activités adaptées à leurs attentes et envies.",
  link_privacy: 'Politique de Confidentialité',
  links: 'Liens : ',
  link_cgu: "Conditions Générales d'Utilisation",
  picture: 'Image',
  splash_text: 'Découvrez les évènements\nsportifs autour de vous',
  subject_email: 'Soumettre une activité ou proposer une amélioration',
  body_email: numBuild => `Bonjour,\nVoici une idée de bon plan :
    \n- Lieu :\n- Adresse :\n- Horaires :\n- Description du bon plan :\n\nVoici une idée d’amélioration (${numBuild}) :\n- Description :`,
  instant_category: 'Catégorie',
  photo: 'Photo',
  library: 'Galerie',
  filter: 'Filtres',
  edit: 'Modifier',
  adjust_photo: 'Ajuster',
  done: 'Terminé',
  contrast: 'Contraste',
  brightness: 'Luminosité',
  saturation: 'Saturation',
  wramth: 'Chaleur',
  crop_rotate: 'Ajuster',
  error_message: 'Une erreur serveur est survenue',
  search: 'Rechercher',
  default: 'Par défaut',
  error_db_migration:
    'une erreur est survenue en essayant de mettre a jour votre application\n\n. Merci de relogger',
  languages: 'Langue',
  change_lang: 'Changer la langue',
  french: 'Français',
  english: 'Anglais',
  restart_app: "Relancer l'appli",
  restart_app_description:
    "Pour modifier la langue, vous devez fermer puis rouvrir l'appli.",
  restart: 'Relancer',
  required_message: 'Obligatoire',
  settings: {
    categories: i18nActivitiesFR,
    types: i18nTypesFR,
    countries: i18nCountriesFR,
  },
};
