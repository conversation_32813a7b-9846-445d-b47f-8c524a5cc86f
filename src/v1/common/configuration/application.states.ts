import { ApplicationUser } from "../domain/entities/ApplicationUser";
import { AppPreference } from "../domain/entities/appPreference";
import { Coordinates } from "../domain/entities/Coordinates";
import { SqlError } from "../domain/entities/types/AppTypes";

export interface ApplicationSettingsState {
  distanceRadius: number;
  retrievalRadius: number;
  contactEmail: string;
  distanceFilter: number;
  appLang: string;
}

export interface ApplicationStatusState {
  network: boolean;
  gps: boolean;
  publicBusinessId: string;
}

export interface ApplicationUIState {
  instantDetailsModal: { isVisible: boolean; instantId: string };
  imageFullScreenModal: { isVisible: boolean; url: string };
  browserModal: { isVisible: boolean; url: string };
}

export interface ApplicationUserState {
  applicationUser: ApplicationUser;
  authentication: { loading: boolean; isAuthenticated: boolean };
}

export interface ApplicationAddressGeocodingState {
  loading: boolean;
  error: string;
  success: boolean;
  coordinates: Coordinates;
}

export interface ApplicationCitiesState {
  loading: boolean;
  cities: string[];
  error: any;
}

export interface ApplicationPreferenceState {
  preference: AppPreference;
  preference2: AppPreference;
  loading: boolean;
  success: boolean;
}

export interface MigrationState {
  loading: boolean;
  success: boolean;
  error: SqlError;
}

export interface ApplicationState {
  userPosition: Coordinates;
  status: ApplicationStatusState;
  settings: ApplicationSettingsState;
  ui: ApplicationUIState;
  addressGeocoding: ApplicationAddressGeocodingState;
  applicationUser: ApplicationUserState;
  applicationCities: ApplicationCitiesState;
  applicationPreference: ApplicationPreferenceState;
  migration: MigrationState;
}
