import {WebsqlDatabase} from 'react-native-sqlite-2';

export class ApplicationContext {
  private static instance: ApplicationContext;
  private _momentJs;
  private _db;

  private constructor() {}

  static getInstance(): ApplicationContext {
    if (!ApplicationContext.instance) {
      ApplicationContext.instance = new ApplicationContext();
      return ApplicationContext.instance;
    }
    return ApplicationContext.instance;
  }

  momentJs() {
    return this._momentJs;
  }

  db(): WebsqlDatabase {
    return this._db;
  }
}
