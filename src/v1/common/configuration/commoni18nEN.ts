import {i18nActivitiesEN} from './en/en.i18n.activities';
import {i18nCountriesEN} from './en/en.i18n.countries';
import {i18nTypesEN} from './en/en.i18n.types';

export const Commoni18nEN = {
  title_no_gps: 'Geolocation is disabled',
  open_setting: 'Modify the parameters',
  description_no_gps:
    'Check your geolocation to take full advantage of the sportaabe application.',
  title_no_network: 'No internet connection',
  description_no_network:
    'Check your internet connection to take full advantage of the sportaabe application.',
  open_android_location_settings:
    "To continue, turn on device location, which uses Google's location service",
  ok: 'OK',
  validate: 'Validate',
  cancel: 'Cancel',
  send_feedback: 'Contact Us',
  about: 'About',
  logout: 'Log out',
  aboutContent:
    'sportaabe mission is to facilitate access to sport for all and promote the health and well-being of all.' +
    '\nThanks to its innovative platform, it makes technology accessible to the sports community. This offer, designed for all types of user profiles,' +
    ' helps organize or participate in activities adapted to their expectations and desires.',
  link_privacy: 'Privacy policy',
  links: 'Links : ',
  link_cgu: 'Terms of Service',
  picture: 'Picture',
  splash_text: 'Discover the sports activities around you',
  subject_email: 'Submit an Activity or suggest an improvement',
  body_email: numBuild => `Hello,\nHere is a good idea:
    \n- Location :\n- Address :\n- Hours :\n- Description of the excellent plan :\n\nHere is an idea for development (${numBuild}) :\n- Description :`,
  instant_category: 'Category',
  photo: 'Photo',
  library: 'Gallery',
  filter: 'Filters',
  edit: 'Edit',
  adjust_photo: 'Adjust',
  done: 'Finished',
  contrast: 'Contrast',
  brightness: 'Brightness',
  saturation: 'Saturation',
  wramth: 'Heat',
  crop_rotate: 'Adjust',
  error_message: 'Server error',
  search: 'Search',
  default: 'By default',
  languages: 'Language',
  change_lang: 'Change language',
  french: 'French',
  english: 'English',
  restart_app: 'Restart Required',
  restart_app_description: 'To change languages, close the app and restart it.',
  restart: 'Restart',
  error_db_migration:
    'An error occurred while trying to update your application. Please relog',
  required_message: 'Required',
  settings: {
    categories: i18nActivitiesEN,
    types: i18nTypesEN,
    countries: i18nCountriesEN,
  },
};
