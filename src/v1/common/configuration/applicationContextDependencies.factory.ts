import Config from 'react-native-config';
import {InMemoryApplicationRemoteService} from '../adapters/secondaries/inmemory/inMemoryApplicationRemoteService';
import {InMemoryMigrationRepository} from '../adapters/secondaries/inmemory/InMemoryMigrationRepository';
import {InMemorySessionRepository} from '../adapters/secondaries/inmemory/InMemorySessionRepository';
import {RealApplicationRemoteService} from '../adapters/secondaries/real/realApplicationRemoteService';
import {SQLiteSessionRepository} from '../adapters/secondaries/real/SQLiteSessionRepository';
import {SqlMigrationRepository} from '../adapters/secondaries/real/SqlMigrationRepository';
import {ApplicationRemoteService} from '../domain/gateway/applicationRemoteService';
import {MigrationRepository} from '../domain/gateway/migrationRepository';
import {SessionRepository} from '../domain/gateway/sessionRepository';

export class ApplicationContextDependenciesFactory {
  static sessionRepository(): SessionRepository {
    /*switch (Config.ENV) {
            case 'prod':
            case 'staging':
            case 'dev':
                return new SQLiteSessionRepository()
            default:
                return new InMemorySessionRepository()
        }*/
    return new SQLiteSessionRepository();
  }

  static applicationRemoteService(): ApplicationRemoteService {
    /*switch (Config.ENV) {
      case 'prod':
      case 'staging':
      case 'dev':
        return new RealApplicationRemoteService();
      default:
        return new InMemoryApplicationRemoteService();
    }*/
    return new RealApplicationRemoteService();
  }

  static migrationRepository(): MigrationRepository {
    /* switch (Config.ENV) {
      case 'prod':
      case 'staging':
      case 'dev':
        return new SqlMigrationRepository();
      default:
        return new InMemoryMigrationRepository();
    }*/
    return new SqlMigrationRepository();
  }
}
