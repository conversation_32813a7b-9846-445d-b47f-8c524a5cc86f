export class ApplicationUser {
  constructor(
    private _phoneNumber: string,
    private _firstName: string,
    private _lastName: string,
    private _avatar: string,
    private _email: string,
    private _birthDate: string,
    private _loginType: string,
    private _spentAmount: number,
    private _fidelityBalance: number,
    private _spentFidelityAmount: number,
    private _uuid: string,
    private _isAdmin: boolean,
    private _language: string,
  ) {}
  get uuid(): string {
    return this._uuid;
  }
  get isAdmin(): boolean {
    return this._isAdmin;
  }
  get phoneNumber(): string {
    return this._phoneNumber;
  }
  get firstName(): string {
    return this._firstName;
  }
  get lastName(): string {
    return this._lastName;
  }
  get avatar(): string {
    return this._avatar;
  }
  get displayName(): string {
    if (this._firstName && this._lastName) {
      return this._firstName + ' ' + this._lastName;
    } else if (this._firstName) {
      return this._firstName;
    } else if (this._lastName) {
      return this._lastName;
    }
    return '';
  }
  get email(): string {
    return this._email;
  }
  get birthDate(): string {
    return this._birthDate;
  }
  get loginType(): string {
    return this._loginType;
  }

  get spentAmount(): number {
    return this._spentAmount;
  }
  get fidelityBalance(): number {
    return this._fidelityBalance;
  }
  get spentFidelityAmount(): number {
    return this._spentFidelityAmount;
  }
  get language(): string {
    return this._language;
  }
}
