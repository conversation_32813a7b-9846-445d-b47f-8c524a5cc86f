export class AppPreference {
  constructor(
    private _forYouCategories: number[],
    private _forYouCities: string[],
    private _forYouDays: string[],
    private _forYouBusiness: string[],
    private _forYouPrice: "free" | "paid" | "all",
    private _forYouName: string,
    private _mapCategories: number[],
    private _mapDays: string[],
    private _mapPrice: "free" | "paid" | "all",
    private _mapBusiness: string[],
    private _mapName: string,
    private _language: "en" | "fr" | "any"
  ) {}

  get forYouCategories(): number[] {
    return this._forYouCategories;
  }

  get forYouDays(): string[] {
    return this._forYouDays;
  }

  get forYouName(): string {
    return this._forYouName;
  }

  setForYouDays(days: string[]): void {
    this._forYouDays = days;
  }

  setMapDays(days: string[]): void {
    this._mapDays = days;
  }

  get forYouCities(): string[] {
    return this._forYouCities;
  }

  get forYouBusiness(): string[] {
    return this._forYouBusiness;
  }

  get forYouPrice(): "free" | "paid" | "all" {
    return this._forYouPrice;
  }

  get mapCategories(): number[] {
    return this._mapCategories;
  }
  get mapDays(): string[] {
    return this._mapDays;
  }

  get mapPrice(): "free" | "paid" | "all" {
    return this._mapPrice;
  }

  get mapName(): string {
    return this._mapName;
  }

  get mapBusiness(): string[] {
    return this._mapBusiness;
  }

  get language(): "en" | "fr" | "any" {
    return this._language;
  }

  toArray(): Array<{
    key: string;
    value: any;
  }> {
    return [
      { key: "foryou_categories", value: this._forYouCategories },
      { key: "foryou_cities", value: this._forYouCities },
      { key: "foryou_days", value: this._forYouDays },
      { key: "foryou_business", value: this._forYouBusiness },
      { key: "foryou_price", value: this._forYouPrice },
      { key: "foryou_name", value: this._forYouName },
      { key: "map_categories", value: this._mapCategories },
      { key: "map_price", value: this._mapPrice },
      { key: "map_days", value: this._mapDays },
      { key: "map_business", value: this._mapBusiness },
      { key: "map_name", value: this._mapName },
      { key: "language", value: this._language },
    ];
  }

  toArray2(): Array<{
    key: string;
    value: any;
  }> {
    return [
      { key: "foryou_categories2", value: this._forYouCategories },
      { key: "foryou_cities2", value: this._forYouCities },
      { key: "foryou_days2", value: this._forYouDays },
      { key: "foryou_business2", value: this._forYouBusiness },
      { key: "foryou_price2", value: this._forYouPrice },
      { key: "foryou_name2", value: this._forYouName },
      { key: "map_categories2", value: this._mapCategories },
      { key: "map_days2", value: this._mapDays },
      { key: "map_price2", value: this._mapPrice },
      { key: "map_business2", value: this._mapBusiness },
      { key: "map_name2", value: this._mapName },
      { key: "language2", value: this._language },
    ];
  }
}
