
export class RGBAColor {
    private _red: number
    private _green: number
    private _blue: number
    private _opacity = 1

    constructor(red: number, green: number, blue: number, opacity: number) {
        this._red = red;
        this._green = green;
        this._blue = blue;
        this._opacity = opacity;
    }

    generateLowOpacityColor(): string {
        const thisColor = new RGBAColor(this._red, this._green, this._blue, this._opacity - 0.6)
        return thisColor.generateColor()
    }

    generateColor(): string {
        return 'rgba(' + this._red + ',' + this._green + ',' + this._blue + ',' + this._opacity + ')'
    }
    generateOpacity(value: number): string {
        this._opacity = value
        const thisColor = new RGBAColor(this._red, this._green, this._blue, this._opacity)
        return thisColor.generateColor()
    }
}
