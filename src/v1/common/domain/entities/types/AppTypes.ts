import { ReactNode } from 'react';

export interface ChoiceType {

    value: string
    label: string
    iconName?: string
    title?: string
    items?: ChoiceType[]
}

export interface TabulationType {

    key: string
    title: string
    content: ReactNode
}

export interface AccordionType {
    rank: number
    title: string
    subTitle: string
    content: ReactNode
    iconName?: string
}

export interface DateRangeType {
    key: 'fixed' | 'all_days' |
        'mondays_to_fridays' | 'saturdays_and_sundays' |
        'all_mondays' | 'all_tuesdays' |
        'all_wednesdays' | 'all_thursdays' |
        'all_fridays' | 'all_saturdays' |
        'all_sundays'
    label: string
    startTime: string
    endTime: string
}

export interface ScheduleType {
    id: string
    dateRange: DateRangeType
    isPaused: boolean
    prePoned: boolean
}

export type FieldType =
    'name'
    | 'birthday'
    | 'phoneNumber'
    | 'siren'
    | 'RNA'
    | 'zipCode'
    | 'description'
    | 'address'
    | 'email'

export interface MigrationQuery {
    version: number
    description: string
    query: string
}

export interface SqlError {
    message: string
    code: number
}
