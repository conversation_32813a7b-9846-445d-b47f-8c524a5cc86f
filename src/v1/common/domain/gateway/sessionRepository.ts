import { Observable } from "rxjs";
import { AppPreference } from "../entities/appPreference";

export interface SessionRepository {
  initStorage(): Observable<void>;

  saveAuthorizationToken(token: string): Observable<void>;

  getAuthorizationToken(): Observable<string>;

  retrieveApplicationPreferences(): Observable<AppPreference>;

  saveApplicationPreferences(filter: AppPreference): Observable<void>;

  retrieveApplicationPreferences2(): Observable<AppPreference>;

  saveApplicationPreferences2(filter: AppPreference): Observable<void>;
}
