import { ApplicationCitiesState } from '../../configuration/application.states';
import {
    LOAD_APPLICATION_CITIES, LOAD_APPLICATION_CITIES_FAILED, LOAD_APPLICATION_CITIES_SUCCEEDED
} from './applicationCities.actions';

const initialState: ApplicationCitiesState = {
    loading: false,
    cities: null,
    error: undefined
}

export const applicationCitiesReducer = (state = initialState, action) => {
    switch (action.type) {
        case LOAD_APPLICATION_CITIES:
            return {
                loading: true,
                cities: null,
                error: undefined
            }
        case LOAD_APPLICATION_CITIES_SUCCEEDED:
            return {
                loading: false,
                cities: action.payload,
                error: undefined
            }
        case LOAD_APPLICATION_CITIES_FAILED:
            return {
                loading: false,
                cities: null,
                error: action.payload
            }
        default:
            return state
    }
}
