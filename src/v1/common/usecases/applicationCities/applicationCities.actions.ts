export const LOAD_APPLICATION_CITIES = 'LOAD_APPLICATION_CITIES';
export const LOAD_APPLICATION_CITIES_FAILED = 'LOAD_APPLICATION_CITIES_FAILED';
export const LOAD_APPLICATION_CITIES_SUCCEEDED = 'LOAD_APPLICATION_CITIES_SUCCEEDED';

export const loadApplicationCities = () => ({
    type: LOAD_APPLICATION_CITIES
})

export const loadApplicationCitiesSucceeded = (cities: string[]) => ({
    type: LOAD_APPLICATION_CITIES_SUCCEEDED,
    payload: cities
})

export const loadApplicationCitiesFailed = (error: string) => ({
    type: LOAD_APPLICATION_CITIES_FAILED,
    payload: error
})
