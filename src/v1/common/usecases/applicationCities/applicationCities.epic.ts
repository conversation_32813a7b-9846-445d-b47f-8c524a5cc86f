import {ofType} from 'redux-observable';
import {of} from 'rxjs/internal/observable/of';
import {catchError, map, switchMap} from 'rxjs/operators';
import {ApplicationRemoteService} from '../../domain/gateway/applicationRemoteService';
import {
  LOAD_APPLICATION_CITIES,
  loadApplicationCitiesFailed,
  loadApplicationCitiesSucceeded,
} from './applicationCities.actions';

export const applicationCitiesEpic: any = (
  action$,
  store,
  {
    applicationRemoteService,
  }: {applicationRemoteService: ApplicationRemoteService},
) =>
  action$.pipe(
    ofType(LOAD_APPLICATION_CITIES),
    switchMap(() =>
      applicationRemoteService.loadApplicationCities().pipe(
        map((cities: string[]) => loadApplicationCitiesSucceeded(cities)),
        catchError((err: string) => of(loadApplicationCitiesFailed(err))),
      ),
    ),
  );
