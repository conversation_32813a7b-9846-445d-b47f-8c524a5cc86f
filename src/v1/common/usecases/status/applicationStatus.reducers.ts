import { ApplicationStatusState } from '../../configuration/application.states'
import { SET_GPS_STATUS, SET_NETWORK_STATUS, SET_PUBLIC_BUSINESS_ID } from './applicationStatus.actions'

const initialStatus: ApplicationStatusState = {
    network: true,
    gps    : true,
    publicBusinessId: null
}

export const applicationStatus = (state = initialStatus, action) => {
    switch (action.type) {
        case SET_NETWORK_STATUS:
            return {
                ...state,
                network: action.payload
            }
        case SET_GPS_STATUS:
            return {
                ...state,
                gps: action.payload
            }
        case SET_PUBLIC_BUSINESS_ID:
            return {
                ...state,
                publicBusinessId: action.payload
            }

        default:
            return state
    }
}
