import { AppPreference } from "../../domain/entities/appPreference";

export const SAVE_APPLICATION_PREFERENCE = "SAVE_APPLICATION_PREFERENCE";
export const SAVE_APPLICATION_PREFERENCE_2 = "SAVE_APPLICATION_PREFERENCE_2";

export const APPLICATION_PREFERENCE_SAVED = "APPLICATION_PREFERENCE_SAVED";
export const APPLICATION_PREFERENCE_SAVED_2 = "APPLICATION_PREFERENCE_SAVED_2";

export const SAVE_APPLICATION_PREFERENCE_ERROR =
  "SAVE_APPLICATION_PREFERENCE_ERROR";
export const SAVE_APPLICATION_PREFERENCE_ERROR_2 =
  "SAVE_APPLICATION_PREFERENCE_ERROR_2";

export const RETRIEVE_APPLICATION_PREFERENCE =
  "RETRIEVE_APPLICATION_PREFERENCE";
export const RETRIEVE_APPLICATION_PREFERENCE_2 =
  "RETRIEVE_APPLICATION_PREFERENCE_2";

export const APPLICATION_PREFERENCE_RETRIEVED =
  "APPLICATION_PREFERENCE_RETRIEVED";
export const APPLICATION_PREFERENCE_RETRIEVED_2 =
  "APPLICATION_PREFERENCE_RETRIEVED_2";

export const RETRIEVE_APPLICATION_PREFERENCE_ERROR =
  "RETRIEVE_APPLICATION_PREFERENCE_ERROR";
export const RETRIEVE_APPLICATION_PREFERENCE_ERROR_2 =
  "RETRIEVE_APPLICATION_PREFERENCE_ERROR_2";

export const saveAppPreference = (preference: AppPreference) => ({
  type: SAVE_APPLICATION_PREFERENCE,
  payload: preference,
});
export const saveAppPreference2 = (preference: AppPreference) => ({
  type: SAVE_APPLICATION_PREFERENCE_2,
  payload: preference,
});

export const appPreferenceSaved = () => ({
  type: APPLICATION_PREFERENCE_SAVED,
});
export const appPreferenceSaved2 = () => ({
  type: APPLICATION_PREFERENCE_SAVED_2,
});

export const saveAppPreferenceError = () => ({
  type: SAVE_APPLICATION_PREFERENCE_ERROR,
});
export const saveAppPreferenceError2 = () => ({
  type: SAVE_APPLICATION_PREFERENCE_ERROR_2,
});

export const retrieveAppPreference = () => ({
  type: RETRIEVE_APPLICATION_PREFERENCE,
});
export const retrieveAppPreference2 = () => ({
  type: RETRIEVE_APPLICATION_PREFERENCE_2,
});

export const appPreferenceRetrieved = (preference: AppPreference) => ({
  type: APPLICATION_PREFERENCE_RETRIEVED,
  payload: preference,
});
export const appPreferenceRetrieved2 = (preference: AppPreference) => ({
  type: APPLICATION_PREFERENCE_RETRIEVED_2,
  payload: preference,
});

export const retrieveAppPreferenceError = () => ({
  type: RETRIEVE_APPLICATION_PREFERENCE_ERROR,
});
export const retrieveAppPreferenceError2 = () => ({
  type: RETRIEVE_APPLICATION_PREFERENCE_ERROR_2,
});
