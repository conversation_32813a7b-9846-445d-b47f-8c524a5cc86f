import { AppState } from "../../../configuration/AppState";

export const appPreferenceSelector = (state: AppState) =>
  state.application.applicationPreference.preference;
export const appPreference2Selector = (state: AppState) =>
  state.application.applicationPreference.preference2;
export const appPreferenceSuccessSelector = (state: AppState) =>
  state.application.applicationPreference.success;
export const appPreferenceLoadingSelector = (state: AppState) =>
  state.application.applicationPreference.loading;
