import {ofType} from 'redux-observable';
import {of} from 'rxjs/internal/observable/of';
import {catchError, flatMap, map, switchMap} from 'rxjs/operators';
import {GlobalSettings} from '../../../configuration/setting/app.settings';
import {retrieveInstantsListing} from '../../../instantscontext/usecases/instantslisting/instantsListing.actions';
import {retrieveInstantsSchedule} from '../../../instantscontext/usecases/instantsSchedule/instantsSchedule.actions';
import {AppPreference} from '../../domain/entities/appPreference';
import {SessionRepository} from '../../domain/gateway/sessionRepository';
import {
  appPreferenceRetrieved,
  appPreferenceSaved,
  RETRIEVE_APPLICATION_PREFERENCE,
  retrieveAppPreference,
  retrieveAppPreferenceError,
  SAVE_APPLICATION_PREFERENCE,
  saveAppPreferenceError,
  SAVE_APPLICATION_PREFERENCE_2,
  RETRIEVE_APPLICATION_PREFERENCE_2,
  appPreferenceRetrieved2,
  retrieveAppPreferenceError2,
  saveAppPreferenceError2,
  appPreferenceSaved2,
  retrieveAppPreference2,
} from './applicationPreference.actions';
import {EventRegister} from 'react-native-event-listeners';
import {APP_PREFERENCE_SAVED} from '../../../helpers/constant.helper';

export const saveApplicationPreferenceEpic: any = (
  action$,
  store,
  {sessionRepository}: {sessionRepository: SessionRepository},
) =>
  action$.pipe(
    ofType(SAVE_APPLICATION_PREFERENCE),
    switchMap<{payload: AppPreference}, any>(action =>
      sessionRepository.saveApplicationPreferences(action.payload).pipe(
        flatMap(() => {
          if (action.payload.forYouCategories !== undefined) {
            EventRegister.emit(APP_PREFERENCE_SAVED, action.payload);
            return [
              appPreferenceSaved(),
              // retrieveInstantsSchedule(action.payload),
              retrieveAppPreference(),
            ];
          } else
            return [
              appPreferenceSaved(),
              retrieveInstantsListing(
                GlobalSettings.centerClichy,
                GlobalSettings.min_retrieval_radius,
                action.payload,
              ),
              retrieveAppPreference(),
            ];
        }),
        catchError(() => of(saveAppPreferenceError())),
      ),
    ),
  );
export const saveApplicationPreferenceEpic2 = (
  action$,
  store,
  {sessionRepository}: {sessionRepository: SessionRepository},
) =>
  action$.pipe(
    ofType(SAVE_APPLICATION_PREFERENCE_2),
    switchMap<{payload: AppPreference}, any>(action =>
      sessionRepository.saveApplicationPreferences2(action.payload).pipe(
        flatMap(() => {
          return [appPreferenceSaved2(), retrieveAppPreference2()];
        }),
        catchError(() => of(saveAppPreferenceError2())),
      ),
    ),
  );
export const retrieveApplicationPreferenceEpic: any = (
  action$,
  store,
  {sessionRepository}: {sessionRepository: SessionRepository},
) =>
  action$.pipe(
    ofType(RETRIEVE_APPLICATION_PREFERENCE),
    switchMap(() =>
      sessionRepository.retrieveApplicationPreferences().pipe(
        map((preference: AppPreference) => appPreferenceRetrieved(preference)),
        catchError(() => of(retrieveAppPreferenceError())),
      ),
    ),
  );

export const retrieveApplicationPreferenceEpic2: any = (
  action$,
  store,
  {sessionRepository}: {sessionRepository: SessionRepository},
) =>
  action$.pipe(
    ofType(RETRIEVE_APPLICATION_PREFERENCE_2),
    switchMap(() =>
      sessionRepository.retrieveApplicationPreferences2().pipe(
        map((preference: AppPreference) => appPreferenceRetrieved2(preference)),
        catchError(() => of(retrieveAppPreferenceError2())),
      ),
    ),
  );
