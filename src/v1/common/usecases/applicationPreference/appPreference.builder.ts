import {
  ACTIVITY_APP_PREFERENCE_SAVED,
  CREATION_APP_PREFERENCE_SAVED,
  FOR_YOU_APP_PREFERENCE_SAVED,
} from 'src/v1/helpers/constant.helper';
import {AppPreference} from '../../domain/entities/appPreference';
import {storage} from 'src/_helpers';

export class AppPreferenceBuilder {
  private _forYouCategories: number[];
  private _forYouCities: string[];
  private _forYouDays: string[];
  private _forYouBusiness: string[];
  private _forYouPrice: 'free' | 'paid' | 'all';
  private _forYouName: string;
  private _mapCategories: number[];
  private _mapPrice: 'free' | 'paid' | 'all';
  private _mapBusiness: string[];
  private _mapDays: string[];
  private _mapName: string;
  private _language: 'en' | 'fr' | 'any';

  constructor() {
    // Initialize all fields with default values
    this._forYouCategories = [];
    this._forYouCities = [];
    this._forYouDays = [];
    this._forYouBusiness = [];
    this._forYouPrice = 'all';
    this._forYouName = '';
    this._mapCategories = [];
    this._mapPrice = 'all';
    this._mapBusiness = [];
    this._mapDays = [];
    this._mapName = '';
    this._language = 'any';
  }

  withForYouCategories(value: number[]): AppPreferenceBuilder {
    this._forYouCategories = value;
    return this;
  }

  withForYouCities(value: string[]): AppPreferenceBuilder {
    this._forYouCities = value;
    return this;
  }
  withForYouDays(value: string[]): AppPreferenceBuilder {
    this._forYouDays = value;
    return this;
  }

  withForYouBusiness(value: string[]): AppPreferenceBuilder {
    this._forYouBusiness = value;
    return this;
  }

  withForYouPrice(value: 'free' | 'paid' | 'all'): AppPreferenceBuilder {
    this._forYouPrice = value;
    return this;
  }

  withForYouName(value: string): AppPreferenceBuilder {
    this._forYouName = value;
    return this;
  }

  withMapCategories(value: number[]): AppPreferenceBuilder {
    this._mapCategories = value;
    return this;
  }

  withMapPrice(value: 'free' | 'paid' | 'all'): AppPreferenceBuilder {
    this._mapPrice = value;
    return this;
  }

  withMapBusiness(value: string[]): AppPreferenceBuilder {
    this._mapBusiness = value;
    return this;
  }

  withMapDays(value: string[]): AppPreferenceBuilder {
    this._mapDays = value;
    return this;
  }

  withMapName(value: string): AppPreferenceBuilder {
    this._mapName = value;
    return this;
  }

  withLanguage(value: 'en' | 'fr' | 'any'): AppPreferenceBuilder {
    this._language = value;
    return this;
  }

  build(): AppPreference {
    return new AppPreference(
      this._forYouCategories,
      this._forYouCities,
      this._forYouDays,
      this._forYouBusiness,
      this._forYouPrice,
      this._forYouName,
      this._mapCategories,
      this._mapDays,
      this._mapPrice,
      this._mapBusiness,
      this._mapName,
      this._language,
    );
  }

  static async loadForYouFilter() {
    let saved = await storage.get(FOR_YOU_APP_PREFERENCE_SAVED);
    if (saved) {
      try {
        saved = typeof saved === 'string' ? JSON.parse(saved) : saved;

        let days = saved.find(category => category.key === 'foryou_days');
        days = days ? days.value : [];

        const filter = new AppPreferenceBuilder()
          .withForYouCategories(
            saved.find(category => category.key === 'foryou_categories')
              ?.value ?? [],
          )
          .withForYouCities(
            saved.find(category => category.key === 'foryou_cities')?.value ??
              [],
          )
          .withForYouDays(
            saved.find(category => category.key === 'foryou_days')?.value ?? [],
          )
          .withForYouBusiness(
            saved.find(category => category.key === 'foryou_business')?.value ??
              [],
          )
          .withForYouPrice(
            saved.find(category => category.key === 'foryou_price')?.value ??
              'all',
          )
          .withForYouName(
            saved.find(category => category.key === 'foryou_name')?.value ?? '',
          )
          .withLanguage(
            saved.find(category => category.key === 'language')?.value ?? 'any',
          )
          .build();
        return filter;
      } catch (error) {
        console.error(error);
      }
    }

    return null;
  }

  static async loadMapFilter() {
    let saved = await storage.get(ACTIVITY_APP_PREFERENCE_SAVED);
    if (saved) {
      try {
        saved = typeof saved === 'string' ? JSON.parse(saved) : saved;

        let days = saved.find(category => category.key === 'map_days');
        days = days ? days.value : [];

        const filter = new AppPreferenceBuilder()
          .withMapCategories(
            saved.find(category => category.key === 'map_categories')?.value ??
              [],
          )

          .withMapDays(
            saved.find(category => category.key === 'map_days')?.value ?? [],
          )
          .withMapBusiness(
            saved.find(category => category.key === 'map_business')?.value ??
              [],
          )
          .withMapPrice(
            saved.find(category => category.key === 'map_price')?.value ??
              'all',
          )
          .withMapName(
            saved.find(category => category.key === 'map_name')?.value ?? '',
          )
          .withLanguage(
            saved.find(category => category.key === 'language')?.value ?? 'any',
          )
          .build();
        return filter;
      } catch (error) {
        console.error(error);
      }
    }

    return null;
  }

  static async loadCreationFilter() {
    let saved = await storage.get(CREATION_APP_PREFERENCE_SAVED);
    if (saved) {
      try {
        saved = typeof saved === 'string' ? JSON.parse(saved) : saved;

        let categories = saved.find(
          category => category.key === 'foryou_categories2',
        );
        categories = categories ? categories.value : [];
        let cities = saved.find(a => a.key === 'foryou_cities2');
        cities = cities ? cities.value : [];
        let days = saved.find(category => category.key === 'foryou_days2');
        days = days ? days.value : [];
        let business = saved.find(
          category => category.key === 'foryou_business2',
        );
        business = business ? business.value : [];

        let price = saved.find(category => category.key === 'foryou_price2');
        price = price ? price.value : 'all';
        let lang = saved.find(category => category.key === 'foryou_language2');
        lang = lang ? lang.value : 'all';
        let name = saved.find(category => category.key === 'foryou_name2');

        const filter = new AppPreferenceBuilder()
          .withForYouCategories(categories || [])
          .withForYouCities(cities || [])
          .withForYouDays(days || [])
          .withForYouBusiness(business || [])
          .withForYouPrice(price)
          .withForYouName(name || '')
          .withLanguage(lang)
          .build();

        return filter;
      } catch (error) {
        console.error(error);
      }
    }

    return null;
  }
}
