import { ApplicationPreferenceState } from "../../configuration/application.states";
import {
  APPLICATION_PREFERENCE_RETRIEVED,
  APPLICATION_PREFERENCE_RETRIEVED_2,
  APPLICATION_PREFERENCE_SAVED,
  APPLICATION_PREFERENCE_SAVED_2,
  RETRIEVE_APPLICATION_PREFERENCE,
  RETRIEVE_APPLICATION_PREFERENCE_2,
  RETRIEVE_APPLICATION_PREFERENCE_ERROR,
  RETRIEVE_APPLICATION_PREFERENCE_ERROR_2,
  SAVE_APPLICATION_PREFERENCE,
  SAVE_APPLICATION_PREFERENCE_2,
  SAVE_APPLICATION_PREFERENCE_ERROR,
  SAVE_APPLICATION_PREFERENCE_ERROR_2,
} from "./applicationPreference.actions";
import { AppPreferenceBuilder } from "./appPreference.builder";

const initialFilter = new AppPreferenceBuilder()
  .withForYouCategories([])
  .withForYouCities([])
  .withForYouBusiness([])
  .withForYouPrice("all")
  .withMapCategories([])
  .withMapPrice("all")
  .build();
const initialState: ApplicationPreferenceState = {
  preference: initialFilter,
  preference2: initialFilter,
  loading: false,
  success: null,
};

export const applicationPreferenceReducer = (state = initialState, action) => {
  switch (action.type) {
    case SAVE_APPLICATION_PREFERENCE:
      return {
        preference: action.payload,
        preference2: initialFilter,
        loading: true,
        success: null,
      };
    case APPLICATION_PREFERENCE_SAVED:
      return {
        ...state,
        loading: false,
        success: true,
      };
    case SAVE_APPLICATION_PREFERENCE_ERROR:
      return {
        ...state,
        loading: false,
        success: false,
      };
    case RETRIEVE_APPLICATION_PREFERENCE:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case APPLICATION_PREFERENCE_RETRIEVED:
      return {
        preference: action.payload,
        loading: false,
        success: true,
        preference2: initialFilter,
      };
    case RETRIEVE_APPLICATION_PREFERENCE_ERROR:
      return {
        preference: initialFilter,
        preference2: initialFilter,
        loading: false,
        success: false,
      };

    case SAVE_APPLICATION_PREFERENCE_2:
      return {
        preference: action.payload,
        preference2: initialFilter,
        loading: true,
        success: null,
      };
    case APPLICATION_PREFERENCE_SAVED_2:
      return {
        ...state,
        loading: false,
        success: true,
      };
    case SAVE_APPLICATION_PREFERENCE_ERROR_2:
      return {
        ...state,
        loading: false,
        success: false,
      };
    case RETRIEVE_APPLICATION_PREFERENCE_2:
      return {
        ...state,
        loading: true,
        success: null,
      };
    case APPLICATION_PREFERENCE_RETRIEVED_2:
      return {
        preference: action.payload,
        preference2: initialFilter,
        loading: false,
        success: true,
      };
    case RETRIEVE_APPLICATION_PREFERENCE_ERROR_2:
      return {
        preference: initialFilter,
        preference2: initialFilter,
        loading: false,
        success: false,
      };
    default:
      return state;
  }
};
