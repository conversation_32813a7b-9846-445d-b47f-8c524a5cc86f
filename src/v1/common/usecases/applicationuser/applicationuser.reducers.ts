import { ApplicationUserState } from '../../configuration/application.states';
import {
    APP_USER_IS_AUTHENTICATED,
    APP_USER_IS_NOT_AUTHENTICATED,
    AUTHENTICATE_APP_USER,
    SET_APP_USER, SET_APP_USER_AVATAR
} from './applicationuser.actions';
import { ApplicationUserBuilder } from './applicationuser.builder';

const initialState: ApplicationUserState = {
    applicationUser: new ApplicationUserBuilder().build(),
    authentication : {
        loading        : false,
        isAuthenticated: null
    }
}
export const applicationUserReducer = (state = initialState, action) => {
    switch (action.type) {
        case SET_APP_USER:
            return {
                ...state, applicationUser: action.payload
            }
        case AUTHENTICATE_APP_USER:
            return {
                ...state,
                authentication:
                    { loading: true, isAuthenticated: null }
            }
        case APP_USER_IS_NOT_AUTHENTICATED:
            return {
                ...state,
                authentication:
                    { loading: false, isAuthenticated: false }
            }
        case APP_USER_IS_AUTHENTICATED:
            return {
                ...state,
                authentication:
                    { loading: false, isAuthenticated: true }
            }

        case SET_APP_USER_AVATAR:
            const applicationUser = new ApplicationUserBuilder()
                .withFirstName(state.applicationUser.firstName)
                .withLastName(state.applicationUser.lastName)
                .withPhoneNumber(state.applicationUser.phoneNumber)
                .withAvatar(action.payload)
                .build()
            return {
                ...state, applicationUser
            }
        default:
            return state
    }
}
