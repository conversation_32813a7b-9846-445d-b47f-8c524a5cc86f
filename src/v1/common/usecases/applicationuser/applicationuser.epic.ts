import { ofType } from 'redux-observable'
import { of } from 'rxjs/internal/observable/of';
import { catchError, concatMap } from 'rxjs/internal/operators';
import { switchMap } from 'rxjs/operators'
import { ProfileRepository } from '../../../accountcontext/profilecontext/domaine/gateway/profileRepository';
import { setProfile } from '../../../accountcontext/profilecontext/usecases/profile.actions';
import {
    appUserIsAuthenticated,
    appUserIsNotAuthenticated,
    AUTHENTICATE_APP_USER,
    setApplicationUser
} from './applicationuser.actions';
import { ApplicationUserBuilder } from './applicationuser.builder';

export const authenticatingApplicationUserEpic = (action$, store, { profileRepository }: { profileRepository: ProfileRepository }) =>
    action$.pipe(
        ofType(AUTHENTICATE_APP_USER),
        switchMap(() => profileRepository.loadLocalProfile()
            .pipe(
                concatMap(profile => {
                        const applicationUser = new ApplicationUserBuilder()
                            .withAvatar(profile.photoURL)
                            .withFirstName(profile.firstName)
                            .withLastName(profile.lastName)
                            .withPhoneNumber(profile.phoneNumber)
                            .build()
                        return [
                            appUserIsAuthenticated(),
                            setApplicationUser(applicationUser),
                            setProfile(profile)
                        ]
                    }
                ), catchError(() => of(appUserIsNotAuthenticated()))
            )
        )
    )
