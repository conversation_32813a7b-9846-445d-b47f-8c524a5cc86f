import { ApplicationUser } from '../../domain/entities/ApplicationUser';

export const SET_APP_USER = 'SET_APP_USER'
export const AUTHENTICATE_APP_USER = 'AUTHENTICATE_APP_USER'
export const APP_USER_IS_NOT_AUTHENTICATED = 'APP_USER_IS_NOT_AUTHENTICATED'
export const APP_USER_IS_AUTHENTICATED = 'APP_USER_IS_AUTHENTICATED'
export const SET_APP_USER_AVATAR = 'SET_APP_USER_AVATAR'

export const setApplicationUser = (appUser: ApplicationUser) => ({
    type: SET_APP_USER,
    payload: appUser
})

export const authenticateAppUser = () => ({
    type: AUTHENTICATE_APP_USER
})

export const appUserIsNotAuthenticated = () => ({
    type: APP_USER_IS_NOT_AUTHENTICATED
})

export const appUserIsAuthenticated = () => ({
    type: APP_USER_IS_AUTHENTICATED
})

export const setAppUserAvatar = avatar => ({
    type: SET_APP_USER_AVATAR,
    payload: avatar
})
