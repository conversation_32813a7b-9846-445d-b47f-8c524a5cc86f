import {ApplicationUser} from '../../domain/entities/ApplicationUser';

export class ApplicationUserBuilder {
  protected _isAdmin: boolean;
  protected _uuid: string;
  protected _phoneNumber: string;
  protected _firstName: string;
  protected _lastName: string;
  protected _email: string;
  protected _birthDate: string;
  protected _loginType: string;
  protected _avatar: string = '';
  private _spentAmount: number;
  private _fidelityBalance: number;
  private _spentFidelityAmount: number;
  private _language: string;

  withEmail(value: string): ApplicationUserBuilder {
    this._email = value;
    return this;
  }

  withUuid(value: string): ApplicationUserBuilder {
    this._uuid = value;
    return this;
  }

  withAdmin(value: boolean): ApplicationUserBuilder {
    this._isAdmin = value;
    return this;
  }

  withBirthDate(value: string): ApplicationUserBuilder {
    this._birthDate = value;
    return this;
  }

  withLoginType(value: string): ApplicationUserBuilder {
    this._loginType = value;
    return this;
  }

  withPhoneNumber(value: string): ApplicationUserBuilder {
    this._phoneNumber = value;
    return this;
  }

  withFirstName(value: string): ApplicationUserBuilder {
    this._firstName = value;
    return this;
  }

  withLastName(value: string): ApplicationUserBuilder {
    this._lastName = value;
    return this;
  }

  withAvatar(value: string): ApplicationUserBuilder {
    this._avatar = value;
    return this;
  }

  withSpentAmount(value: number): ApplicationUserBuilder {
    this._spentAmount = value;
    return this;
  }

  withFidelityBalance(value: number): ApplicationUserBuilder {
    this._fidelityBalance = value;
    return this;
  }

  withSpentFidelityAmount(value: number): ApplicationUserBuilder {
    this._spentFidelityAmount = value;
    return this;
  }

  withLanguage(value: string): ApplicationUserBuilder {
    this._language = value;
    return this;
  }

  build(): ApplicationUser {
    return new ApplicationUser(
      this._phoneNumber,
      this._firstName,
      this._lastName,
      this._avatar,
      this._email,
      this._birthDate,
      this._loginType,
      this._spentAmount,
      this._fidelityBalance,
      this._spentFidelityAmount,
      this._uuid,
      this._isAdmin,
      this._language,
    );
  }
}
