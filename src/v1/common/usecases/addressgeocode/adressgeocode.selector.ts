import { AppState } from '../../../configuration/AppState';

export const geocodeAddressLoadingSelector = (store: AppState) => store.application.addressGeocoding.loading
export const geocodeAddressErrorSelector = (store: AppState) => store.application.addressGeocoding.error
export const geocodeAddressSuccessSelector = (store: AppState) => store.application.addressGeocoding.success
export const geocodeAddressCoordinatesSelector = (store: AppState) => store.application.addressGeocoding.coordinates
