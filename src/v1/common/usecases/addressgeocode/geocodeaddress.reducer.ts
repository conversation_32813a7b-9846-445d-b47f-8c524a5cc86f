import { ApplicationAddressGeocodingState } from '../../configuration/application.states';
import { GEOCODE_ADDRESS, GEOCODE_ADDRESS_FAILED, GEOCODE_ADDRESS_SUCCEED } from './geocodeaddress.action';

const initialState: ApplicationAddressGeocodingState = {
    loading    : false,
    error      : undefined,
    success    : null,
    coordinates: { latitude: undefined, longitude: undefined }
}

export const applicationGeocodeAddressReducer = (state = initialState, action) => {
    switch (action.type) {
        case  GEOCODE_ADDRESS :
            return {
                ...state,
                loading    : true,
                error      : undefined,
                success    : null,
                coordinates: { latitude: undefined, longitude: undefined }
            }
        case GEOCODE_ADDRESS_SUCCEED:
            return {
                ...state,
                loading    : false,
                error      : undefined,
                success    : true,
                coordinates: action.payload
            }
        case GEOCODE_ADDRESS_FAILED:
            return {
                ...state,
                loading    : false,
                error      : action.payload,
                success    : false,
                coordinates: { latitude: undefined, longitude: undefined }
            }
        default:
            return state
    }
}
