import { Coordinates } from '../../domain/entities/Coordinates';

export const GEOCODE_ADDRESS = 'GEOCODE_ADDRESS'
export const GEOCODE_ADDRESS_SUCCEED = 'GEOCODE_ADDRESS_SUCCEED'
export const GEOCODE_ADDRESS_FAILED = 'GEOCODE_ADDRESS_FAILED'

export const geocodeAddress = (zipCode: string, city: string, country: string, address: string) => ({
    type   : GEOCODE_ADDRESS,
    payload: { zipCode, city, country, address }
})

export const geocodeAddressSucceed = (coord: Coordinates) => ({
    type   : GEOCODE_ADDRESS_SUCCEED,
    payload: coord
})

export const geocodeAddressFailed = (error: string) => ({
    type   : GEOCODE_ADDRESS_FAILED,
    payload: error
})
