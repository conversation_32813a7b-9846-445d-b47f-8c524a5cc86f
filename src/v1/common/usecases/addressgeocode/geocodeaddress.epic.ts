import {ofType} from 'redux-observable';
import {of} from 'rxjs/index';
import {catchError, map, switchMap} from 'rxjs/internal/operators';
import {Coordinates} from '../../domain/entities/Coordinates';
import {ApplicationRemoteService} from '../../domain/gateway/applicationRemoteService';
import {
  GEOCODE_ADDRESS,
  geocodeAddressFailed,
  geocodeAddressSucceed,
} from './geocodeaddress.action';

export const geocodeAddressEpic: any = (
  action$,
  store,
  {
    applicationRemoteService,
  }: {applicationRemoteService: ApplicationRemoteService},
) =>
  action$.pipe(
    ofType(GEOCODE_ADDRESS),
    switchMap<
      {
        payload: {
          zipCode: string;
          city: string;
          country: string;
          address: string;
        };
      },
      any
    >(action =>
      applicationRemoteService
        .geocodeAddress(
          action.payload.zipCode,
          action.payload.city,
          action.payload.country,
          action.payload.address,
        )
        .pipe(
          map((coord: Coordinates) => geocodeAddressSucceed(coord)),
          catchError(error => of(geocodeAddressFailed(error))),
        ),
    ),
  );
