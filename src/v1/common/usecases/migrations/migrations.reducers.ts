import { MigrationState } from '../../configuration/application.states';
import { MIGRAT<PERSON>, MIGRATIONS_FAILED, MIGRATIONS_SUCCEDED } from './migrations.actions';

const initialStatus: MigrationState = {
    loading: false,
    success: null,
    error  : undefined
}

export const migrationReducer = (state = initialStatus, action) => {
    switch (action.type) {
        case MIGRATE:
            return {
                ...state,
                loading: true
            }
        case MIGRATIONS_SUCCEDED:
            return {
                ...state,
                loading: false,
                success: true
            }
        case MIGRATIONS_FAILED:
            return {
                ...state,
                loading: false,
                error  : action.payload
            }
        default:
            return state
    }
}
