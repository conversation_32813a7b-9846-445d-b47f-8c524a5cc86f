import {ofType} from 'redux-observable';
import {of} from 'rxjs/internal/observable/of';
import {concatMap} from 'rxjs/internal/operators';
import {catchError, map, switchMap} from 'rxjs/operators';
import {migrationQueries} from '../../../configuration/migrations.diff';
import {MigrationEntity} from '../../domain/entities/migrationVersion';
import {MigrationQuery, SqlError} from '../../domain/entities/types/AppTypes';
import {ApplicationRemoteService} from '../../domain/gateway/applicationRemoteService';
import {MigrationRepository} from '../../domain/gateway/migrationRepository';
import {
  MIGRATE,
  migrationFailed,
  migrationSucceeded,
} from './migrations.actions';

const compareMigrations = (
  migrationsEntities: MigrationEntity[],
  allMigration: MigrationQuery[],
): MigrationQuery[] => {
  const sortedMigrationQueries: MigrationQuery[] = [...allMigration].sort(
    (a, b) => (a.version < b.version ? -1 : 1),
  );
  return sortedMigrationQueries.filter(query => {
    for (const entity of migrationsEntities)
      if (entity.version === query.version) return false;
    return true;
  });
};

export const migrationEpic: any = (
  action$,
  store,
  {
    migrationRepository,
    applicationRemoteService,
  }: {
    migrationRepository: MigrationRepository;
    applicationRemoteService: ApplicationRemoteService;
  },
) =>
  action$.pipe(
    ofType(MIGRATE),
    switchMap(() =>
      migrationRepository.retrieveMigrationVersions().pipe(
        concatMap((savedMigration: MigrationEntity[]) => {
          const migrationToExecute = compareMigrations(
            savedMigration,
            migrationQueries,
          );

          return migrationRepository.executeMigrations(migrationToExecute).pipe(
            map(() => {
              return migrationSucceeded();
            }),
            catchError((err: any) => {
              applicationRemoteService.sendReport(JSON.stringify(err)).pipe(
                map(() => void 0),
                catchError(() => of(migrationFailed(err))),
              );
              return of(migrationFailed(err));
            }),
          );
        }),
        catchError((retrieveError: any) => {
          console.log('====> ', retrieveError);
          if (
            retrieveError &&
            retrieveError.message &&
            retrieveError.message.indexOf(
              'no such table: migration_versions',
            ) !== -1
          )
            return migrationRepository.createMigrationTable().pipe(
              concatMap(() =>
                migrationRepository.executeMigrations(migrationQueries).pipe(
                  map(() => migrationSucceeded()),
                  catchError((error: SqlError) => {
                    applicationRemoteService
                      .sendReport(JSON.stringify(error))
                      .pipe(
                        map(() => void 0),
                        catchError(() => of(migrationFailed(error))),
                      );
                    return of(migrationFailed(error));
                  }),
                  catchError((executeError: SqlError) => {
                    applicationRemoteService
                      .sendReport(JSON.stringify(executeError))
                      .pipe(
                        map(() => void 0),
                        catchError(() => of(migrationFailed(executeError))),
                      );
                    return of(migrationFailed(executeError));
                  }),
                ),
              ),
              catchError((createError: SqlError) => {
                applicationRemoteService
                  .sendReport(JSON.stringify(createError))
                  .pipe(
                    map(() => void 0),
                    catchError(() => of(migrationFailed(createError))),
                  );
                return of(migrationFailed(createError));
              }),
            );
          else {
            applicationRemoteService
              .sendReport(JSON.stringify(retrieveError))
              .pipe(
                map(() => migrationFailed(retrieveError)),
                catchError(() => of(migrationFailed(retrieveError))),
              );
            return of(migrationFailed(retrieveError));
          }
        }),
      ),
    ),
  );
