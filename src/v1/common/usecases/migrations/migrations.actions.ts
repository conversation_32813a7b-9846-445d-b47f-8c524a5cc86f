import { SqlError } from '../../domain/entities/types/AppTypes';

export const MIGRATE = 'MIGRATE'
export const MIGRATIONS_SUCCEDED = 'MIGRATIONS_SUCCEDED'
export const MIGRATIONS_FAILED = 'MIGRATIONS_FAILED'

export const migrate = () => ({
    type: MIGRATE
})

export const migrationSucceeded = () => ({
    type: MIGRATIONS_SUCCEDED
})
export const migrationFailed = (error: SqlError) => ({
    type   : MIGRATIONS_FAILED,
    payload: error
})
