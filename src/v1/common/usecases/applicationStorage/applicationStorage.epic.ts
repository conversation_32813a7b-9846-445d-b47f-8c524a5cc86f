import {ofType} from 'redux-observable';
import {of} from 'rxjs';
import {catchError, map, switchMap} from 'rxjs/operators';
import {SessionRepository} from '../../domain/gateway/sessionRepository';
import {
  INITIALIZE_STORAGE,
  initializeStorageFailed,
  initializeStorageSucceed,
} from './applicationStorage.actions';

export const initStorageEpic: any = (
  action$,
  store,
  {sessionRepository}: {sessionRepository: SessionRepository},
) =>
  action$.pipe(
    ofType(INITIALIZE_STORAGE),
    switchMap(() =>
      sessionRepository.initStorage().pipe(
        map(() => initializeStorageSucceed()),
        catchError(error => of(initializeStorageFailed(error))),
      ),
    ),
  );
