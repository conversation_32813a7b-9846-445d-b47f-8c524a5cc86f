import { ApplicationUIState } from '../../configuration/application.states'
import {
    CLOSE_BROWSER_MODAL,
    CLOSE_IMAGE_FULL_SCREEN_MODAL,
    CLOSE_INSTANT_DETAILS_MODAL,
    OPEN_BROWSER_MODAL,
    OPEN_IMAGE_FULL_SCREEN_MODAL,
    OPEN_INSTANT_DETAILS_MODAL
} from './ui.actions'

const initialApplicationUI: ApplicationUIState = {
    instantDetailsModal : { isVisible: false, instantId: null },
    browserModal        : { isVisible: false, url: null },
    imageFullScreenModal: { isVisible: false, url: '' }
}

export const applicationUI = (state = initialApplicationUI, action) => {
    switch (action.type) {
        case OPEN_INSTANT_DETAILS_MODAL:
            return {
                ...state,
                instantDetailsModal: {
                    isVisible: true,
                    instantId: action.payload
                }
            }

        case CLOSE_INSTANT_DETAILS_MODAL:
            return {
                ...state,
                instantDetailsModal: {
                    isVisible: false,
                    instantId: null
                }
            }

        case OPEN_BROWSER_MODAL:
            return {
                ...state,
                browserModal: {
                    isVisible: true,
                    url      : action.payload
                }
            }

        case CLOSE_BROWSER_MODAL:
            return {
                ...state,
                browserModal: {
                    isVisible: false,
                    url      : null
                }
            }

        case OPEN_IMAGE_FULL_SCREEN_MODAL:
            return {
                ...state,
                imageFullScreenModal: {
                    isVisible: true,
                    url      : action.payload
                }
            }

        case CLOSE_IMAGE_FULL_SCREEN_MODAL:
            return {
                ...state,
                imageFullScreenModal: {
                    isVisible: false,
                    url      : ''
                }
            }

        default:
            return state
    }
}
