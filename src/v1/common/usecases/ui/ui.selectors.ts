import { AppState } from '../../../configuration/AppState'

export const isVisibleInstantDetailsModal = (state: AppState) => state.application.ui.instantDetailsModal.isVisible
export const instantDetailIdModal = (state: AppState) => state.application.ui.instantDetailsModal.instantId

export const isVisibleBrowserModal = (state: AppState) => state.application.ui.browserModal.isVisible
export const urlBrowserModal = (state: AppState) => state.application.ui.browserModal.url

export const isVisibleImageFullScreenModal = (state: AppState) => state.application.ui.imageFullScreenModal.isVisible
export const urlImageFullScreenModal = (state: AppState) => state.application.ui.imageFullScreenModal.url
