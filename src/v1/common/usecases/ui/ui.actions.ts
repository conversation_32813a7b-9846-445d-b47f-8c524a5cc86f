export const OPEN_INSTANT_DETAILS_MODAL = 'OPEN_INSTANT_DETAILS_MODAL'
export const CLOSE_INSTANT_DETAILS_MODAL = 'CLOSE_INSTANT_DETAILS_MODAL'

export const OPEN_BROWSER_MODAL = 'OPEN_BROWSER_MODAL'
export const CLOSE_BROWSER_MODAL = 'CLOSE_BROWSER_MODAL'

export const OPEN_IMAGE_FULL_SCREEN_MODAL = 'OPEN_IMAGE_FULL_SCREEN_MODAL'
export const CLOSE_IMAGE_FULL_SCREEN_MODAL = 'CLOSE_IMAGE_FULL_SCREEN_MODAL'

export const openInstantDetailsModal = (instantId: string) => ({
    type   : OPEN_INSTANT_DETAILS_MODAL,
    payload: instantId
})

export const closeInstantDetailsModal = () => ({
    type   : CLOSE_INSTANT_DETAILS_MODAL
})

export const openBrowserModal = (url: string) => ({
    type   : OPEN_BROWSER_MODAL,
    payload: url
})

export const closeBrowserModal = () => ({
    type   : CLOSE_BROWSER_MODAL
})

export const openImageFullScreenModal = (url: string) => ({
    type   : OPEN_IMAGE_FULL_SCREEN_MODAL,
    payload: url
})

export const closeImageFullScreenModal = () => ({
    type   : CLOSE_IMAGE_FULL_SCREEN_MODAL
})
