import { combineReducers } from 'redux'
import { ApplicationState } from '../configuration/application.states';
import { applicationGeocodeAddressReducer } from './addressgeocode/geocodeaddress.reducer';
import { applicationCitiesReducer } from './applicationCities/applicationCities.reducer';
import { applicationPreferenceReducer } from './applicationPreference/applicationPreference.reducer';
import { applicationSettings } from './applicationsettings/applicationSettings.reducers'
import { applicationUserReducer } from './applicationuser/applicationuser.reducers';
import { migrationReducer } from './migrations/migrations.reducers';
import { applicationStatus } from './status/applicationStatus.reducers'
import { applicationUI } from './ui/ui.reducers'
import { userPosition } from './userposition/getUserPosition.reducers'

export const application = combineReducers<ApplicationState>({
    userPosition,
    status               : applicationStatus,
    settings             : applicationSettings,
    ui                   : applicationUI,
    applicationUser      : applicationUserReducer,
    addressGeocoding     : applicationGeocodeAddressReducer,
    applicationCities    : applicationCitiesReducer,
    applicationPreference: applicationPreferenceReducer,
    migration            : migrationReducer
});
