import { GlobalSettings } from '../../../configuration/setting/app.settings';
import { Coordinates } from '../../domain/entities/Coordinates'
import { SET_USER_POSITION } from './userPosition.actions'

const initialUserPosition: Coordinates = {
    latitude : GlobalSettings.centerClichy.latitude,
    longitude: GlobalSettings.centerClichy.longitude
}

export const userPosition = (state = initialUserPosition, action) => {
    switch (action.type) {
        case SET_USER_POSITION:
            return action.payload

        default:
            return state
    }
}
