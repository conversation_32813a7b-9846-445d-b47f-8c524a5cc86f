import {GlobalSettings} from '../../../configuration/setting/app.settings';
import {ApplicationSettingsState} from '../../configuration/application.states';
import {SET_APP_LANGUE} from '../appLang/appLang.actions';
import {
  SET_DISTANCE_RADIUS,
  SET_RETRIEVAL_RADIUS,
} from './applicationSetting.actions';

const initialApplicationSettingsState: ApplicationSettingsState = {
  distanceRadius: GlobalSettings.min_display_radius,
  retrievalRadius: GlobalSettings.min_retrieval_radius,
  contactEmail: '<EMAIL>',
  distanceFilter: 15,
  appLang: 'fr',
};

export const applicationSettings = (
  state = initialApplicationSettingsState,
  action,
) => {
  switch (action.type) {
    case SET_DISTANCE_RADIUS:
      return {...state, distanceRadius: action.payload};
    case SET_RETRIEVAL_RADIUS:
      return {...state, retrievalRadius: action.payload};
    case SET_APP_LANGUE:
      return {...state, appLang: action.payload};
    default:
      return state;
  }
};
