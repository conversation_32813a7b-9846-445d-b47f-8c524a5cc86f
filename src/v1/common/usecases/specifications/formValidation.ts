import {differenceInYears, parse} from 'date-fns';
import {FieldType} from '../../domain/entities/types/AppTypes';

export class FormValidation {
  static validation(fieldName: FieldType, value): boolean {
    const constraints = {
      phoneNumber: {
        format: {
          pattern: /^(?:(?:\+)33)\s*[1-9](?:[\s.-]*\d{2}){4}$/,
          message: '',
        },
      },
      name: {
        format: {
          pattern: /^(?=.{3,50}$)[a-zA-Z .àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ]*$/,
          message: '',
        },
      },
      birthday: {
        format: {
          pattern: birthDate => {
            return differenceInYears(new Date(), new Date(birthDate)) >= 18;
          },
          message: 'Vous devez avoir au moins 18 ans pour vous inscrire.',
        },
      },
      siren: {
        format: {
          pattern: siren => {
            return this.isValidSiren(siren);
          },
          message: 'siren non valide',
        },
      },
      email: {
        format: {
          pattern: email => this.isValidEmail(email),
          message: 'email non valide',
        },
      },
      RNA: {
        format: {
          pattern: /^[w|W][0-9]{9}$/,
          message: 'Numéro RNA non Valide',
        },
      },
      zipCode: {
        format: {
          pattern: /^[a-zA-Z0-9]{4,6}$/,
          message: '',
        },
      },
      description: {
        format: {
          pattern: /.{10,}/,
          message: '',
        },
      },
      address: {
        format: {
          pattern: /.{5,}/,
          message: '',
        },
      },
    };

    if (
      'birthday' === fieldName ||
      'siren' === fieldName ||
      'email' === fieldName
    )
      return constraints[fieldName].format.pattern(value);
    return new RegExp(constraints[fieldName].format.pattern).test(
      value ? value.toString().trim() : value,
    );
  }

  private static isValidSiren(siren: string): boolean {
    if (siren.replace(/\s/g, '').length !== 9) return false;
    else {
      let somme: number = 0;
      let tmp: number = 0;
      const arraySiren = siren.replace(/\s/g, '').split('');
      arraySiren.map((val, index) => {
        if (index % 2) {
          tmp = parseInt(val, 0) * 2;
          if (tmp > 9) tmp -= 9;
        } else tmp = parseInt(val, 0);
        somme += tmp;
      });
      return somme % 10 === 0;
    }
  }

  private static isValidEmail(email: string): boolean {
    const regex = new RegExp(
      [
        '^(([^<>()[\\]\\.,;:\\s@"]+(\\.[^<>()\\[\\]\\.,;:\\s@"]+)*)',
        '|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.',
        '[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+',
        '[a-zA-Z]{2,}))$',
      ].join(''),
    );
    if (email && email.length > 0) return regex.test(email.toLowerCase());
    return false;
  }
}
