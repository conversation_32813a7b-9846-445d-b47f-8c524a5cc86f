import { GlobalSettings } from '../../../configuration/setting/app.settings';
import { InstantCalculateDistance } from '../../../instantscontext/usecases/specifications/instantCalculateDistance';
import { Coordinates } from '../../domain/entities/Coordinates';
import { ChoiceType } from '../../domain/entities/types/AppTypes';

export class ApplicationSpecification {
    static isPhoneNumber(phone) {
        const regex = /^(?:(?:\(?(?:00|\+)([1-4]\d\d|[1-9]\d?)\)?)?[\-\.\ \\\/]?)?((?:\(?\d{1,}\)?[\-\.\ \\\/]?){0,})(?:[\-\.\ \\\/]?(\d+))?$/;
        return regex.test(phone)
    }

    static isInClichy(location: Coordinates): boolean {
        return InstantCalculateDistance.calculateDistanceInMeter(location, GlobalSettings.centerClichy) < GlobalSettings.radiusClichy
    }

    static mergeArrayAndRemoveDuplicates(firstArray: string[], secondArray: string[]): string[] {
        return firstArray.concat(secondArray).filter((elem, pos, arr) => {
            return arr.indexOf(elem) === pos;
        });
    }

    static arrayDiff(firstArray: string[], secondArray: string[]): string[] {
        return secondArray.filter(i => {
            return firstArray.indexOf(i) === -1;
        });
    }

    static sortByAlphabetize(array: ChoiceType[]): ChoiceType[] {
        if (array && array.length > 0)
            return array.sort((item1, item2) => {
                return item1.label.toUpperCase().trim() < item2.label.toUpperCase().trim() ? -1 : 1
            })
        else
            return array
    }
}
