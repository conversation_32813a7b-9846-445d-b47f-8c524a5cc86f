import { Observable } from 'rxjs';
import { of } from 'rxjs/internal/observable/of'
import { Profile } from '../../../domaine/entities/profile';
import { ProfileRemoteService } from '../../../domaine/gateway/profileRemoteService';

export class InMemoryProfileRemoteService implements ProfileRemoteService {
    editRemoteProfile(profile: Profile): Observable<void> {
        return of(void 0)
    }

    uploadRemotePhoto(uri: string): Observable<string> {
        return of('https://via.placeholder.com/200')
    }

    loadRemoteProfile(): Observable<Profile> {
        return undefined;
    }

    downloadRemoteProfilePicture(token: string): Observable<string> {
        return of('path/to/localFile')
    }
}
