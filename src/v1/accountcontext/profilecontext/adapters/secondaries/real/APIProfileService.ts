import Config from 'react-native-config';
import RNFetchBlob from 'rn-fetch-blob';
import {Observable} from 'rxjs';
import {AjaxResponse} from 'rxjs/ajax';
import {fromPromise} from 'rxjs/internal-compatibility';
import {catchError, map} from 'rxjs/operators';
import {SecuredObservableAjaxHttpClient} from '../../../../../common/adapters/secondaries/real/securedObservableAjax.httpClient';
import {ApplicationContextDependenciesFactory} from '../../../../../common/configuration/applicationContextDependencies.factory';
import {Profile} from '../../../domaine/entities/profile';
import {ProfileRemoteService} from '../../../domaine/gateway/profileRemoteService';
import {ProfileLoadDTO} from './profile/dto/profileLoad.dto';
import {ProfileMapper} from './profile/mapper/profile.mapper';
import {http} from 'src/_helpers';

export class APIProfileService implements ProfileRemoteService {
  editRemoteProfile(profile: Profile): Observable<void> {
    const securedAjaxClient = new SecuredObservableAjaxHttpClient(
      ApplicationContextDependenciesFactory.sessionRepository(),
    );
    const URL = Config.API_URL + '/v1/profile/update';
    const body = ProfileMapper.mapToProfileDTO(profile);
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json',
    };
    return securedAjaxClient.post(URL, body, headers).pipe(
      map(() => void 0),
      catchError(err => Observable.throwError(err.status.toString())),
    );
  }

  uploadRemotePhoto(uri: string): Observable<string> {
    const securedAjaxClient = new SecuredObservableAjaxHttpClient(
      ApplicationContextDependenciesFactory.sessionRepository(),
    );
    const URL = Config.API_URL + '/v1/profile/picture/update';
    const body = new FormData();
    const fileName = uri.substring(uri.lastIndexOf('/') + 1);
    body.append('picture', {uri, name: fileName, type: 'image/jpeg'});
    const headers = {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
    return securedAjaxClient.post(URL, body, headers).pipe(
      map(() => void 0),
      catchError(err => Observable.throwError(err)),
    );
  }

  loadRemoteProfile(): Observable<Profile> {
    const URL = Config.API_URL + '/v1/profile/info';
    return fromPromise(
      (async () => {
        const res = await http.get(URL);
        if (res.status === 200) {
          return ProfileMapper.mapFromDtoToProfile(res.data);
        }
        return null;
      })(),
    );
  }

  downloadRemoteProfilePicture(token: string): Observable<string> {
    const URL = Config.API_URL + '/v1/profile/picture';
    const headers = {
      Authorization: token,
    };
    console.log('=== downloadRemoteProfilePicture ==== ', token);
    return fromPromise(
      RNFetchBlob.config({
        fileCache: false,
        path: RNFetchBlob.fs.dirs.DocumentDir + '/avatar.jpeg',
      }).fetch('GET', URL, headers),
    ).pipe(
      map(res => ProfileMapper.mapPathToAvatarUri(res.path())),
      catchError(err => Observable.throwError(err.status.toString())),
    );
  }
}
