import {Observable} from 'rxjs';
import {fromPromise} from 'rxjs/internal/observable/fromPromise';
import {ApplicationContext} from '../../../../../../common/configuration/application.context';
import {Profile} from '../../../../domaine/entities/profile';
import {ProfileRepository} from '../../../../domaine/gateway/profileRepository';
import {ProfileMapper} from './mapper/profile.mapper';
import {storage} from 'src/_helpers';
import {JWT_TOKEN_KEY} from 'src/v1/helpers/constant.helper';

const db = ApplicationContext.getInstance().db();

export class SQLiteProfileRepository implements ProfileRepository {
  saveLocalProfile(profile: Profile): Observable<void> {
    const saveProfilePromise = new Promise<void>((resolve, reject) => {
      db.transaction(transactionInsertion => {
        transactionInsertion.executeSql(
          'INSERT INTO user values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            profile.phoneNumber,
            profile.photoURL,
            profile.firstName,
            profile.lastName,
            profile.email,
            profile.birthDate,
            profile.gender,
            profile.weight,
            profile.height,
            profile.address,
            profile.city,
            profile.country,
          ],
          () => resolve(void 0),
          err => {
            reject(err);
            return false;
          },
        );
      });
    });

    return fromPromise(saveProfilePromise);
  }

  static async deleteLocalProfile(): Promise<void> {
    await storage.remove(JWT_TOKEN_KEY);
    await new Promise<void>((resolve, reject) => {
      db.transaction(transactionDeletion => {
        transactionDeletion.executeSql(
          'DELETE FROM user',
          [],
          () => resolve(void 0),
          err => {
            reject(err);
            return false;
          },
        );
      });
    });
    await new Promise<void>((resolve, reject) => {
      db.transaction(transactionDeletion => {
        transactionDeletion.executeSql(
          'DELETE FROM business',
          [],
          () => resolve(void 0),
          err => {
            reject(err);
            return false;
          },
        );
      });
    });
    await new Promise<void>((resolve, reject) => {
      db.transaction(transactionDeletion => {
        transactionDeletion.executeSql(
          'DELETE FROM config',
          [],
          () => resolve(void 0),
          err => {
            reject(err);
            return false;
          },
        );
      });
    });
    await new Promise<void>((resolve, reject) => {
      db.transaction(transactionDeletion => {
        transactionDeletion.executeSql(
          'DELETE FROM Instant_Shedule_Table;',
          [],
          () => resolve(void 0),
          err => {
            reject(err);
            return false;
          },
        );
      });
    });
  }

  loadLocalProfile(): Observable<Profile> {
    const getUserProfilePromise = new Promise<Profile>((resolve, reject) => {
      db.transaction(transactionSelect => {
        transactionSelect.executeSql(
          'SELECT * from user',
          [],
          (transaction, result) => {
            if (result.rows.length > 0)
              resolve(ProfileMapper.mapDBToDomainProfile(result.rows.item(0)));
            else reject(0);
          },
          err => {
            reject(err);
            return false;
          },
        );
      });
    });

    return fromPromise(getUserProfilePromise);
  }

  updateLocalProfile(profile: Profile): Observable<void> {
    const updateProfilePromise = new Promise<void>((resolve, reject) => {
      db.transaction(transactionUpdate => {
        transactionUpdate.executeSql(
          'UPDATE user SET firstName = ? , lastName = ?, email= ?,  gender = ?,' +
            'weight= ?, height= ?, address= ?, city= ?, country= ? WHERE phoneNumber = ?',
          [
            profile.firstName,
            profile.lastName,
            profile.email,
            profile.gender,
            profile.weight,
            profile.height,
            profile.address,
            profile.city,
            profile.country,
            profile.phoneNumber,
          ],
          (transaction, result) => {
            if (result.rowsAffected === 1) return resolve(void 0);
            else return reject('error');
          },
          err => {
            reject(err);
            return false;
          },
        );
      });
    });

    return fromPromise(updateProfilePromise);
  }

  updateLocalPhotoURL(photoURL: string, phoneNumber): Observable<void> {
    const updateProfilePromise = new Promise<void>((resolve, reject) => {
      db.transaction(transactionUpdate => {
        transactionUpdate.executeSql(
          'UPDATE user SET photoURL = ? WHERE phoneNumber = ?',
          [photoURL, phoneNumber],
          (transaction, result) => {
            if (result.rowsAffected === 1) resolve(void 0);
            else reject('error');
          },
          err => {
            reject(err);
            return false;
          },
        );
      });
    });

    return fromPromise(updateProfilePromise);
  }
}
