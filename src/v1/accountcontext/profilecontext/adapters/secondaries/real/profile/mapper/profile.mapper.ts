import {format} from 'date-fns';
import {Profile} from '../../../../../domaine/entities/profile';
import {ProfileBuilder} from '../../../../../domaine/entities/profile.builder';
import {ProfileDBDTO} from '../dto/profileDB.dto';
import {ProfileLoadDTO} from '../dto/profileLoad.dto';
import {ProfileUpdateDTO} from '../dto/profileUpdate.dto';

export class ProfileMapper {
  static mapDBToDomainProfile(profileLocal: ProfileDBDTO): Profile {
    //TODO photoURL null
    return new ProfileBuilder()
      .withLastName(profileLocal.lastName)
      .withFirstName(profileLocal.firstName)
      .withPhoneNumber(profileLocal.phoneNumber)
      .withPhotoURL(profileLocal.photoURL === null ? '' : profileLocal.photoURL)
      .withBirthDate(profileLocal.birthdate)
      .withGender(profileLocal.gender)
      .withWeight(profileLocal.weight)
      .withHeight(profileLocal.height)
      .withAddress(profileLocal.address)
      .withCity(profileLocal.city)
      .withCountry(profileLocal.country)
      .withEmail(profileLocal.email)
      .build();
  }

  static mapToProfileDTO(profile: Profile): ProfileUpdateDTO {
    //TODO : Update Profile
    return {
      first_name: profile.firstName,
      last_name: profile.lastName,
      date_of_birth: format(profile.birthDate, 'yyy-MM-dd'),
      gender:
        profile.gender === undefined || profile.gender === null
          ? ''
          : profile.gender,
      weight:
        profile.weight === undefined || profile.weight === null
          ? ''
          : profile.weight,
      height:
        profile.height === undefined || profile.height === null
          ? ''
          : profile.height,
      address:
        profile.address === undefined || profile.address === null
          ? ''
          : profile.address,
      city:
        profile.city === undefined || profile.city === null ? '' : profile.city,
      country:
        profile.country === undefined || profile.country === null
          ? ''
          : profile.country,
      email:
        profile.email === undefined || profile.email === null
          ? ''
          : profile.email,
    };
  }

  static mapFromDtoToProfile(profileLoadDTO: ProfileLoadDTO): Profile {
    return new ProfileBuilder()
      .withLastName(profileLoadDTO.last_name)
      .withFirstName(profileLoadDTO.first_name)
      .withBirthDate(new Date(profileLoadDTO.date_of_bird))
      .withPhoneNumber(profileLoadDTO.phone_number)
      .withGender(profileLoadDTO.gender)
      .withWeight(profileLoadDTO.weight)
      .withHeight(profileLoadDTO.height)
      .withAddress(profileLoadDTO.address)
      .withCity(profileLoadDTO.city)
      .withCountry(profileLoadDTO.country)
      .withEmail(profileLoadDTO.email)
      .build();
  }

  static mapPathToAvatarUri(path: string): string {
    return 'file://' + path;
  }
}
