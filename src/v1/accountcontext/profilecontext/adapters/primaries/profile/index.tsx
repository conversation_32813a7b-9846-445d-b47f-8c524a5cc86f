import {connect} from 'react-redux';
import {AppState} from '../../../../../configuration/AppState';
import {Profile} from '../../../domaine/entities/profile';

import {
  editProfile,
  uploadProfilePhoto,
} from '../../../usecases/profile.actions';
import {
  profileLoadingSelector,
  profileSelector,
  profileSuccessSelector,
} from '../../../usecases/profile.selectors';
import {ProfileContainer} from './profile.container';
import {faker} from 'src/_helpers';

const mapStateToProps = (state: AppState) => ({
  isLoading: profileLoadingSelector(state),
  profile: profileSelector(state),
  success: profileSuccessSelector(state),
});

const mapDispatchToProps = dispatch => ({
  updateProfile: (profile: Profile) => dispatch(editProfile(profile)),
  editPhoto: (uri: string, phoneNumber: string) =>
    dispatch(uploadProfilePhoto(uri, phoneNumber)),
});

//export const EditProfile = connect(mapStateToProps, mapDispatchToProps)(ProfileContainer)

export const EditProfile = (props: any) => {
  return (
    <ProfileContainer
      isLoading={false}
      profile={faker.getProfile()}
      updateProfile={function (profile: Profile): void {
        console.log(arguments);
      }}
      navigation={props.navigation}
      editPhoto={function (uri: string, phoneNumber: string): void {
        console.log(arguments);
      }}
      success={false}
    />
  );
};
