import React, {PureComponent} from 'react';
import {
  Image,
  ImageRequireSource,
  ImageURISource,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Permissions, {PERMISSIONS} from 'react-native-permissions';
import {CustomizedCameraModal} from '../../../../../../common/adapters/primaries/components/form/pickers/modals/customizedCameraModal';
import {Theme} from '../../../../../../configuration/theme/app.theme';

interface Props {
  imageURI: ImageURISource | ImageRequireSource;
  size: number;
  onPictureTaken: (uri) => void;
}

interface State {
  isVisible: boolean;
}

export class ProfileImage extends PureComponent<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
    };
  }

  render() {
    return (
      <View>
        <TouchableOpacity onPress={() => this.isCameraAuthorized()}>
          <Image
            style={[styles.image, this.imageStyle(this.props.size)]}
            source={this.props.imageURI}
          />
        </TouchableOpacity>
        <CustomizedCameraModal
          visible={this.state.isVisible}
          onCloseRequest={() => this.setState({isVisible: false})}
          onPicturing={imageUri => {
            this.setState({isVisible: false});
            this.props.onPictureTaken(imageUri);
          }}
        />
      </View>
    );
  }

  isCameraAuthorized() {
    Permissions.request(
      Platform.OS == 'ios'
        ? PERMISSIONS.IOS.CAMERA
        : PERMISSIONS.ANDROID.CAMERA,
    ).then(success => {
      if (success === 'granted') this.setState({isVisible: true});
      else {
        this.setState({isVisible: false});
        Permissions.openSettings();
      }
    });
  }

  imageStyle(size) {
    return {
      width: size,
      height: size,
      borderRadius: size / 2,
    };
  }
}

const styles = StyleSheet.create({
  image: {
    borderRadius: 30,
    width: 60,
    height: 60,
    backgroundColor: Theme.gainsboro,
  },
});
