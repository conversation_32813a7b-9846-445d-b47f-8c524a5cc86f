import React, { useEffect, useState } from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import ImageResizer from 'react-native-image-resizer';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../../../common/adapters/primaries/components/form/fields/defaultButton.field';
import { InputWithLabel } from '../../../../../common/adapters/primaries/components/form/inputs/inputWithLabel';
import { ChoicePickerContainer } from '../../../../../common/adapters/primaries/components/form/pickers/choicePicker.container';
import { Spinner } from '../../../../../common/adapters/primaries/components/spinner.presentational';
import { ChoiceType } from '../../../../../common/domain/entities/types/AppTypes';
import { RGBAColor } from '../../../../../common/domain/entities/types/rgba';
import { FormValidation } from '../../../../../common/usecases/specifications/formValidation';
import { I18n } from '../../../../../configuration/i18n/i18n';
import { Countries } from '../../../../../configuration/setting/app.settings';
import { Theme } from '../../../../../configuration/theme/app.theme';
import { Profile } from '../../../domaine/entities/profile';
import { ProfileBuilder } from '../../../domaine/entities/profile.builder';
import { ProfileImage } from './components/profileImage.presentational';
import { DatePickerContainer, PhoneInputField } from 'src/_components';
import { http, storage, t, util } from 'src/_helpers';

interface Props {
  isLoading: boolean;
  profile: Profile;
  updateProfile: (profile: Profile) => void;
  navigation: any;
  editPhoto: (uri: string) => void;
  user: any;
  cancelDelete: () => void;
  success: boolean;
}

export const ProfileContainer: React.FC<Props> = ({
  isLoading,
  profile,
  updateProfile,
  navigation,
  editPhoto,
  user,
  cancelDelete,
  success,
}) => {
  const [formData, setFormData] = useState({
    email: undefined,
    firstName: undefined,
    lastName: undefined,
    avatar: require('../../../../../assets/img/default_user.jpg'),
    birthDate: null,
    gender: undefined,
    weight: undefined,
    height: undefined,
    address: undefined,
    city: undefined,
    country: undefined,
  });

  const [formError, setFormError] = useState({
    isErrorEmail: null,
    isErrorFirstName: null,
    isErrorLastName: null,
    isErrorBirthDate: null,
    isErrorWeight: null,
    isErrorHeight: null,
  });

  //console.log("user: ", user);

  useEffect(() => {
    if (profile && formData.firstName === undefined && !isLoading) {
      setFormData({
        email: profile.email,
        firstName: profile.firstName,
        lastName: profile.lastName,
        avatar:
          profile.photoURL === ''
            ? require('../../../../../assets/img/default_user.jpg')
            : { uri: profile.photoURL },
        birthDate: profile.birthDate,
        gender: profile.gender,
        weight: profile.weight,
        height: profile.height,
        address: profile.address,
        city: profile.city,
        country: profile.country,
      });
  }}, [isLoading ,profile]);

  useEffect(() => {
    if (success) {
      navigation.navigate('more');
    }
  }, [success, navigation]);

  const isEmailLogin = profile?.loginType === 'EMAIL';

  const loading = (isLoading || !user) ? (
    <Spinner background={'rgba(254, 148, 62, 0.4);'} />
  ) : null;

  const handleUpdateState = (cat, key, val, setState) => {
    setState(prevState => {
      return {
        ...prevState,
        [key]: val,
      }
    });
  };

  const getCountryList = (): ChoiceType[] => {
    const result: any[] = [];
    Countries().map(item => {
      result.push({
        label: item.name,
        value: item.code,
      });
    });
    return result;
  };

  const setProfilePhoto = (imageUri: string) => {
    resizeImage(imageUri).then(URI => {
      setFormData(prevState => ({
        ...prevState,
        avatar: { uri: URI },
      }));
      const uri = Platform.OS === 'ios' ? URI.replace('file://', '') : URI;
      editPhoto(uri);
    });
  };

  const editProfile = async () => {
    if (hasModification()) {
      
      if (validateForm()) {
        const newProfile = new ProfileBuilder()
          .withEmail(formData.email)
          .withLoginType(profile?.loginType)
          .withFirstName(formData.firstName)
          .withLastName(formData.lastName)
          .withPhoneNumber(profile.phoneNumber)
          .withBirthDate(formData.birthDate)
          .withGender(formData.gender)
          .withPhotoURL(profile.photoURL)
          .withWeight(formData.weight)
          .withHeight(formData.height)
          .withAddress(formData.address)
          .withCity(formData.city)
          .withCountry(formData.country)
          .build();
        updateProfile(newProfile);
        setFormError({
          ...formError,
          isErrorFirstName: null,
          isErrorLastName: null,
          isErrorBirthDate: null,
        });
      } else if (formData.firstName === '' || formData.lastName === '')
        util.alert(I18n.getTranslation().account.profile.error_validate_name);
      else if (formError.isErrorBirthDate === false)
        util.alert(I18n.getTranslation().account.profile.error_validate_form);
    }
    else {
      navigation.goBack();
    }
  };

  const hasModification = () => {
    return (
      profile.firstName !== formData.firstName ||
      profile.lastName !== formData.lastName ||
      profile.gender !== formData.gender ||
      profile.birthDate !== formData.birthDate ||
      profile.email !== formData.email ||
      profile.weight !== formData.weight ||
      profile.height !== formData.height ||
      profile.address !== formData.address ||
      profile.city !== formData.city ||
      profile.country !== formData.country
    );
  };

  const isValidForm = () =>
    formError.isErrorFirstName === false &&
    formError.isErrorLastName === false &&
    formError.isErrorBirthDate !== true &&
    formError.isErrorEmail === false;

  const validateForm = () => {

    const newFormError = {
      isErrorFirstName: FormValidation.validation('name', formData.firstName) ? false : true,
      isErrorLastName: FormValidation.validation('name', formData.lastName) ? false : true,
      isErrorBirthDate: formData.birthDate
        ? FormValidation.validation('birthday', formData.birthDate) ? false : true
        : null,
      isErrorEmail: formData.email
        ? FormValidation.validation('email', formData.email) ? false : true
        : null,
    };

    setFormError(prevFromError => ({ ...prevFromError, ...newFormError }));

    // Check if the form is valid (all fields are false)
    return Object.values(newFormError).every(value => value === false);
  };

  const resizeImage = async (uri: string): Promise<string> => {
    const pathFile = uri.substring(5, uri.lastIndexOf('/') + 1);
    const data = await ImageResizer.createResizedImage(
      uri,
      1000,
      1000,
      'JPEG',
      90,
      0,
      pathFile,
    );
    return data.uri;
  };

  return (
    <View style={styles.container}>
      {profile && (
        <KeyboardAwareScrollView style={styles.content}>
          <View style={styles.header}>
            <ProfileImage
              size={100}
              imageURI={formData.avatar}
              onPictureTaken={setProfilePhoto}
            />
            <Text style={styles.title}>
              {formData.firstName} {formData.lastName}
            </Text>
            <Text style={styles.phoneNumber}>
              {isEmailLogin ? profile.email : profile.phoneNumber}
            </Text>
          </View>
          <View>
            <DefaultButton
                label={t('Gérer mes licences' ,'Manage my licenses')}
                onPress={() => navigation.navigate('insurance_history')}
            />
          </View>
          {user && user.status === 'PENDING_REMOVAL' && (
            <DefaultButton
              label={t('Annuler la suppression du compte' ,'Cancel account deletion')}
              onPress={() => cancelDelete()}
            />
          )}
          {!isEmailLogin && (
            <InputWithLabel
              placeholder={
                I18n.getTranslation().account.profile.email_placeholder
              }
              label={I18n.getTranslation().account.profile.email}
              value={formData.email}
              style={{ marginTop: 12 }}
              autoCapitalize={'none'}
              keyboardType={'email-address'}
              error={formError.isErrorEmail}
              onChange={email => handleUpdateState('formData', 'email', email, setFormData)}
            />
          )}
          {isEmailLogin && (
            <PhoneInputField
              placeholder={I18n.getTranslation().account.profile.phone_number}
              value={profile.phoneNumber}
              style={{ marginTop: 20 }}
              color={new RGBAColor(0, 0, 0, 0.6) as any}
              disable={false}
              backgroundDisable={new RGBAColor(254, 148, 62, 0.4) as any}
            />
          )}
          <InputWithLabel
            placeholder={
              I18n.getTranslation().account.profile.first_name_placeholder
            }
            label={I18n.getTranslation().account.profile.first_name}
            value={formData.firstName}
            style={{ marginTop: 12 }}
            error={formError.isErrorFirstName}
            onChange={firstName =>
              handleUpdateState('formData', 'firstName', firstName, setFormData)
            }
          />
          <InputWithLabel
            placeholder={
              I18n.getTranslation().account.profile.last_name_placeholder
            }
            label={I18n.getTranslation().account.profile.last_name}
            value={formData.lastName}
            style={{ marginTop: 12 }}
            error={formError.isErrorLastName}
            onChange={lastName =>
              handleUpdateState('formData', 'lastName', lastName, setFormData)
            }
          />
          <DatePickerContainer
            placeholder={I18n.getTranslation().account.profile.birthdate}
            mode={'date'}
            style={{ marginTop: 20 }}
            color={new RGBAColor(0, 0, 0, 1) as any}
            value={formData.birthDate}
            onBlur={() =>
              handleUpdateState(
                'formError',
                'isErrorBirthDate',
                FormValidation.validation('birthday', formData.birthDate),
                setFormError
              )
            }
            onChange={birthDate => {
              handleUpdateState('formData', 'birthDate', birthDate, setFormData);
              handleUpdateState(
                'formError',
                'isErrorBirthDate',
                !FormValidation.validation('birthday', formData.birthDate),
                setFormError
              );
            }}
            error={formError.isErrorBirthDate}
            errorMsg={I18n.getTranslation().account.signup.msg_birthday_error}
          />
          <View style={styles.row}>
            <View style={styles.colLeft}>
              <InputWithLabel
                placeholder={'0'}
                label={I18n.getTranslation().account.profile.weight}
                value={formData.weight ?? ''}
                style={{ marginTop: 12 }}
                keyboardType={'numeric'}
                error={formError.isErrorWeight}
                onChange={weight =>
                  handleUpdateState('formData', 'weight', weight, setFormData)
                }
              />
            </View>
            <View style={styles.colRight}>
              <InputWithLabel
                placeholder={'0'}
                label={I18n.getTranslation().account.profile.height}
                value={formData.height ?? ''}
                style={{ marginTop: 12 }}
                keyboardType={'numeric'}
                error={formError.isErrorHeight}
                onChange={height =>
                  handleUpdateState('formData', 'height', height, setFormData)
                }
              />
            </View>
          </View>
          <ChoicePickerContainer
            placeholder={I18n.getTranslation().account.profile.gender}
            options={[
              {
                value: 'female',
                label: I18n.getTranslation().account.profile.femme,
              },
              {
                value: 'male',
                label: I18n.getTranslation().account.profile.homme,
              },
              {
                value: 'neutral',
                label: I18n.getTranslation().account.profile.neutre,
              },
            ]}
            value={formData.gender}
            style={styles.choicePicker}
            onChange={gender =>
              handleUpdateState('formData', 'gender', gender.value, setFormData)
            }
          />
          <InputWithLabel
            placeholder={
              I18n.getTranslation().account.profile.address_placeholder
            }
            label={I18n.getTranslation().account.profile.address}
            value={formData.address}
            style={{ marginTop: 12 }}
            onChange={address =>
              handleUpdateState('formData', 'address', address, setFormData)
            }
          />
          <InputWithLabel
            placeholder={
              I18n.getTranslation().account.profile.city_placeholder
            }
            label={I18n.getTranslation().account.profile.city}
            value={formData.city}
            style={{ marginTop: 12 }}
            onChange={city => handleUpdateState('formData', 'city', city, setFormData)}
          />
          <ChoicePickerContainer
            placeholder={I18n.getTranslation().account.profile.country}
            options={getCountryList()}
            value={formData.country}
            style={styles.choicePicker}
            onChange={country =>
              handleUpdateState('formData', 'country', country.value, setFormData)
            }
          />
          <View style={styles.button}>
            <DefaultButton
              label={I18n.getTranslation().account.profile.update_changes}
              onPress={editProfile}
            />
          </View>
        </KeyboardAwareScrollView>
      )}
      {loading}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 15,
  },
  header: {
    height: 150,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 15,
    zIndex: 1,
    marginBottom: 25,
  },
  title: {
    paddingTop: 12,
    fontSize: 18,
    color: 'black',
    fontFamily: 'U8 Bold',
  },
  button: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  phoneNumber: {
    fontSize: 15,
    fontFamily: 'U8 Regular',
    color: Theme.dimGray,
    paddingTop: 8,
    letterSpacing: 1,
  },
  row: {
    flexDirection: 'row',
  },
  colLeft: {
    paddingRight: 5,
    flex: 1,
  },
  colRight: {
    paddingLeft: 5,
    flex: 1,
  },
  choicePicker: {
    marginTop: 12,
    padding: 5,
    height: 55,
  },
});
