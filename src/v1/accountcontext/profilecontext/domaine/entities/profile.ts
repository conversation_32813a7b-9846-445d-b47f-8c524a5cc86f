export class Profile {
  constructor(
    private _phoneNumber: string,
    private _firstName: string,
    private _lastName: string,
    private _birthDate: Date,
    private _photoURL: string,
    private _gender?: string,
    private _email?: string,
    private _weight?: string,
    private _height?: string,
    private _address?: string,
    private _city?: string,
    private _country?: string,
    private _loginType?: string,
  ) {}

  fullName(): string {
    return this._firstName + ' ' + this._lastName;
  }

  get phoneNumber(): string {
    return this._phoneNumber;
  }

  get firstName(): string {
    return this._firstName;
  }

  get lastName(): string {
    return this._lastName;
  }

  get birthDate(): Date {
    return this._birthDate;
  }

  get photoURL(): string {
    return this._photoURL;
  }

  get gender(): string {
    return this._gender;
  }

  get email(): string {
    return this._email;
  }

  get weight(): string {
    return this._weight;
  }

  get height(): string {
    return this._height;
  }

  get address(): string {
    return this._address;
  }

  get city(): string {
    return this._city;
  }

  get country(): string {
    return this._country;
  }

  get loginType(): string {
    return this._loginType;
  }

  public set photoURL(v: string) {
    this._photoURL = v;
  }
}
