import {Profile} from './profile';

export class ProfileBuilder {
  protected _phoneNumber: string;
  protected _firstName: string;
  protected _lastName: string;
  protected _birthDate: Date;
  protected _photoURL: string = '';
  protected _gender?: string;
  protected _email?: string;
  protected _weight?: string;
  protected _height?: string;
  protected _address?: string;
  protected _city?: string;
  protected _country?: string;
  protected _loginType?: string;

  fromProfile(profile: Profile): ProfileBuilder {
    this._phoneNumber = profile.phoneNumber;
    this._firstName = profile.firstName;
    this._lastName = profile.lastName;
    this._birthDate = profile.birthDate;
    this._photoURL = profile.photoURL;
    this._email = profile.email;
    this._gender = profile.gender;
    this._address = profile.address;
    this._city = profile.city;
    this._country = profile.country;
    this._weight = profile.weight;
    this._height = profile.height;
    this._loginType = profile.loginType;
    return this;
  }

  withPhoneNumber(value: string): ProfileBuilder {
    this._phoneNumber = value;
    return this;
  }

  withFirstName(value: string): ProfileBuilder {
    this._firstName = value;
    return this;
  }

  withLastName(value: string): ProfileBuilder {
    this._lastName = value;
    return this;
  }

  withBirthDate(value: Date): ProfileBuilder {
    this._birthDate = value;
    return this;
  }

  withPhotoURL(value: string): ProfileBuilder {
    this._photoURL = value;
    return this;
  }

  withGender(value: string): ProfileBuilder {
    this._gender = value;
    return this;
  }

  withEmail(value: string): ProfileBuilder {
    this._email = value;
    return this;
  }

  withWeight(value: string): ProfileBuilder {
    this._weight = value;
    return this;
  }

  withHeight(value: string): ProfileBuilder {
    this._height = value;
    return this;
  }

  withAddress(value: string): ProfileBuilder {
    this._address = value;
    return this;
  }

  withCity(value: string): ProfileBuilder {
    this._city = value;
    return this;
  }

  withCountry(value: string): ProfileBuilder {
    this._country = value;
    return this;
  }

  withLoginType(value: string): ProfileBuilder {
    this._loginType = value;
    return this;
  }

  build(): Profile {
    return new Profile(
      this._phoneNumber,
      this._firstName,
      this._lastName,
      this._birthDate,
      this._photoURL,
      this._gender,
      this._email,
      this._weight,
      this._height,
      this._address,
      this._city,
      this._country,
      this._loginType,
    );
  }
}
