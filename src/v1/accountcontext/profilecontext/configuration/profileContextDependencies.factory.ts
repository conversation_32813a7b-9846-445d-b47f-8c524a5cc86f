import Config from 'react-native-config'
import { InMemoryProfileRepository } from '../../../common/adapters/secondaries/inmemory/InMemoryProfileRepository';
import { InMemoryProfileRemoteService } from '../adapters/secondaries/inmemory/inMemoryProfileRemoteService';
import { APIProfileService } from '../adapters/secondaries/real/APIProfileService';
import { SQLiteProfileRepository } from '../adapters/secondaries/real/profile/SQLiteProfileRepository';
import { ProfileRemoteService } from '../domaine/gateway/profileRemoteService';
import { ProfileRepository } from '../domaine/gateway/profileRepository';

export class ProfileContextDependenciesFactory {

    static profileRemoteService(): ProfileRemoteService {
        switch (Config.ENV) {
            case 'prod':
            case 'staging':
            case 'dev':
                return new APIProfileService()
            default:
                return new InMemoryProfileRemoteService()
        }
    }

    static profileRepository(): ProfileRepository {
        switch (Config.ENV) {
            case 'prod':
            case 'staging':
            case 'dev':
                return new SQLiteProfileRepository()
            default:
                return new InMemoryProfileRepository()
        }
    }
}
