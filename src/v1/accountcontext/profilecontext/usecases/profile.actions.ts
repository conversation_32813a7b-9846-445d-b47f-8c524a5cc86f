import { Profile } from '../domaine/entities/profile';

export const SET_PROFILE = 'SET_PROFILE'

export const EDIT_PROFILE = 'EDIT_PROFILE'
export const PROFILE_EDITION_ERROR = 'PROFILE_EDITION_ERROR'
export const PROFILE_EDITION_SUCCESS = 'PROFILE_EDITION_SUCCESS'
export const UPDATE_PROFILE_IMAGE_URL = 'UPDATE_PROFILE_IMAGE_URL'

export const UPLOAD_PROFILE_PHOTO = 'UPLOAD_PROFILE_PHOTO'
export const UPLOAD_PROFILE_PHOTO_SUCCESS = 'UPLOAD_PROFILE_PHOTO_SUCCESS'
export const UPLOAD_PROFILE_PHOTO_ERROR = 'UPLOAD_PROFILE_PHOTO_ERROR'

export const editProfile = (profile: Profile) => ({
    type   : EDIT_PROFILE,
    payload: { profile }
})

export const errorProfileEdition = (error: string) => ({
    type   : PROFILE_EDITION_ERROR,
    payload: error
})

export const successProfileEdition = () => ({
    type   : PROFILE_EDITION_SUCCESS
})

export const updateProfileImageURL = (photoURL: string) => ({
    type   : UPDATE_PROFILE_IMAGE_URL,
    payload: photoURL
})

export const uploadProfilePhoto = (uri: string, phoneNumber: string) => ({
    type   : UPLOAD_PROFILE_PHOTO,
    payload: { photoURL: uri, phoneNumber }
})

export const uploadProfilePhotoSuccess = () => ({
    type: UPLOAD_PROFILE_PHOTO_SUCCESS
})

export const uploadProfilePhotoError = (error: string) => ({
    type   : UPLOAD_PROFILE_PHOTO_ERROR,
    payload: error
})
export const setProfile = (profile: Profile) => ({
    type   : SET_PROFILE,
    payload: profile
})
