import { ofType } from 'redux-observable'
import { of } from 'rxjs/internal/observable/of'
import { flatMap, mergeMap, switchMap } from 'rxjs/internal/operators';
import { catchError } from 'rxjs/operators'
import { setApplicationUser, setAppUserAvatar } from '../../../common/usecases/applicationuser/applicationuser.actions';
import { ApplicationUserBuilder } from '../../../common/usecases/applicationuser/applicationuser.builder';
import { Profile } from '../domaine/entities/profile';
import { ProfileRemoteService } from '../domaine/gateway/profileRemoteService';
import { ProfileRepository } from '../domaine/gateway/profileRepository';
import {
    EDIT_PROFILE,
    errorProfileEdition,
    successProfileEdition,
    updateProfileImageURL,
    UPLOAD_PROFILE_PHOTO, uploadProfilePhotoError, uploadProfilePhotoSuccess
} from './profile.actions';

export const profileEditionEpic = (action$, store, { profileRemoteService, profileRepository }:
    { profileRemoteService: ProfileRemoteService, profileRepository: ProfileRepository }) =>
    action$.pipe(
        ofType(EDIT_PROFILE),
        switchMap<{ payload: { profile: Profile } }, any>(
            action => profileRemoteService.editRemoteProfile(action.payload.profile)
                .pipe(
                    mergeMap(() => profileRepository.updateLocalProfile(action.payload.profile)
                        .pipe(
                            flatMap(() => {
                                    const applicationUser = new ApplicationUserBuilder()
                                        .withAvatar(action.payload.profile.photoURL)
                                        .withFirstName(action.payload.profile.firstName)
                                        .withLastName(action.payload.profile.lastName)
                                        .withPhoneNumber(action.payload.profile.phoneNumber)
                                        .build()
                                    return [setApplicationUser(applicationUser), successProfileEdition()]
                                }
                            ), catchError(error => of(errorProfileEdition(error)))
                        )
                    ), catchError(error => of(errorProfileEdition(error)))
                )
        )
    )

export const profilePhotoEditionEpic = (action$, store, { profileRemoteService, profileRepository }:
    { profileRemoteService: ProfileRemoteService, profileRepository: ProfileRepository }) =>
    action$.pipe(
        ofType(UPLOAD_PROFILE_PHOTO),
        switchMap<{ payload: { photoURL: string, phoneNumber: string } }, any>(
            action => profileRemoteService.uploadRemotePhoto(action.payload.photoURL)
                .pipe(
                    mergeMap(
                        () => profileRepository.updateLocalPhotoURL(action.payload.photoURL, action.payload.phoneNumber)
                            .pipe(
                                flatMap(() => [
                                        updateProfileImageURL(action.payload.photoURL),
                                        setAppUserAvatar(action.payload.photoURL),
                                        uploadProfilePhotoSuccess()
                                    ]
                                ), catchError(error => of(uploadProfilePhotoError(error)))
                            )
                    ), catchError(error => of(uploadProfilePhotoError(error)))
                )
        )
    )
