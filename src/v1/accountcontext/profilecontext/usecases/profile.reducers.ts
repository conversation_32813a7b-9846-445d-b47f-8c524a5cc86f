import { ProfileState } from '../configuration/profile.state';
import { ProfileBuilder } from '../domaine/entities/profile.builder';
import {
    EDIT_PROFILE,
    PROFILE_EDITION_ERROR,
    PROFILE_EDITION_SUCCESS,
    SET_PROFILE,
    UPDATE_PROFILE_IMAGE_URL,
    UPLOAD_PROFILE_PHOTO,
    UPLOAD_PROFILE_PHOTO_ERROR,
    UPLOAD_PROFILE_PHOTO_SUCCESS
} from './profile.actions';

const initialProfileState: ProfileState = {
    isLoading: false,
    error    : undefined,
    success  : false,
    profile  : null,
    photoURL : null
}
export const profileReducer = (state = initialProfileState, action) => {
    switch (action.type) {
        case EDIT_PROFILE:
            return {
                ...state,
                isLoading: true,
                error    : undefined,
                profile  : action.payload.profile,
                success  : false
            }
        case PROFILE_EDITION_ERROR:
            return {
                ...state,
                isLoading: false,
                success  : false,
                error    : action.payload
            }
        case PROFILE_EDITION_SUCCESS:
            return {
                ...state,
                isLoading: false,
                success  : true,
                error: undefined
            }
        case UPDATE_PROFILE_IMAGE_URL:
            const profile = new ProfileBuilder().fromProfile(state.profile).build()
            return {
                ...state,
                profile,
                success: false,
                error  : undefined
            }
        case UPLOAD_PROFILE_PHOTO:
            return {
                ...state,
                isLoading: true,
                photoURL : action.payload.photoURL
            }
        case UPLOAD_PROFILE_PHOTO_ERROR:
            return {
                ...state,
                isLoading: false,
                error    : action.payload,
                success  : false
            }
        case UPLOAD_PROFILE_PHOTO_SUCCESS:
            return {
                ...state,
                isLoading: false,
                success  : true,
                error    : undefined
            }
        case SET_PROFILE:
            return {
                ...state,
                isLoading: false,
                success  : false,
                error    : undefined,
                profile  : action.payload
            }
        default:
            return state
    }
}
