import { AppState } from '../../../configuration/AppState'

export const profileLoadingSelector = (appState: AppState) => appState.account.profile.isLoading
export const profileErrorSelector = (appState: AppState) => appState.account.profile.error
export const profileSelector = (appState: AppState) => appState.account.profile.profile
export const profileSuccessSelector = (appState: AppState) => appState.account.profile.success
export const profileAvatarSelector = (appState: AppState) => appState.account.profile.photoURL
