import React, {PureComponent} from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import {
  HeaderButtonIcon,
  HeaderTitle,
} from '../../../../common/adapters/primaries/navigation/navigationHeader';
import {I18n} from '../../../../configuration/i18n/i18n';
import {Theme} from '../../../../configuration/theme/app.theme';

interface StoreInfo {
  id: number;
  name: string;
}

export class PreferencesContainer extends PureComponent<any, any> {
  static navigationOptions = ({navigation}) => {
    return {
      headerTitle: (
        <HeaderTitle
          title={I18n.getTranslation().account.preferences.my_preferences}
        />
      ),
      headerRight: <View />,
      headerLeft: (
        <HeaderButtonIcon
          CTA={() => navigation.navigate('main')}
          iconName={'angle-left'}
        />
      ),
      headerStyle: {
        backgroundColor: Theme.blue,
        borderBottomColor: Theme.blue,
        height: 40,
      },
    };
  };

  render() {
    const storeListFollows: StoreInfo[] = [
      {id: 1, name: 'Store Name'},
      {id: 2, name: 'Store Name'},
      {id: 3, name: 'Store Name'},
      {id: 4, name: 'Store Name'},
      {id: 5, name: 'Store Name'},
      {id: 6, name: 'Store Name'},
    ];
    return (
      <View style={styles.container}>
        <FlatList
          data={storeListFollows}
          renderItem={data => this.renderItem(data.item)}
        />
      </View>
    );
  }

  renderItem(item) {
    return (
      <View style={styles.content}>
        <Text>---------------</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
  },
  content: {
    paddingVertical: 10,
    borderBottomColor: Theme.gainsboro,
    borderBottomWidth: 1,
    width: '90%',
    backgroundColor: 'white',
    alignSelf: 'center',
  },
});
