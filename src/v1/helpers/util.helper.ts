import {Platform} from 'react-native';
import Config from 'react-native-config';

const checkEnv = (env: string) => {
  return Config.ENV === env;
};

const isDev = () => {
  return checkEnv('dev');
};

const isError = (level: string) => {
  return level === 'error';
};

const isWarm = (level: string) => {
  return level === 'warm';
};
const isInfo = (level: string) => {
  return level === 'info';
};
const checkPlatform = (
  platform: 'ios' | 'android' | 'windows' | 'macos' | 'web',
) => {
  return Platform.OS === platform;
};

const isAndroid = () => {
  return checkPlatform('android');
};

const isIOS = () => {
  return checkPlatform('ios');
};

const toString = (data: any) => {
  return typeof data === 'string' ? data : JSON.stringify(data);
};

const log = (data: any, level = 'error') => {
  if (isDev()) {
    if (isError(level)) {
      console.error(toString(data));
    } else if (isWarm(level)) {
      console.warn(toString(data));
    } else if (isInfo(level)) {
      console.info(toString(data));
    } else {
      console.log(toString(data));
    }
  }
};

export const util = {checkEnv, isDev, log, isAndroid, isIOS, toString};
