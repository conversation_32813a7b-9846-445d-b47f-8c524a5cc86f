import axios, {AxiosInstance, AxiosResponse} from 'axios';
import Config from 'react-native-config';
import {EventRegister} from 'react-native-event-listeners';
import {API_ERROR, JWT_TOKEN_KEY, USER_DISABLED} from './constant.helper';
import {storage} from './storage.helper';
import {util} from './util.helper';

const baseURL = Config.API_URL;

export const http = {
  get,
  post,
  put,
  remove,
  patch,
  getInstance,
};

async function getInstance(multipart?: string): Promise<AxiosInstance> {
  return new Promise<AxiosInstance>(async (resolve, reject) => {
    const headers = await getHeaders(multipart);
    resolve(
      axios.create({
        timeout: 60000,
        headers: headers,
        baseURL,
      }),
    );
  });
}

async function getHeaders(multipart?: string) {
  const headers = {
    Accept: 'application/json',
    'Content-Type': multipart || 'application/json',
  };
  const token = await storage.get(JWT_TOKEN_KEY);

  if (token) {
    Object.assign(headers, {
      Authorization: `${token.replace(/['"]+/g, '')}`,
    });
  }

  return headers;
}

async function get(path: string): Promise<any> {
  const axiosInstance = await getInstance();
  return axiosInstance
    .get(path)
    .then((res: any) => handleResponse(res))
    .catch((err: any) => handleError(err));
}

async function post(path: string, data?: any, multipart?: any): Promise<any> {
  const axiosInstance = await getInstance(multipart);

  return axiosInstance
    .post(path, data)
    .then((res: any) => handleResponse(res))
    .catch((err: any) => handleError(err));
}

async function put(path: string, data?: any): Promise<any> {
  const axiosInstance = await getInstance();
  return axiosInstance
    .put(path, data)
    .then((res: any) => handleResponse(res))
    .catch((err: any) => handleError(err));
}

async function patch(path: string, data?: any): Promise<any> {
  const axiosInstance = await getInstance();
  return axiosInstance
    .patch(path, data)
    .then((res: any) => handleResponse(res))
    .catch((err: any) => handleError(err));
}

async function remove(path: string, data?: any): Promise<any> {
  const axiosInstance = await getInstance();
  return axiosInstance
    .delete(path, data)
    .then((res: any) => handleResponse(res))
    .catch((err: any) => handleError(err));
}

function handleResponse(res: AxiosResponse) {
  const data = util.toString(res.data);
  util.log(
    'Request response data ==> ' +
      (data && data.length < 100 ? data : data.substring(0, 100)),
    'info',
  );
  return res.data;
}

function handleError(err: any) {
  let data = null;
  let status = 500;
  let message =
    "Une érreur est survenu lors de l'opération. Merci de reéssayer !";
  if (util.isDev()) {
    util.log('<== Request Error ==> ');
    util.log(JSON.stringify(err));
    if (err && err.response) {
      util.log(err.response);
      util.log(err.response.data);
      util.log(err.response.status);
      util.log(err.response.config);
      data = err.response.data;
      status = err.response.status;
      if (
        err &&
        err.response &&
        err.response.data &&
        err.response.data.message
      ) {
        message = err.response.data.message;
      }
    } else if (err && err.request) {
      util.log(err.request);
    } else {
      util.log(err.message);
    }
    util.log('<== End request Error ==> ');
    util.log('==> status ' + status);
    EventRegister.emit(API_ERROR, message);
  }

  return {success: false, status, data, message};
}
