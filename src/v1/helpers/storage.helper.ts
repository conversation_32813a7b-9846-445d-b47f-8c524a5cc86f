import AsyncStorage from "@react-native-community/async-storage";
import { util } from "./util.helper";

const set = async (key: string, value: any) => {
  try {
    const jsonValue = typeof value === "string" ? value : JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (e) {
    util.log(e);
  }
};

const get = async (key: string) => {
  try {
    const value = await AsyncStorage.getItem(key);
    try {
      return JSON.parse(value);
    } catch (error) {}
    return value;
  } catch (e) {
    util.log(e);
  }
};

export const storage = { set, get };
