# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx12288m -XX:MaxMetaspaceSize=3072m -Xms3g -Xss8m -XX:+UseG1GC

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# Suppress unsupported compileSdk warning
android.suppressUnsupportedCompileSdk=34

# Additional Gradle optimizations
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.caching=true
org.gradle.workers.max=2
kotlin.incremental=false

# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.125.0

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

BUILD_VERSION_CODE=203003
BUILD_VERSION_NAME=2.3.1
PLAY_TRACK=alpha


MYAPP_RELEASE_STORE_FILE=kamersport.keystore
MYAPP_RELEASE_KEY_ALIAS=kamersport
MYAPP_RELEASE_STORE_PASSWORD=123Diane
MYAPP_RELEASE_KEY_PASSWORD=123Diane

# keytool -genkeypair -v -keystore kamersport.keystore -alias kamersport -keyalg RSA -keysize 2048 -validity 10000