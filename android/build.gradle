// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"
        kotlinVersion = '1.8.0'
        playServicesLocationVersion = "21.0.1"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.google.gms:google-services:4.3.15')
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
  repositories {
    maven {
      // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
      url("$rootDir/../node_modules/react-native/android")
    }
    maven {
      // Android JSC is installed from npm
      url("$rootDir/../node_modules/jsc-android/dist")
    }
    google()
    jcenter()
    maven { url 'https://www.jitpack.io' }
    configurations.all {
      resolutionStrategy.eachDependency { details ->
        def requested = details.requested
        if (requested.group == 'androidx.core' && requested.name == 'core') {
          details.useVersion '1.10.1'
        }
      }
    }
  }
}
