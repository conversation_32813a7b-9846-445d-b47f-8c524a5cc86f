diff --git a/node_modules/@tootsweet/react-native-gps-state/android/build.gradle b/node_modules/@tootsweet/react-native-gps-state/android/build.gradle
index 4f5168a..7a55a48 100644
--- a/node_modules/@tootsweet/react-native-gps-state/android/build.gradle
+++ b/node_modules/@tootsweet/react-native-gps-state/android/build.gradle
@@ -10,7 +10,8 @@ def computeVersionName() {

 buildscript {
     repositories {
-        jcenter()
+        google()
+        mavenCentral()
     }

     dependencies {
-        classpath 'com.android.tools.build:gradle:2.2.3'
+        classpath 'com.android.tools.build:gradle:7.3.1'
     }
 }

@@ -40,7 +41,8 @@ android {
 }

 repositories {
-    mavenCentral()
+    google()
+    mavenCentral()
     maven {
         url "$projectDir/../../react-native/android"
     }
@@ -46,8 +49,8 @@ repositories {
 }

 dependencies {
-    compile "com.facebook.react:react-native:+"  // From node_modules
-    compile 'com.android.support:appcompat-v7:26.1.0'
+    implementation "com.facebook.react:react-native:+"  // From node_modules
+    implementation 'com.android.support:appcompat-v7:26.1.0'
     testImplementation 'junit:junit:4.12'
     /*androidTestCompile('com.android.support.test.espresso:espresso-core:2.2.2', {
         exclude group: 'com.android.support', module: 'support-annotations'
